﻿{
  "name": "com.stratkit.production-core",
  "version": "1.3.0",
  "displayName": "Stratkit Production Core",
  "unity": "2022.2",
  "unityRelease": "17f1",
  "description": "Common code for things like units production, building construction etc",
  "dependencies": {
    "com.unity.entities": "1.0.16",
    "com.stratkit.provinces": "1.1.1",
    "com.stratkit.server-communication": "1.6.1",
    "com.stratkit.game-info-state-loader": "0.3.1",
    "com.unity.collections": "2.1.4",
    "com.unity.mathematics": "1.2.6"
  },
  "publishConfig": {
    "registry": "https://npm.pkg.github.com/@bytro"
  },
  "author": {
    "name": "By<PERSON>",
    "email": "<EMAIL>",
    "url": "https://www.bytro.com"
  }
}
