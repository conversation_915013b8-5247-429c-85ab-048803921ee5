{"name": "Stratkit.Production", "rootNamespace": "Stratkit.Production", "references": ["Stratkit.Data.Provinces", "Stratkit.GameInfoStateLoader", "Stratkit.ServerCommunication", "Unity.Collections", "Unity.Entities", "Unity.Mathematics", "Stratkit.Map"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}