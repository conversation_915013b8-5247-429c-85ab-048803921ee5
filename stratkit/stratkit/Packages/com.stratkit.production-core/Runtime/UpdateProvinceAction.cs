﻿using Stratkit.Map;
using Stratkit.ServerCommunication;
using System;
using System.Runtime.Serialization;
using Unity.Entities;
using Location = Stratkit.Data.Provinces.Location;

namespace Stratkit.Production {
    /// <summary>
    /// Action to issue upgrades in a province
    /// </summary>
    public sealed class UpdateProvinceAction : ServerCommand {
        [NonSerialized, IgnoreDataMember]
        public const string TypeIdentifier = "ultshared.action.UltUpdateProvinceAction";

        [SerializableName("provinceIDs", type: Location.TypeIdentifier)]
        public Entity[] Provinces = null!;

        [SerializableName("mode")]
        public int Mode = 2;

        [SerializableName("upgrade")]
        public UpgradeType? Upgrade;

        [SerializableName("deploymentTarget")]
        public MapCoordinates? DeploymentTarget;
    }
}
