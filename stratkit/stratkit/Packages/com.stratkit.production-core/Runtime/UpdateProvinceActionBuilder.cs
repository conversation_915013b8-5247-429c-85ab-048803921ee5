using Stratkit.Map;
using Stratkit.ServerCommunication;
using System.Collections.Generic;
using Unity.Entities;

namespace Stratkit.Production {
    /// <summary>
    /// Builder which builds an <see cref="UpdateProvinceAction"/>.
    /// </summary>
    public sealed class UpdateProvinceActionBuilder : IBuilder<UpdateProvinceAction> {
        private readonly UpdateProvinceMode _mode;
        private readonly List<Entity> _provinces;
        private UpgradeType? _upgrade;
        private MapCoordinates? _deploymentTarget;

        public UpdateProvinceActionBuilder(UpdateProvinceMode updateMode, Entity province) { // Minor performance improvement, given that most commands will be issued for just one province
            _mode = updateMode;
            _provinces = new List<Entity>(1) { province };
        }

        public UpdateProvinceActionBuilder(UpdateProvinceMode updateMode, List<Entity> provinces) {
            _mode = updateMode;
            _provinces = provinces;
        }

        public UpdateProvinceActionBuilder(UpdateProvinceMode updateMode, params Entity[] provinces) {
            _mode = updateMode;
            _provinces = new List<Entity>(provinces);
        }

        public UpdateProvinceActionBuilder SetUpgradeType(UpgradeType upgradeType) {
            _upgrade = upgradeType;
            return this;
        }

        public UpdateProvinceActionBuilder AddProvince(Entity provinceEntity) {
            _provinces.Add(provinceEntity);
            return this;
        }

        public UpdateProvinceActionBuilder SetDeploymentTarget(MapCoordinates? deploymentTarget) {
            _deploymentTarget = deploymentTarget;
            return this;
        }

        public UpdateProvinceAction Build() {
            return new UpdateProvinceAction {
                Type = UpdateProvinceAction.TypeIdentifier,
                Provinces = _provinces.ToArray(),
                Mode = (int)_mode,
                Upgrade = _upgrade,
                DeploymentTarget = _deploymentTarget,
            };
        }
    }
}
