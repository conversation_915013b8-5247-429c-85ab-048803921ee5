using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AdditionalProductionContent {
        public IReadOnlyDictionary<float, float> BuildTimeFactor => @buildTimeFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @buildTimeFactor = new();

        public IReadOnlyDictionary<float, float> CostFactor => @costFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @costFactor = new();

        public IReadOnlyDictionary<ContentItemId, int> MaxAmount => @maxAmount;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @maxAmount = new();

        [UnityEngine.Scripting.Preserve]
        public AdditionalProductionContent() { }
    }
}
