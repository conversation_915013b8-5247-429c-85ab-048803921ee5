using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class SplitStrategyContent {
        public SplitStrategy Strategy => @strategy;
        [JsonProperty, CreateProperty]
        internal SplitStrategy @strategy;

        [UnityEngine.Scripting.Preserve]
        public SplitStrategyContent() { }
    }
}
