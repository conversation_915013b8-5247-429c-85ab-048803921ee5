using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AttackTargetGoalContent {
        public int NumberOfAttacks => @numberOfAttacks;
        [JsonProperty, CreateProperty]
        internal int @numberOfAttacks;

        [UnityEngine.Scripting.Preserve]
        public AttackTargetGoalContent() { }
    }
}
