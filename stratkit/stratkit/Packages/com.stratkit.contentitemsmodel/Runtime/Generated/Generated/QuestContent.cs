using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class QuestContent {
        public int DaysBeforeFirstQuest => @daysBeforeFirstQuest;
        [JsonProperty, CreateProperty]
        internal int @daysBeforeFirstQuest;

        public int MaxInactiveDaysSpawningCrates => @maxInactiveDaysSpawningCrates;
        [JsonProperty, CreateProperty]
        internal int @maxInactiveDaysSpawningCrates;

        public int MaxQuestSpawnInterval => @maxQuestSpawnInterval;
        [JsonProperty, CreateProperty]
        internal int @maxQuestSpawnInterval;

        public int MaxTickets => @maxTickets;
        [JsonProperty, CreateProperty]
        internal int @maxTickets;

        public int MinQuestSpawnInterval => @minQuestSpawnInterval;
        [JsonProperty, CreateProperty]
        internal int @minQuestSpawnInterval;

        public int NumberOfPlansAwarded => @numberOfPlansAwarded;
        [JsonProperty, CreateProperty]
        internal int @numberOfPlansAwarded;

        public int NumberOfResourcesAwarded => @numberOfResourcesAwarded;
        [JsonProperty, CreateProperty]
        internal int @numberOfResourcesAwarded;

        public IReadOnlyDictionary<ContentItemId, PlansContent> Plans => @plans;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, PlansContent> @plans = new();

        public ProvinceSelectorContent? ProvinceSelector => @provinceSelector;
        [JsonProperty, CreateProperty]
        internal ProvinceSelectorContent? @provinceSelector;

        public IReadOnlyDictionary<RewardType, int> QuestRewardDropChances => @questRewardDropChances;
        [JsonProperty, CreateProperty]
        internal Dictionary<RewardType, int> @questRewardDropChances = new();

        public int QuestTimeLimit => @questTimeLimit;
        [JsonProperty, CreateProperty]
        internal int @questTimeLimit;

        public IReadOnlyDictionary<Rarity, RarityIntervalContent> ResourceIntervals => @resourceIntervals;
        [JsonProperty, CreateProperty]
        internal Dictionary<Rarity, RarityIntervalContent> @resourceIntervals = new();

        public IReadOnlyDictionary<ResourceId, Rarity> Resources => @resources;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, Rarity> @resources = new();

        public ContentItemId TicketContentID => @ticketContentID;
        [JsonProperty, CreateProperty]
        internal ContentItemId @ticketContentID;

        [UnityEngine.Scripting.Preserve]
        public QuestContent() { }
    }
}
