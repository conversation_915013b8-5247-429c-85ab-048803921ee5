using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AdviserRewardsConfig {
        public IReadOnlyDictionary<string, AdviserReward> AdviseIDToRewardMap => @adviseIDToRewardMap;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, AdviserReward> @adviseIDToRewardMap = new();

        public ContentItemId RewardTrackerInventoryItemID => @rewardTrackerInventoryItemID;
        [JsonProperty, CreateProperty]
        internal ContentItemId @rewardTrackerInventoryItemID;

        [UnityEngine.Scripting.Preserve]
        public AdviserRewardsConfig() { }
    }
}
