using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ProvinceEffectContent {
        public IReadOnlyDictionary<ResourceId, float> ProductionFactor => @productionFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @productionFactor = new();

        public StackStrategy? StackStrategy => @stackStrategy;
        [JsonProperty, CreateProperty]
        internal StackStrategy? @stackStrategy;

        [UnityEngine.Scripting.Preserve]
        public ProvinceEffectContent() { }
    }
}
