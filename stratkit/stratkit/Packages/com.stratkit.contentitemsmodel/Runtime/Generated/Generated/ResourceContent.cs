using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ResourceContent {
        public float? Capacity => @capacity;
        [JsonProperty, CreateProperty]
        internal float? @capacity;

        public int Category => @category;
        [JsonProperty, CreateProperty]
        internal int @category;

        public float PremiumAccountBaseProductionBonus => @premiumAccountBaseProductionBonus;
        [JsonProperty, CreateProperty]
        internal float @premiumAccountBaseProductionBonus;

        public float PremiumAccountProductionBonus => @premiumAccountProductionBonus;
        [JsonProperty, CreateProperty]
        internal float @premiumAccountProductionBonus;

        public float? PremiumPriceFactor => @premiumPriceFactor;
        [JsonProperty, CreateProperty]
        internal float? @premiumPriceFactor;

        public float ProductionFactor => @productionFactor;
        [JsonProperty, CreateProperty]
        internal float @productionFactor;

        public int StartAmount => @startAmount;
        [JsonProperty, CreateProperty]
        internal int @startAmount;

        [UnityEngine.Scripting.Preserve]
        public ResourceContent() { }
    }
}
