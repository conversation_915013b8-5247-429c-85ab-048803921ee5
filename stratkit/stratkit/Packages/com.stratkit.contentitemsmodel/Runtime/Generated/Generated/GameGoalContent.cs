using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class GameGoalContent : IContent {
        public AttackTargetGoalContent? AttackTarget => @attackTarget;
        [JsonProperty, CreateProperty]
        internal AttackTargetGoalContent? @attackTarget;

        public CompleteResearchesGoalContent? CompleteResearches => @completeResearches;
        [JsonProperty, CreateProperty]
        internal CompleteResearchesGoalContent? @completeResearches;

        public CompleteSpecificResearchesGoalContent? CompleteSpecificResearches => @completeSpecificResearches;
        [JsonProperty, CreateProperty]
        internal CompleteSpecificResearchesGoalContent? @completeSpecificResearches;

        public ConquerProvincesGoalContent? ConquerProvinces => @conquerProvinces;
        [JsonProperty, CreateProperty]
        internal ConquerProvincesGoalContent? @conquerProvinces;

        public ConstructSpecificUpgradesGoalContent? ConstructSpecificUpgrades => @constructSpecificUpgrades;
        [JsonProperty, CreateProperty]
        internal ConstructSpecificUpgradesGoalContent? @constructSpecificUpgrades;

        public ConstructUpgradesGoalContent? ConstructUpgrades => @constructUpgrades;
        [JsonProperty, CreateProperty]
        internal ConstructUpgradesGoalContent? @constructUpgrades;

        public DefeatSpecificUnitsGoalContent? DefeatSpecificUnits => @defeatSpecificUnits;
        [JsonProperty, CreateProperty]
        internal DefeatSpecificUnitsGoalContent? @defeatSpecificUnits;

        public DefeatUnitsGoalContent? DefeatUnits => @defeatUnits;
        [JsonProperty, CreateProperty]
        internal DefeatUnitsGoalContent? @defeatUnits;

        public EarnVictoryPointsGoalContent? EarnVictoryPoints => @earnVictoryPoints;
        [JsonProperty, CreateProperty]
        internal EarnVictoryPointsGoalContent? @earnVictoryPoints;

        public bool FactionGoal => @factionGoal;
        [JsonProperty, CreateProperty]
        internal bool @factionGoal;

        public MoveArmiesGoalContent? MoveArmies => @moveArmies;
        [JsonProperty, CreateProperty]
        internal MoveArmiesGoalContent? @moveArmies;

        public OwnProvincesGoalContent? OwnProvinces => @ownProvinces;
        [JsonProperty, CreateProperty]
        internal OwnProvincesGoalContent? @ownProvinces;

        public OwnSpecificProvincesGoalContent? OwnSpecificProvinces => @ownSpecificProvinces;
        [JsonProperty, CreateProperty]
        internal OwnSpecificProvincesGoalContent? @ownSpecificProvinces;

        public IReadOnlyDictionary<ContentItemId, float> PremiumRewards => @premiumRewards;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @premiumRewards = new();

        public ProduceSpecificUnitsGoalContent? ProduceSpecificUnits => @produceSpecificUnits;
        [JsonProperty, CreateProperty]
        internal ProduceSpecificUnitsGoalContent? @produceSpecificUnits;

        public ProduceUnitsGoalContent? ProduceUnits => @produceUnits;
        [JsonProperty, CreateProperty]
        internal ProduceUnitsGoalContent? @produceUnits;

        public ReduceMoraleGoalContent? ReduceMorale => @reduceMorale;
        [JsonProperty, CreateProperty]
        internal ReduceMoraleGoalContent? @reduceMorale;

        public IReadOnlyDictionary<ResourceId, float> ResourceRewards => @resourceRewards;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @resourceRewards = new();

        public SplitArmiesGoalContent? SplitArmies => @splitArmies;
        [JsonProperty, CreateProperty]
        internal SplitArmiesGoalContent? @splitArmies;

        [UnityEngine.Scripting.Preserve]
        public GameGoalContent() { }
    }
}
