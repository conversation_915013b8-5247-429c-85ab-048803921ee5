using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AutoRecruitContent {
        public ContentItemId AutoRecruitUnitId => @autoRecruitUnitId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @autoRecruitUnitId;

        public IReadOnlyDictionary<float, float> MoraleBasedAutoRecruitmentTime => @moraleBasedAutoRecruitmentTime;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedAutoRecruitmentTime = new();

        public IReadOnlyDictionary<ResourceId, float> ResourceFactors => @resourceFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @resourceFactors = new();

        [UnityEngine.Scripting.Preserve]
        public AutoRecruitContent() { }
    }
}
