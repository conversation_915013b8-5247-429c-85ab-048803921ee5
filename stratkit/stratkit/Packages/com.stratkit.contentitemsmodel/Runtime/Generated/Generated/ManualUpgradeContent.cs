using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ManualUpgradeContent {
        public float CostFactor => @costFactor;
        [JsonProperty, CreateProperty]
        internal float @costFactor;

        public float TimeFactor => @timeFactor;
        [JsonProperty, CreateProperty]
        internal float @timeFactor;

        [UnityEngine.Scripting.Preserve]
        public ManualUpgradeContent() { }
    }
}
