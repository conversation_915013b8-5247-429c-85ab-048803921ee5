using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class NewspaperContent {
        public bool PopularityReportsEnabled => @popularityReportsEnabled;
        [JsonProperty, CreateProperty]
        internal bool @popularityReportsEnabled;

        [UnityEngine.Scripting.Preserve]
        public NewspaperContent() { }
    }
}
