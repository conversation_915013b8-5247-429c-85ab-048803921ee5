using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class UpgradeContent : IContent {
        public float AiProductionFactor => @aiProductionFactor;
        [JsonProperty, CreateProperty]
        internal float @aiProductionFactor;

        public DamageType ArmorType => @armorType;
        [JsonProperty, CreateProperty]
        internal DamageType @armorType;

        public string ArticlePrefix => @articlePrefix;
        [JsonProperty, CreateProperty]
        internal string @articlePrefix = string.Empty;

        public int BuildCondition => @buildCondition;
        [JsonProperty, CreateProperty]
        internal int @buildCondition;

        public TimeSpan BuildTime => @buildTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @buildTime;

        public IReadOnlyDictionary<int, TimeSpan> BuildTimeFunction => @buildTimeFunction;
        [JsonProperty, CreateProperty]
        internal Dictionary<int, TimeSpan> @buildTimeFunction = new();

        public UpgradeCategory? Category => @category;
        [JsonProperty, CreateProperty]
        internal UpgradeCategory? @category;

        public IReadOnlyDictionary<ResourceId, float> Costs => @costs;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @costs = new();

        public IReadOnlyDictionary<ResourceId, float> DailyCosts => @dailyCosts;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @dailyCosts = new();

        public IReadOnlyDictionary<ResourceId, float> DailyProductions => @dailyProductions;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @dailyProductions = new();

        public int DayOfAvailability => @dayOfAvailability;
        [JsonProperty, CreateProperty]
        internal int @dayOfAvailability;

        public bool Enableable => @enableable;
        [JsonProperty, CreateProperty]
        internal bool @enableable;

        public IReadOnlyDictionary<UpgradeFeature, float> FeatureFunctions => @featureFunctions;
        [JsonProperty, CreateProperty]
        internal Dictionary<UpgradeFeature, float> @featureFunctions = new();

        public string FeatureIconPrefix => @featureIconPrefix;
        [JsonProperty, CreateProperty]
        internal string @featureIconPrefix = string.Empty;

        public IReadOnlyDictionary<UpgradeFeature, float> Features => @features;
        [JsonProperty, CreateProperty]
        internal Dictionary<UpgradeFeature, float> @features = new();

        public IReadOnlyList<TerrainType> ForbiddenTerrain => @forbiddenTerrain;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @forbiddenTerrain = new();

        public IReadOnlyList<TerrainType>? ForbiddenTerrainCore => @forbiddenTerrainCore;
        [JsonProperty, CreateProperty]
        internal List<TerrainType>? @forbiddenTerrainCore;

        public HarbourContent? Harbour => @harbour;
        [JsonProperty, CreateProperty]
        internal HarbourContent? @harbour;

        public HitBoxContent? HitBoxContent => @hitBoxContent;
        [JsonProperty, CreateProperty]
        internal HitBoxContent? @hitBoxContent;

        public int IconPosX => @iconPosX;
        [JsonProperty, CreateProperty]
        internal int @iconPosX;

        public int IconPosY => @iconPosY;
        [JsonProperty, CreateProperty]
        internal int @iconPosY;

        public string MapIcon => @mapIcon;
        [JsonProperty, CreateProperty]
        internal string @mapIcon = string.Empty;

        public int MaxCondition => @maxCondition;
        [JsonProperty, CreateProperty]
        internal int @maxCondition;

        public int MinCondition => @minCondition;
        [JsonProperty, CreateProperty]
        internal int @minCondition;

        public IReadOnlyDictionary<string, UpgradeMobilizationContent> MobilizationContents => @mobilizationContents;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, UpgradeMobilizationContent> @mobilizationContents = new();

        public IReadOnlyDictionary<int, float> PossibleProvinceStates => @possibleProvinceStates;
        [JsonProperty, CreateProperty]
        internal Dictionary<int, float> @possibleProvinceStates = new();

        public IReadOnlyDictionary<ResourceId, float> ProdBonus => @prodBonus;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @prodBonus = new();

        public string ProvIcon => @provIcon;
        [JsonProperty, CreateProperty]
        internal string @provIcon = string.Empty;

        public string ProvImage => @provImage;
        [JsonProperty, CreateProperty]
        internal string @provImage = string.Empty;

        public double RankingFactor => @rankingFactor;
        [JsonProperty, CreateProperty]
        internal double @rankingFactor;

        public IReadOnlyList<ContentItemId> RemovedUpgrades => @removedUpgrades;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @removedUpgrades = new();

        public ContentItemId? ReplacedUpgrade => @replacedUpgrade;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @replacedUpgrade;

        public IReadOnlyList<ContentItemId> RequiredResearches => @requiredResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @requiredResearches = new();

        public IReadOnlyDictionary<ContentItemId, float> RequiredUpgrades => @requiredUpgrades;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @requiredUpgrades = new();

        public IReadOnlyDictionary<ResourceId, float> ResourceCapImpact => @resourceCapImpact;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @resourceCapImpact = new();

        public bool ShowConstructionInNews => @showConstructionInNews;
        [JsonProperty, CreateProperty]
        internal bool @showConstructionInNews;

        public bool ShowDamagedInNews => @showDamagedInNews;
        [JsonProperty, CreateProperty]
        internal bool @showDamagedInNews;

        public string SortOrders => @sortOrders;
        [JsonProperty, CreateProperty]
        internal string @sortOrders = string.Empty;

        public IReadOnlyDictionary<ContentItemId, float> UnitCosts => @unitCosts;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @unitCosts = new();

        public int UnitPack => @unitPack;
        [JsonProperty, CreateProperty]
        internal int @unitPack;

        public string UpgrDesc => @upgrDesc;
        [JsonProperty, CreateProperty]
        internal string @upgrDesc = string.Empty;

        public string UpgrName => @upgrName;
        [JsonProperty, CreateProperty]
        internal string @upgrName = string.Empty;

        [UnityEngine.Scripting.Preserve]
        public UpgradeContent() { }
    }
}
