using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DamageAuraContent {
        public TimeSpan DamageTickInterval => @damageTickInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan @damageTickInterval;

        public float Radius => @radius;
        [JsonProperty, CreateProperty]
        internal float @radius;

        [UnityEngine.Scripting.Preserve]
        public DamageAuraContent() { }
    }
}
