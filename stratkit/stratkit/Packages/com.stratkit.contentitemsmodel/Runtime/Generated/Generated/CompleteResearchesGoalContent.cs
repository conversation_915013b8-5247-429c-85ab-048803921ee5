using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class CompleteResearchesGoalContent {
        public int NumberOfResearches => @numberOfResearches;
        [JsonProperty, CreateProperty]
        internal int @numberOfResearches;

        [UnityEngine.Scripting.Preserve]
        public CompleteResearchesGoalContent() { }
    }
}
