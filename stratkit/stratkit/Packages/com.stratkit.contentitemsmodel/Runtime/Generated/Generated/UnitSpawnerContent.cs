using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitSpawnerContent {
        public bool CanAttackOnSea => @canAttackOnSea;
        [JsonProperty, CreateProperty]
        internal bool @canAttackOnSea;

        public TimeSpan MobilizationTime => @mobilizationTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @mobilizationTime;

        public ContentItemId SpawnedUnitTypeId => @spawnedUnitTypeId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @spawnedUnitTypeId;

        [UnityEngine.Scripting.Preserve]
        public UnitSpawnerContent() { }
    }
}
