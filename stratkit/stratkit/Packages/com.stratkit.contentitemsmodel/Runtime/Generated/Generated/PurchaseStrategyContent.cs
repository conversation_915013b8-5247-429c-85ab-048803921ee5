using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class PurchaseStrategyContent {
        public IReadOnlyDictionary<ResourceId, float> Cost => @cost;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @cost = new();

        public bool EnableAllPriority => @enableAllPriority;
        [JsonProperty, CreateProperty]
        internal bool @enableAllPriority;

        public int InitialCount => @initialCount;
        [JsonProperty, CreateProperty]
        internal int @initialCount;

        public bool Purchasable => @purchasable;
        [JsonProperty, CreateProperty]
        internal bool @purchasable;

        public string Requirements => @requirements;
        [JsonProperty, CreateProperty]
        internal string @requirements = string.Empty;

        [UnityEngine.Scripting.Preserve]
        public PurchaseStrategyContent() { }
    }
}
