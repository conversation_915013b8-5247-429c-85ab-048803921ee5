using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ArmyModifierSet {
        public IReadOnlyDictionary<string, ArmyModifierContent> ArmyModifiers => @armyModifiers;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, ArmyModifierContent> @armyModifiers = new();

        public string? GroupId => @groupId;
        [JsonProperty, CreateProperty]
        internal string? @groupId;

        public ContentItemId? RequiredInventoryItem => @requiredInventoryItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @requiredInventoryItem;

        [UnityEngine.Scripting.Preserve]
        public ArmyModifierSet() { }
    }
}
