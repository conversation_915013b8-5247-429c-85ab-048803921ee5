using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class HeroConfigContent {
        public int HeroLimit => @heroLimit;
        [JsonProperty, CreateProperty]
        internal int @heroLimit;

        [UnityEngine.Scripting.Preserve]
        public HeroConfigContent() { }
    }
}
