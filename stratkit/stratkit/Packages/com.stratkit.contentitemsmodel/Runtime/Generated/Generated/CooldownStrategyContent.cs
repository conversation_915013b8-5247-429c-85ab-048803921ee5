using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class CooldownStrategyContent {
        public ContentItemId CooldownToken => @cooldownToken;
        [JsonProperty, CreateProperty]
        internal ContentItemId @cooldownToken;

        public CooldownType CooldownType => @cooldownType;
        [JsonProperty, CreateProperty]
        internal CooldownType @cooldownType;

        [UnityEngine.Scripting.Preserve]
        public CooldownStrategyContent() { }
    }
}
