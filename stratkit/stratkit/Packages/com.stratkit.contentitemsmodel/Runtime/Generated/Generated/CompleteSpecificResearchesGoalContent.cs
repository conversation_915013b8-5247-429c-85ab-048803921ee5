using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class CompleteSpecificResearchesGoalContent {
        public int NumberOfResearches => @numberOfResearches;
        [JsonProperty, CreateProperty]
        internal int @numberOfResearches;

        public IReadOnlyList<string> ResearchUpgradeGroups => @researchUpgradeGroups;
        [JsonProperty, CreateProperty]
        internal List<string> @researchUpgradeGroups = new();

        [UnityEngine.Scripting.Preserve]
        public CompleteSpecificResearchesGoalContent() { }
    }
}
