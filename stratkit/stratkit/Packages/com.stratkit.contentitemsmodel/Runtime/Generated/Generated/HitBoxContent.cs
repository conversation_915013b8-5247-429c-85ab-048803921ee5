using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class HitBoxContent {
        public float DamageShareWeight => @damageShareWeight;
        [JsonProperty, CreateProperty]
        internal float @damageShareWeight;

        public float HitChanceWeight => @hitChanceWeight;
        [JsonProperty, CreateProperty]
        internal float @hitChanceWeight;

        [UnityEngine.Scripting.Preserve]
        public HitBoxContent() { }
    }
}
