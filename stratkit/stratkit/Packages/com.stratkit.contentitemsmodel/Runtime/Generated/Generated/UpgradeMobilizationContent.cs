using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UpgradeMobilizationContent {
        public float MobilizationTimeBonus => @mobilizationTimeBonus;
        [JsonProperty, CreateProperty]
        internal float @mobilizationTimeBonus;

        [UnityEngine.Scripting.Preserve]
        public UpgradeMobilizationContent() { }
    }
}
