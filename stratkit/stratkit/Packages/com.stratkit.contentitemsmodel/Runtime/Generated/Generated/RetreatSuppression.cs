using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class RetreatSuppression {
        public float Suppression => @suppression;
        [JsonProperty, CreateProperty]
        internal float @suppression;

        [UnityEngine.Scripting.Preserve]
        public RetreatSuppression() { }
    }
}
