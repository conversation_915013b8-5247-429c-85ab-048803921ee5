using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class RarityIntervalContent {
        public int Max => @max;
        [JsonProperty, CreateProperty]
        internal int @max;

        public int Min => @min;
        [JsonProperty, CreateProperty]
        internal int @min;

        [UnityEngine.Scripting.Preserve]
        public RarityIntervalContent() { }
    }
}
