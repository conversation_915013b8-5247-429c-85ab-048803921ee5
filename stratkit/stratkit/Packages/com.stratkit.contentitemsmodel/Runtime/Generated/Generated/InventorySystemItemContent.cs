using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class InventorySystemItemContent {
        public string ItemGroup => @itemGroup;
        [JsonProperty, CreateProperty]
        internal string @itemGroup = string.Empty;

        public string ItemPriority => @itemPriority;
        [JsonProperty, CreateProperty]
        internal string @itemPriority = string.Empty;

        public string ItemType => @itemType;
        [JsonProperty, CreateProperty]
        internal string @itemType = string.Empty;

        public InventoryUnitCardContent? UnitCardContent => @unitCardContent;
        [JsonProperty, CreateProperty]
        internal InventoryUnitCardContent? @unitCardContent;

        [UnityEngine.Scripting.Preserve]
        public InventorySystemItemContent() { }
    }
}
