using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitMobilizationContent {
        public TimeSpan MobilizationTime => @mobilizationTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @mobilizationTime;

        [UnityEngine.Scripting.Preserve]
        public UnitMobilizationContent() { }
    }
}
