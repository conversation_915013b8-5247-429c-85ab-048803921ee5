using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class CombatRestrictionContent {
        public bool CanBeDeployedInActiveCombat => @canBeDeployedInActiveCombat;
        [JsonProperty, CreateProperty]
        internal bool @canBeDeployedInActiveCombat;

        public float? MaxDistanceFromEnemy => @maxDistanceFromEnemy;
        [JsonProperty, CreateProperty]
        internal float? @maxDistanceFromEnemy;

        public float? MinDistanceFromEnemy => @minDistanceFromEnemy;
        [JsonProperty, CreateProperty]
        internal float? @minDistanceFromEnemy;

        [UnityEngine.Scripting.Preserve]
        public CombatRestrictionContent() { }
    }
}
