using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AffectedUnitsContent {
        public IReadOnlyList<DamageType> DamageTypes => @damageTypes;
        [JsonProperty, CreateProperty]
        internal List<DamageType> @damageTypes = new();

        public IReadOnlyList<Faction> Factions => @factions;
        [JsonProperty, CreateProperty]
        internal List<Faction> @factions = new();

        public IReadOnlyList<ContentItemId> TypeIds => @typeIds;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @typeIds = new();

        public IReadOnlyList<string> UpgradeGroups => @upgradeGroups;
        [JsonProperty, CreateProperty]
        internal List<string> @upgradeGroups = new();

        [UnityEngine.Scripting.Preserve]
        public AffectedUnitsContent() { }
    }
}
