using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class OwnProvincesGoalContent {
        public int NumberOfProvinces => @numberOfProvinces;
        [JsonProperty, CreateProperty]
        internal int @numberOfProvinces;

        [UnityEngine.Scripting.Preserve]
        public OwnProvincesGoalContent() { }
    }
}
