using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class SoftCurrencyContent {
        public ResourceId SoftCurrencyResourceId => @softCurrencyResourceId;
        [JsonProperty, CreateProperty]
        internal ResourceId @softCurrencyResourceId;

        [UnityEngine.Scripting.Preserve]
        public SoftCurrencyContent() { }
    }
}
