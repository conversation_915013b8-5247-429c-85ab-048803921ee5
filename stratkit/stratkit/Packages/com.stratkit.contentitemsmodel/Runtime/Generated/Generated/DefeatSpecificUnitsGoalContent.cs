using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DefeatSpecificUnitsGoalContent {
        public int NumberOfUnits => @numberOfUnits;
        [JsonProperty, CreateProperty]
        internal int @numberOfUnits;

        public IReadOnlyList<string> UnitUpgradeGroups => @unitUpgradeGroups;
        [JsonProperty, CreateProperty]
        internal List<string> @unitUpgradeGroups = new();

        [UnityEngine.Scripting.Preserve]
        public DefeatSpecificUnitsGoalContent() { }
    }
}
