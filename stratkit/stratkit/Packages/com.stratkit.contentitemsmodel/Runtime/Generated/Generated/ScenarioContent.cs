using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class ScenarioContent : IContent {
        public BuildAiContent? AiBuildConfig => @aiBuildConfig;
        [JsonProperty, CreateProperty]
        internal BuildAiContent? @aiBuildConfig;

        public IReadOnlyDictionary<float, float> AiCowardMoodFactor => @aiCowardMoodFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @aiCowardMoodFactor = new();

        public GarrisonAiContent? AiGarrisonConfig => @aiGarrisonConfig;
        [JsonProperty, CreateProperty]
        internal GarrisonAiContent? @aiGarrisonConfig;

        public OffensiveAiContent? AiOffensiveConfig => @aiOffensiveConfig;
        [JsonProperty, CreateProperty]
        internal OffensiveAiContent? @aiOffensiveConfig;

        public int? AiSessionsPerDay => @aiSessionsPerDay;
        [JsonProperty, CreateProperty]
        internal int? @aiSessionsPerDay;

        public IReadOnlyList<ContentItemId> AiStartResearches => @aiStartResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @aiStartResearches = new();

        public IReadOnlyDictionary<float, float> AiWarDeclarationProvinceCapFactor => @aiWarDeclarationProvinceCapFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @aiWarDeclarationProvinceCapFactor = new();

        public TimeSpan? AllProvincesRefreshTickRate => @allProvincesRefreshTickRate;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @allProvincesRefreshTickRate;

        public int AllianceLeagueSugPlayers => @allianceLeagueSugPlayers;
        [JsonProperty, CreateProperty]
        internal int @allianceLeagueSugPlayers;

        public TimeSpan? AttackInterval => @attackInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @attackInterval;

        public TimeSpan? AutoRecruitmentSpeed => @autoRecruitmentSpeed;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @autoRecruitmentSpeed;

        public bool BlockNextDayEvent => @blockNextDayEvent;
        [JsonProperty, CreateProperty]
        internal bool @blockNextDayEvent;

        public string? CanBeAutoCreatedByOtherTitles => @canBeAutoCreatedByOtherTitles;
        [JsonProperty, CreateProperty]
        internal string? @canBeAutoCreatedByOtherTitles;

        public IReadOnlyDictionary<float, float> CapitalDistancePenalty => @capitalDistancePenalty;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @capitalDistancePenalty = new();

        public string ClimateZones => @climateZones;
        [JsonProperty, CreateProperty]
        internal string @climateZones = string.Empty;

        public int? CoalitionGoldReward => @coalitionGoldReward;
        [JsonProperty, CreateProperty]
        internal int? @coalitionGoldReward;

        public int? CoalitionMemberLimit => @coalitionMemberLimit;
        [JsonProperty, CreateProperty]
        internal int? @coalitionMemberLimit;

        public int? CountryChooserGrouping => @countryChooserGrouping;
        [JsonProperty, CreateProperty]
        internal int? @countryChooserGrouping;

        public IReadOnlyDictionary<float, float> CountryChooserThreshold => @countryChooserThreshold;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @countryChooserThreshold = new();

        public TimeSpan? DayChangeInterval => @dayChangeInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @dayChangeInterval;

        public IReadOnlyList<ContentItemId> DisabledPremiums => @disabledPremiums;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @disabledPremiums = new();

        public IReadOnlyList<ContentItemId> DisabledResearches => @disabledResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @disabledResearches = new();

        public IReadOnlyList<ContentItemId> DisabledUnits => @disabledUnits;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @disabledUnits = new();

        public int DoubleResourceProductionThreshold => @doubleResourceProductionThreshold;
        [JsonProperty, CreateProperty]
        internal int @doubleResourceProductionThreshold;

        public Relation? EnemyTeamRelation => @enemyTeamRelation;
        [JsonProperty, CreateProperty]
        internal Relation? @enemyTeamRelation;

        public string? EventScenario => @eventScenario;
        [JsonProperty, CreateProperty]
        internal string? @eventScenario;

        public string? ExcludeFromHighlightedGames => @excludeFromHighlightedGames;
        [JsonProperty, CreateProperty]
        internal string? @excludeFromHighlightedGames;

        public ExpansionPenalty? ExpansionPenalty => @expansionPenalty;
        [JsonProperty, CreateProperty]
        internal ExpansionPenalty? @expansionPenalty;

        public bool ExperimentalOnMobile => @experimentalOnMobile;
        [JsonProperty, CreateProperty]
        internal bool @experimentalOnMobile;

        public string ExplorationSetFiles => @explorationSetFiles;
        [JsonProperty, CreateProperty]
        internal string @explorationSetFiles = string.Empty;

        public TeamMode? ForcedTeamMode => @forcedTeamMode;
        [JsonProperty, CreateProperty]
        internal TeamMode? @forcedTeamMode;

        public bool FreeOfChargePremiumCurrencyMode => @freeOfChargePremiumCurrencyMode;
        [JsonProperty, CreateProperty]
        internal bool @freeOfChargePremiumCurrencyMode;

        public int? GameDuration => @gameDuration;
        [JsonProperty, CreateProperty]
        internal int? @gameDuration;

        public IReadOnlyDictionary<string, GameGoalSelectionContent> GameGoals => @gameGoals;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, GameGoalSelectionContent> @gameGoals = new();

        public string GeneralCardLimits => @generalCardLimits;
        [JsonProperty, CreateProperty]
        internal string @generalCardLimits = string.Empty;

        public bool GoldFeature => @goldFeature;
        [JsonProperty, CreateProperty]
        internal bool @goldFeature;

        public TimeSpan? HarbourCosts => @harbourCosts;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @harbourCosts;

        public HeroConfigContent? HeroConfig => @heroConfig;
        [JsonProperty, CreateProperty]
        internal HeroConfigContent? @heroConfig;

        public bool ImmutableProvince => @immutableProvince;
        [JsonProperty, CreateProperty]
        internal bool @immutableProvince;

        public string? IndustrialComplexID => @industrialComplexID;
        [JsonProperty, CreateProperty]
        internal string? @industrialComplexID;

        public string IngameDesc => @ingameDesc;
        [JsonProperty, CreateProperty]
        internal string @ingameDesc = string.Empty;

        public string IngameName => @ingameName;
        [JsonProperty, CreateProperty]
        internal string @ingameName = string.Empty;

        public bool International => @international;
        [JsonProperty, CreateProperty]
        internal bool @international;

        public string? ListAllPlayers => @listAllPlayers;
        [JsonProperty, CreateProperty]
        internal string? @listAllPlayers;

        public int? ListOrder => @listOrder;
        [JsonProperty, CreateProperty]
        internal int? @listOrder;

        public MapFileReference Map => @map;
        [JsonProperty, CreateProperty]
        internal MapFileReference @map = new();

        public float? MarketFee => @marketFee;
        [JsonProperty, CreateProperty]
        internal float? @marketFee;

        public string? MaxCreationAllowed => @maxCreationAllowed;
        [JsonProperty, CreateProperty]
        internal string? @maxCreationAllowed;

        public Relation? MaxEnemyTeamRelation => @maxEnemyTeamRelation;
        [JsonProperty, CreateProperty]
        internal Relation? @maxEnemyTeamRelation;

        public int? MaxJoinDay => @maxJoinDay;
        [JsonProperty, CreateProperty]
        internal int? @maxJoinDay;

        public int MaxPlayers => @maxPlayers;
        [JsonProperty, CreateProperty]
        internal int @maxPlayers;

        public ContentItemId? MaxRank => @maxRank;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @maxRank;

        public int MaxTeams => @maxTeams;
        [JsonProperty, CreateProperty]
        internal int @maxTeams;

        public int MinPlayers => @minPlayers;
        [JsonProperty, CreateProperty]
        internal int @minPlayers;

        public ContentItemId? MinRank => @minRank;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @minRank;

        public int MinReopenProvinces => @minReopenProvinces;
        [JsonProperty, CreateProperty]
        internal int @minReopenProvinces;

        public int MinStartProvinces => @minStartProvinces;
        [JsonProperty, CreateProperty]
        internal int @minStartProvinces;

        public Relation? MinTeamRelation => @minTeamRelation;
        [JsonProperty, CreateProperty]
        internal Relation? @minTeamRelation;

        public string? ModID => @modID;
        [JsonProperty, CreateProperty]
        internal string? @modID;

        public int? ModVersion => @modVersion;
        [JsonProperty, CreateProperty]
        internal int? @modVersion;

        public string NativeUprisingPossibilities => @nativeUprisingPossibilities;
        [JsonProperty, CreateProperty]
        internal string @nativeUprisingPossibilities = string.Empty;

        public string? NoGarrison => @noGarrison;
        [JsonProperty, CreateProperty]
        internal string? @noGarrison;

        public bool NoRetirement => @noRetirement;
        [JsonProperty, CreateProperty]
        internal bool @noRetirement;

        public string? NoWof => @noWof;
        [JsonProperty, CreateProperty]
        internal string? @noWof;

        public int NumberOfUnknownAIs => @numberOfUnknownAIs;
        [JsonProperty, CreateProperty]
        internal int @numberOfUnknownAIs;

        public int NumberOfUnknownProvinces => @numberOfUnknownProvinces;
        [JsonProperty, CreateProperty]
        internal int @numberOfUnknownProvinces;

        public TimeSpan? PatrolInterval => @patrolInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @patrolInterval;

        public string? PlayNowAiLevel => @playNowAiLevel;
        [JsonProperty, CreateProperty]
        internal string? @playNowAiLevel;

        public string? PlayNowAnonymous => @playNowAnonymous;
        [JsonProperty, CreateProperty]
        internal string? @playNowAnonymous;

        public string? PlayNowAntiCheatLevel => @playNowAntiCheatLevel;
        [JsonProperty, CreateProperty]
        internal string? @playNowAntiCheatLevel;

        public string? PlayNowCountrySelect => @playNowCountrySelect;
        [JsonProperty, CreateProperty]
        internal string? @playNowCountrySelect;

        public int? PlayNowGameDuration => @playNowGameDuration;
        [JsonProperty, CreateProperty]
        internal int? @playNowGameDuration;

        public string? PlayNowMaxGameAge => @playNowMaxGameAge;
        [JsonProperty, CreateProperty]
        internal string? @playNowMaxGameAge;

        public string? PlayNowMinActivity => @playNowMinActivity;
        [JsonProperty, CreateProperty]
        internal string? @playNowMinActivity;

        public string? PlayNowNumberOfPlayers => @playNowNumberOfPlayers;
        [JsonProperty, CreateProperty]
        internal string? @playNowNumberOfPlayers;

        public string? PlayNowNumberOfTeams => @playNowNumberOfTeams;
        [JsonProperty, CreateProperty]
        internal string? @playNowNumberOfTeams;

        public string? PlayNowPeacePeriod => @playNowPeacePeriod;
        [JsonProperty, CreateProperty]
        internal string? @playNowPeacePeriod;

        public string? PlayNowPeacePeriodAI => @playNowPeacePeriodAI;
        [JsonProperty, CreateProperty]
        internal string? @playNowPeacePeriodAI;

        public string? PlayNowQuestProvinceConquer => @playNowQuestProvinceConquer;
        [JsonProperty, CreateProperty]
        internal string? @playNowQuestProvinceConquer;

        public string? PlayNowStartLevel => @playNowStartLevel;
        [JsonProperty, CreateProperty]
        internal string? @playNowStartLevel;

        public string? PlayNowStartWhenFull => @playNowStartWhenFull;
        [JsonProperty, CreateProperty]
        internal string? @playNowStartWhenFull;

        public int? PlayerAiStartDay => @playerAiStartDay;
        [JsonProperty, CreateProperty]
        internal int? @playerAiStartDay;

        public bool PoiShowIcon => @poiShowIcon;
        [JsonProperty, CreateProperty]
        internal bool @poiShowIcon;

        public int? PoiTimer => @poiTimer;
        [JsonProperty, CreateProperty]
        internal int? @poiTimer;

        public int? PoiWinningAmount => @poiWinningAmount;
        [JsonProperty, CreateProperty]
        internal int? @poiWinningAmount;

        public TimeSpan? PremiumActionAfterAttackCooldown => @premiumActionAfterAttackCooldown;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @premiumActionAfterAttackCooldown;

        public IReadOnlyList<PremiumSpyJobType>? PremiumSpyJobTypes => @premiumSpyJobTypes;
        [JsonProperty, CreateProperty]
        internal List<PremiumSpyJobType>? @premiumSpyJobTypes;

        public string? PreventTutorialSkip => @preventTutorialSkip;
        [JsonProperty, CreateProperty]
        internal string? @preventTutorialSkip;

        public bool ProhibitFriendlyRelationsForAI => @prohibitFriendlyRelationsForAI;
        [JsonProperty, CreateProperty]
        internal bool @prohibitFriendlyRelationsForAI;

        public string ProvinceLevelPossibilities => @provinceLevelPossibilities;
        [JsonProperty, CreateProperty]
        internal string @provinceLevelPossibilities = string.Empty;

        public int? ProvinceNeighbourMoraleFactor => @provinceNeighbourMoraleFactor;
        [JsonProperty, CreateProperty]
        internal int? @provinceNeighbourMoraleFactor;

        public int? ProvinceNeighbourMoraleThreshold => @provinceNeighbourMoraleThreshold;
        [JsonProperty, CreateProperty]
        internal int? @provinceNeighbourMoraleThreshold;

        public string? QuestProvinceConquerFeature => @questProvinceConquerFeature;
        [JsonProperty, CreateProperty]
        internal string? @questProvinceConquerFeature;

        public string? QuickJoinCooldownTimeInSeconds => @quickJoinCooldownTimeInSeconds;
        [JsonProperty, CreateProperty]
        internal string? @quickJoinCooldownTimeInSeconds;

        public bool RandomizeProvinces => @randomizeProvinces;
        [JsonProperty, CreateProperty]
        internal bool @randomizeProvinces;

        public IReadOnlyDictionary<float, float> RandomizedAiMood => @randomizedAiMood;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @randomizedAiMood = new();

        public int? RankedPeriodDays => @rankedPeriodDays;
        [JsonProperty, CreateProperty]
        internal int? @rankedPeriodDays;

        public double RankingScoreFactor => @rankingScoreFactor;
        [JsonProperty, CreateProperty]
        internal double @rankingScoreFactor;

        public int? ResearchOffset => @researchOffset;
        [JsonProperty, CreateProperty]
        internal int? @researchOffset;

        public int? ResearchTimeScale => @researchTimeScale;
        [JsonProperty, CreateProperty]
        internal int? @researchTimeScale;

        public string ResourcePossibilities => @resourcePossibilities;
        [JsonProperty, CreateProperty]
        internal string @resourcePossibilities = string.Empty;

        public float? ScenarioSpeedUpFactor => @scenarioSpeedUpFactor;
        [JsonProperty, CreateProperty]
        internal float? @scenarioSpeedUpFactor;

        public string? ScenarioTeamMode => @scenarioTeamMode;
        [JsonProperty, CreateProperty]
        internal string? @scenarioTeamMode;

        public bool ShowInQuickJoinList => @showInQuickJoinList;
        [JsonProperty, CreateProperty]
        internal bool @showInQuickJoinList;

        public string? ShowMapBriefing => @showMapBriefing;
        [JsonProperty, CreateProperty]
        internal string? @showMapBriefing;

        public string? SkipCreationLimitForGoldRounds => @skipCreationLimitForGoldRounds;
        [JsonProperty, CreateProperty]
        internal string? @skipCreationLimitForGoldRounds;

        public IReadOnlyList<ContentItemId> StartResearches => @startResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @startResearches = new();

        public IReadOnlyDictionary<ContentItemId, float> StartUnits => @startUnits;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @startUnits = new();

        public ScenarioStatus Status => @status;
        [JsonProperty, CreateProperty]
        internal ScenarioStatus @status;

        public int SugPlayers => @sugPlayers;
        [JsonProperty, CreateProperty]
        internal int @sugPlayers;

        public int SugTeams => @sugTeams;
        [JsonProperty, CreateProperty]
        internal int @sugTeams;

        public bool SuppressClientServerArmyStateMismatchException => @suppressClientServerArmyStateMismatchException;
        [JsonProperty, CreateProperty]
        internal bool @suppressClientServerArmyStateMismatchException;

        public TimeSpan? TeamJoinCDAfterKick => @teamJoinCDAfterKick;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @teamJoinCDAfterKick;

        public TimeSpan? TeamJoinCDAfterLeave => @teamJoinCDAfterLeave;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @teamJoinCDAfterLeave;

        public IReadOnlyList<ContentItemId> TeamVictoryAwards => @teamVictoryAwards;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @teamVictoryAwards = new();

        public int TeamVictoryPoints => @teamVictoryPoints;
        [JsonProperty, CreateProperty]
        internal int @teamVictoryPoints;

        public int? TeamVictoryReward => @teamVictoryReward;
        [JsonProperty, CreateProperty]
        internal int? @teamVictoryReward;

        public string? TeamVictoryRewardBoosts => @teamVictoryRewardBoosts;
        [JsonProperty, CreateProperty]
        internal string? @teamVictoryRewardBoosts;

        public string TerrainTypePossibilities => @terrainTypePossibilities;
        [JsonProperty, CreateProperty]
        internal string @terrainTypePossibilities = string.Empty;

        public string? TileCountX => @tileCountX;
        [JsonProperty, CreateProperty]
        internal string? @tileCountX;

        public string? TileCountY => @tileCountY;
        [JsonProperty, CreateProperty]
        internal string? @tileCountY;

        public TimeSpan? TimeLimitedScenarioDuration => @timeLimitedScenarioDuration;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @timeLimitedScenarioDuration;

        public long? TimeLimitedScenarioStartDate => @timeLimitedScenarioStartDate;
        [JsonProperty, CreateProperty]
        internal long? @timeLimitedScenarioStartDate;

        public TimeSpan? TimeLimitedScenarioTeaseDuration => @timeLimitedScenarioTeaseDuration;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @timeLimitedScenarioTeaseDuration;

        public bool TutorialScenario => @tutorialScenario;
        [JsonProperty, CreateProperty]
        internal bool @tutorialScenario;

        public int? TutorialTaskGoldReward => @tutorialTaskGoldReward;
        [JsonProperty, CreateProperty]
        internal int? @tutorialTaskGoldReward;

        public bool UnlimitedArmyTrades => @unlimitedArmyTrades;
        [JsonProperty, CreateProperty]
        internal bool @unlimitedArmyTrades;

        public bool UseFactionIconsForPlayerAvatars => @useFactionIconsForPlayerAvatars;
        [JsonProperty, CreateProperty]
        internal bool @useFactionIconsForPlayerAvatars;

        public string? UseHelpCarousel => @useHelpCarousel;
        [JsonProperty, CreateProperty]
        internal string? @useHelpCarousel;

        public string? UseTutorialAdviser => @useTutorialAdviser;
        [JsonProperty, CreateProperty]
        internal string? @useTutorialAdviser;

        public IReadOnlyList<ContentItemId> VictoryAwards => @victoryAwards;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @victoryAwards = new();

        public int VictoryPoints => @victoryPoints;
        [JsonProperty, CreateProperty]
        internal int @victoryPoints;

        public string? VictoryRewardBoosts => @victoryRewardBoosts;
        [JsonProperty, CreateProperty]
        internal string? @victoryRewardBoosts;

        [UnityEngine.Scripting.Preserve]
        public ScenarioContent() { }
    }
}
