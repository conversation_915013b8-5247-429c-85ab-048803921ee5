using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class StackStrategy {
        public int ClassId => @classId;
        [JsonProperty, CreateProperty]
        internal int @classId;

        public float Priority => @priority;
        [JsonProperty, CreateProperty]
        internal float @priority;

        [UnityEngine.Scripting.Preserve]
        public StackStrategy() { }
    }
}
