using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ProduceUnitsGoalContent {
        public int NumberOfUnits => @numberOfUnits;
        [JsonProperty, CreateProperty]
        internal int @numberOfUnits;

        [UnityEngine.Scripting.Preserve]
        public ProduceUnitsGoalContent() { }
    }
}
