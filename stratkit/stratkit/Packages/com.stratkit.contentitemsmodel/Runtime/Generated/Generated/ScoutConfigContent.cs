using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ScoutConfigContent {
        public IReadOnlyList<string> ScoutClasses => @scoutClasses;
        [JsonProperty, CreateProperty]
        internal List<string> @scoutClasses = new();

        public int ScoutLevel => @scoutLevel;
        [JsonProperty, CreateProperty]
        internal int @scoutLevel;

        [UnityEngine.Scripting.Preserve]
        public ScoutConfigContent() { }
    }
}
