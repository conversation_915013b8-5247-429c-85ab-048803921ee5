using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class AwardContent : IContent {
        public string Checker => @checker;
        [JsonProperty, CreateProperty]
        internal string @checker = string.Empty;

        public CollectibleRewardOfferContent? CollectibleRewardOffer => @collectibleRewardOffer;
        [JsonProperty, CreateProperty]
        internal CollectibleRewardOfferContent? @collectibleRewardOffer;

        public string Config => @config;
        [JsonProperty, CreateProperty]
        internal string @config = string.Empty;

        public string CriteriaDesc => @criteriaDesc;
        [JsonProperty, CreateProperty]
        internal string @criteriaDesc = string.Empty;

        public string FullTitle => @fullTitle;
        [JsonProperty, CreateProperty]
        internal string @fullTitle = string.Empty;

        public int Set => @set;
        [JsonProperty, CreateProperty]
        internal int @set;

        public int SetOrderID => @setOrderID;
        [JsonProperty, CreateProperty]
        internal int @setOrderID;

        public bool ShowProgress => @showProgress;
        [JsonProperty, CreateProperty]
        internal bool @showProgress;

        public int Trigger => @trigger;
        [JsonProperty, CreateProperty]
        internal int @trigger;

        public int Type => @type;
        [JsonProperty, CreateProperty]
        internal int @type;

        [UnityEngine.Scripting.Preserve]
        public AwardContent() { }
    }
}
