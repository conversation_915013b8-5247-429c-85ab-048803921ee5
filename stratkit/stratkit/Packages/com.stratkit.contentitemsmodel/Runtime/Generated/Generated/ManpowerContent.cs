using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class ManpowerContent : IContent {
        public float ManpowerPopulationFactor => @manpowerPopulationFactor;
        [JsonProperty, CreateProperty]
        internal float @manpowerPopulationFactor;

        public ResourceId ManpowerResource => @manpowerResource;
        [JsonProperty, CreateProperty]
        internal ResourceId @manpowerResource;

        [UnityEngine.Scripting.Preserve]
        public ManpowerContent() { }
    }
}
