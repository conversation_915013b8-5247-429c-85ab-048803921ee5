using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class RankContent : IContent {
        public string FullTitle => @fullTitle;
        [JsonProperty, CreateProperty]
        internal string @fullTitle = string.Empty;

        public bool IsOfficer => @isOfficer;
        [JsonProperty, CreateProperty]
        internal bool @isOfficer;

        public int MinPoints => @minPoints;
        [JsonProperty, CreateProperty]
        internal int @minPoints;

        [UnityEngine.Scripting.Preserve]
        public RankContent() { }
    }
}
