using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ProvinceSelectorContent {
        public int MaxDistance => @maxDistance;
        [JsonProperty, CreateProperty]
        internal int @maxDistance;

        public int MaxLandHops => @maxLandHops;
        [JsonProperty, CreateProperty]
        internal int @maxLandHops;

        public int MaxSeaHops => @maxSeaHops;
        [JsonProperty, CreateProperty]
        internal int @maxSeaHops;

        [UnityEngine.Scripting.Preserve]
        public ProvinceSelectorContent() { }
    }
}
