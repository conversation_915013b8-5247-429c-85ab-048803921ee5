using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class VisibilityStrategyContent {
        public Relation MinimumRelation => @minimumRelation;
        [JsonProperty, CreateProperty]
        internal Relation @minimumRelation;

        [UnityEngine.Scripting.Preserve]
        public VisibilityStrategyContent() { }
    }
}
