using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class MoveArmiesGoalContent {
        public int NumberOfArmies => @numberOfArmies;
        [JsonProperty, CreateProperty]
        internal int @numberOfArmies;

        [UnityEngine.Scripting.Preserve]
        public MoveArmiesGoalContent() { }
    }
}
