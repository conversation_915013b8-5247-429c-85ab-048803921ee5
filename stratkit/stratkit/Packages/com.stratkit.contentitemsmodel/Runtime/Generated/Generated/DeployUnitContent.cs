using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DeployUnitContent {
        public ContentItemId? CooldownToken => @cooldownToken;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @cooldownToken;

        public int DeployAmount => @deployAmount;
        [JsonProperty, CreateProperty]
        internal int @deployAmount;

        public IReadOnlyDictionary<ResourceId, int> DeployCost => @deployCost;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, int> @deployCost = new();

        public float DeployRange => @deployRange;
        [JsonProperty, CreateProperty]
        internal float @deployRange;

        public TimeSpan DeployTime => @deployTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @deployTime;

        public ContentItemId SpawnedUnitTypeId => @spawnedUnitTypeId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @spawnedUnitTypeId;

        [UnityEngine.Scripting.Preserve]
        public DeployUnitContent() { }
    }
}
