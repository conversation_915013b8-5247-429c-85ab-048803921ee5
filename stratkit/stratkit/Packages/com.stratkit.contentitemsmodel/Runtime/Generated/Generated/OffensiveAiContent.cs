using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class OffensiveAiContent {
        public float AiCore => @aiCore;
        [JsonProperty, CreateProperty]
        internal float @aiCore;

        public float Base => @base;
        [JsonProperty, CreateProperty]
        internal float @base;

        public float Capital => @capital;
        [JsonProperty, CreateProperty]
        internal float @capital;

        public float EnemyCore => @enemyCore;
        [JsonProperty, CreateProperty]
        internal float @enemyCore;

        public float EnemyStrengthProvinceNeighborsFactor => @enemyStrengthProvinceNeighborsFactor;
        [JsonProperty, CreateProperty]
        internal float @enemyStrengthProvinceNeighborsFactor;

        public IReadOnlyDictionary<float, float> MilitaryPower => @militaryPower;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @militaryPower = new();

        public float Upgrades => @upgrades;
        [JsonProperty, CreateProperty]
        internal float @upgrades;

        public float VictoryPoints => @victoryPoints;
        [JsonProperty, CreateProperty]
        internal float @victoryPoints;

        [UnityEngine.Scripting.Preserve]
        public OffensiveAiContent() { }
    }
}
