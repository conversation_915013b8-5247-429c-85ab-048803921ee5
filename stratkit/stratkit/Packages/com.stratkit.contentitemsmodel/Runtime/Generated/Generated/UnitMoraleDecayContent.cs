using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitMoraleDecayContent {
        public TimeSpan MoraleDecayTime => @moraleDecayTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @moraleDecayTime;

        public float MoraleThreshold => @moraleThreshold;
        [JsonProperty, CreateProperty]
        internal float @moraleThreshold;

        [UnityEngine.Scripting.Preserve]
        public UnitMoraleDecayContent() { }
    }
}
