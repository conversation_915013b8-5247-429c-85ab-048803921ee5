using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class InventoryUnitCardContent {
        public bool AutoUpgradeWithResearch => @autoUpgradeWithResearch;
        [JsonProperty, CreateProperty]
        internal bool @autoUpgradeWithResearch;

        public int? DayOfAvailability => @dayOfAvailability;
        [JsonProperty, CreateProperty]
        internal int? @dayOfAvailability;

        public int? DeploymentMoraleCost => @deploymentMoraleCost;
        [JsonProperty, CreateProperty]
        internal int? @deploymentMoraleCost;

        public int DeploymentMoraleThreshold => @deploymentMoraleThreshold;
        [JsonProperty, CreateProperty]
        internal int @deploymentMoraleThreshold;

        public TimeSpan MobilizationTime => @mobilizationTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @mobilizationTime;

        public bool Permanent => @permanent;
        [JsonProperty, CreateProperty]
        internal bool @permanent;

        public IReadOnlyDictionary<string, RequiredItemEffects> RequiredItemEffects => @requiredItemEffects;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, RequiredItemEffects> @requiredItemEffects = new();

        [UnityEngine.Scripting.Preserve]
        public InventoryUnitCardContent() { }
    }
}
