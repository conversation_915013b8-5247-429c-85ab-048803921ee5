using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ConstructSpecificUpgradesGoalContent {
        public int NumberOfUpgrades => @numberOfUpgrades;
        [JsonProperty, CreateProperty]
        internal int @numberOfUpgrades;

        public IReadOnlyList<ContentItemId> RequiredUpgradeIds => @requiredUpgradeIds;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @requiredUpgradeIds = new();

        [UnityEngine.Scripting.Preserve]
        public ConstructSpecificUpgradesGoalContent() { }
    }
}
