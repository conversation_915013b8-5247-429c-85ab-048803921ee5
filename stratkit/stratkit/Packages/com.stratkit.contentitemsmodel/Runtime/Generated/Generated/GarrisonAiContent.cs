using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class GarrisonAiContent {
        public int MinGarrisonSize => @minGarrisonSize;
        [JsonProperty, CreateProperty]
        internal int @minGarrisonSize;

        public int PrefGarrison => @prefGarrison;
        [JsonProperty, CreateProperty]
        internal int @prefGarrison;

        [UnityEngine.Scripting.Preserve]
        public GarrisonAiContent() { }
    }
}
