using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DefensiveAiContent {
        public float BaseValue => @baseValue;
        [JsonProperty, CreateProperty]
        internal float @baseValue;

        public float CapitalValue => @capitalValue;
        [JsonProperty, CreateProperty]
        internal float @capitalValue;

        public IReadOnlyDictionary<Relation, float> DangerFactors => @dangerFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<Relation, float> @dangerFactors = new();

        public float UpgradeValue => @upgradeValue;
        [JsonProperty, CreateProperty]
        internal float @upgradeValue;

        [UnityEngine.Scripting.Preserve]
        public DefensiveAiContent() { }
    }
}
