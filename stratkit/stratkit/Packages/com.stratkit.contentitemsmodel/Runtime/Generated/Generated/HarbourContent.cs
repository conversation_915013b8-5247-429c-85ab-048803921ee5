using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class HarbourContent {
        public float EmbarkmentFactor => @embarkmentFactor;
        [JsonProperty, CreateProperty]
        internal float @embarkmentFactor;

        public IReadOnlyList<TerrainType> ForbiddenTerrain => @forbiddenTerrain;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @forbiddenTerrain = new();

        [UnityEngine.Scripting.Preserve]
        public HarbourContent() { }
    }
}
