using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class StealthConfigContent {
        public string StealthClass => @stealthClass;
        [JsonProperty, CreateProperty]
        internal string @stealthClass = string.Empty;

        public int StealthLevel => @stealthLevel;
        [JsonProperty, CreateProperty]
        internal int @stealthLevel;

        public IReadOnlyList<TerrainType> StealthOnTerrain => @stealthOnTerrain;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @stealthOnTerrain = new();

        [UnityEngine.Scripting.Preserve]
        public StealthConfigContent() { }
    }
}
