using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class BuildAiContent {
        public float CapitalFactor => @capitalFactor;
        [JsonProperty, CreateProperty]
        internal float @capitalFactor;

        public float NonCoreFactor => @nonCoreFactor;
        [JsonProperty, CreateProperty]
        internal float @nonCoreFactor;

        public float UnitFactor => @unitFactor;
        [JsonProperty, CreateProperty]
        internal float @unitFactor;

        public float UpgradeEconomyFactor => @upgradeEconomyFactor;
        [JsonProperty, CreateProperty]
        internal float @upgradeEconomyFactor;

        public float UpgradeMilitaryFactor => @upgradeMilitaryFactor;
        [JsonProperty, CreateProperty]
        internal float @upgradeMilitaryFactor;

        public float UpgradeMoraleFactor => @upgradeMoraleFactor;
        [JsonProperty, CreateProperty]
        internal float @upgradeMoraleFactor;

        public float VictoryPointValue => @victoryPointValue;
        [JsonProperty, CreateProperty]
        internal float @victoryPointValue;

        [UnityEngine.Scripting.Preserve]
        public BuildAiContent() { }
    }
}
