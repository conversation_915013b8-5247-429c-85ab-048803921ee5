using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class OfferContent : IContent {
        public string Country => @country;
        [JsonProperty, CreateProperty]
        internal string @country = string.Empty;

        public string? Currency => @currency;
        [JsonProperty, CreateProperty]
        internal string? @currency;

        public long EndDate => @endDate;
        [JsonProperty, CreateProperty]
        internal long @endDate;

        public bool Featured => @featured;
        [JsonProperty, CreateProperty]
        internal bool @featured;

        public string Image => @image;
        [JsonProperty, CreateProperty]
        internal string @image = string.Empty;

        public int ItemAmount => @itemAmount;
        [JsonProperty, CreateProperty]
        internal int @itemAmount;

        public double MinPrice => @minPrice;
        [JsonProperty, CreateProperty]
        internal double @minPrice;

        public string Name => @name;
        [JsonProperty, CreateProperty]
        internal string @name = string.Empty;

        public int OfferCategory => @offerCategory;
        [JsonProperty, CreateProperty]
        internal int @offerCategory;

        public IReadOnlyDictionary<ContentItemId, float> PremiumIDs => @premiumIDs;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @premiumIDs = new();

        public double Price => @price;
        [JsonProperty, CreateProperty]
        internal double @price;

        public IReadOnlyDictionary<ContentItemId, float> PriceConfig => @priceConfig;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @priceConfig = new();

        public IReadOnlyDictionary<float, float> PriceFunction => @priceFunction;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @priceFunction = new();

        public double PriceStep => @priceStep;
        [JsonProperty, CreateProperty]
        internal double @priceStep;

        public int Quantity => @quantity;
        [JsonProperty, CreateProperty]
        internal int @quantity;

        public long ReferenceValue => @referenceValue;
        [JsonProperty, CreateProperty]
        internal long @referenceValue;

        public IReadOnlyDictionary<ContentItemId, float> RequiredPremiums => @requiredPremiums;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @requiredPremiums = new();

        public ResourceId? Resource => @resource;
        [JsonProperty, CreateProperty]
        internal ResourceId? @resource;

        public string RestrictToMethods => @restrictToMethods;
        [JsonProperty, CreateProperty]
        internal string @restrictToMethods = string.Empty;

        public string SpecialImg => @specialImg;
        [JsonProperty, CreateProperty]
        internal string @specialImg = string.Empty;

        public string SpecialPriceImg => @specialPriceImg;
        [JsonProperty, CreateProperty]
        internal string @specialPriceImg = string.Empty;

        public long StartDate => @startDate;
        [JsonProperty, CreateProperty]
        internal long @startDate;

        [UnityEngine.Scripting.Preserve]
        public OfferContent() { }
    }
}
