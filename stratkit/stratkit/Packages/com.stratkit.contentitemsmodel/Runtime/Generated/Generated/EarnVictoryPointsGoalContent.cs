using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class EarnVictoryPointsGoalContent {
        public int NumberOfVictoryPoints => @numberOfVictoryPoints;
        [JsonProperty, CreateProperty]
        internal int @numberOfVictoryPoints;

        [UnityEngine.Scripting.Preserve]
        public EarnVictoryPointsGoalContent() { }
    }
}
