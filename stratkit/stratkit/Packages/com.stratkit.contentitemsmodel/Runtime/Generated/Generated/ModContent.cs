using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class ModContent : IContent {
        public bool AddefendHupLogin => @addefendHupLogin;
        [JsonProperty, CreateProperty]
        internal bool @addefendHupLogin;

        public bool AddefendUberLogin => @addefendUberLogin;
        [JsonProperty, CreateProperty]
        internal bool @addefendUberLogin;

        public AdviserRewardsConfig? AdviserRewardConfig => @adviserRewardConfig;
        [JsonProperty, CreateProperty]
        internal AdviserRewardsConfig? @adviserRewardConfig;

        public BuildAiContent AiBuildConfig => @aiBuildConfig;
        [JsonProperty, CreateProperty]
        internal BuildAiContent @aiBuildConfig = new();

        public IReadOnlyDictionary<float, float> AiCowardMoodFactor => @aiCowardMoodFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @aiCowardMoodFactor = new();

        public GarrisonAiContent AiGarrisonConfig => @aiGarrisonConfig;
        [JsonProperty, CreateProperty]
        internal GarrisonAiContent @aiGarrisonConfig = new();

        public int AiMinimumMarketOrderSize => @aiMinimumMarketOrderSize;
        [JsonProperty, CreateProperty]
        internal int @aiMinimumMarketOrderSize;

        public OffensiveAiContent AiOffensiveConfig => @aiOffensiveConfig;
        [JsonProperty, CreateProperty]
        internal OffensiveAiContent @aiOffensiveConfig = new();

        public int AiSessionsPerDay => @aiSessionsPerDay;
        [JsonProperty, CreateProperty]
        internal int @aiSessionsPerDay;

        public int AiStockFactor => @aiStockFactor;
        [JsonProperty, CreateProperty]
        internal int @aiStockFactor;

        public IReadOnlyDictionary<float, float> AiWarDeclarationProvinceCapFactor => @aiWarDeclarationProvinceCapFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @aiWarDeclarationProvinceCapFactor = new();

        public int? AirCorridorDegrees => @airCorridorDegrees;
        [JsonProperty, CreateProperty]
        internal int? @airCorridorDegrees;

        public int? ArmyCap => @armyCap;
        [JsonProperty, CreateProperty]
        internal int? @armyCap;

        public float ArmyStrengthFactor => @armyStrengthFactor;
        [JsonProperty, CreateProperty]
        internal float @armyStrengthFactor;

        public TimeSpan AttackInterval => @attackInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan @attackInterval;

        public AutoArmyWithdrawContent? AutoArmyWithdraw => @autoArmyWithdraw;
        [JsonProperty, CreateProperty]
        internal AutoArmyWithdrawContent? @autoArmyWithdraw;

        public AutoRecruitContent? AutoRecruit => @autoRecruit;
        [JsonProperty, CreateProperty]
        internal AutoRecruitContent? @autoRecruit;

        public bool AutoUnitUpgradeWithResearch => @autoUnitUpgradeWithResearch;
        [JsonProperty, CreateProperty]
        internal bool @autoUnitUpgradeWithResearch;

        public IReadOnlyList<ContentItemId> Awards => @awards;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @awards = new();

        public int? BuildingDamageFromSpies => @buildingDamageFromSpies;
        [JsonProperty, CreateProperty]
        internal int? @buildingDamageFromSpies;

        public int BuildingDamageOnConquer => @buildingDamageOnConquer;
        [JsonProperty, CreateProperty]
        internal int @buildingDamageOnConquer;

        public int CapitalDayDistance => @capitalDayDistance;
        [JsonProperty, CreateProperty]
        internal int @capitalDayDistance;

        public int CasualtiesFactor => @casualtiesFactor;
        [JsonProperty, CreateProperty]
        internal int @casualtiesFactor;

        public bool Compat => @compat;
        [JsonProperty, CreateProperty]
        internal bool @compat;

        public string CompatChilds => @compatChilds;
        [JsonProperty, CreateProperty]
        internal string @compatChilds = string.Empty;

        public IReadOnlyList<ContentItemId> CompatScenarios => @compatScenarios;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @compatScenarios = new();

        public int CoreProvinceDefenceFactor => @coreProvinceDefenceFactor;
        [JsonProperty, CreateProperty]
        internal int @coreProvinceDefenceFactor;

        public IReadOnlyDictionary<float, float> CrowdPenalty => @crowdPenalty;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @crowdPenalty = new();

        public ContentItemId? DailyPayoutItem => @dailyPayoutItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @dailyPayoutItem;

        public IReadOnlyList<DamageType> DamageTypes => @damageTypes;
        [JsonProperty, CreateProperty]
        internal List<DamageType> @damageTypes = new();

        public ContentItemId DefaultInfantryUnitId => @defaultInfantryUnitId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @defaultInfantryUnitId;

        public ContentItemId DefaultScenario => @defaultScenario;
        [JsonProperty, CreateProperty]
        internal ContentItemId @defaultScenario;

        public DefensiveAiContent? DefensiveAi => @defensiveAi;
        [JsonProperty, CreateProperty]
        internal DefensiveAiContent? @defensiveAi;

        public bool DisableNonUrbanProduction => @disableNonUrbanProduction;
        [JsonProperty, CreateProperty]
        internal bool @disableNonUrbanProduction;

        public bool DisplayAds => @displayAds;
        [JsonProperty, CreateProperty]
        internal bool @displayAds;

        public IReadOnlyList<ResourceId> EnabledDiplomaticTradeResources => @enabledDiplomaticTradeResources;
        [JsonProperty, CreateProperty]
        internal List<ResourceId> @enabledDiplomaticTradeResources = new();

        public IReadOnlyList<DiplomaticTradeType> EnabledDiplomaticTradeTypes => @enabledDiplomaticTradeTypes;
        [JsonProperty, CreateProperty]
        internal List<DiplomaticTradeType> @enabledDiplomaticTradeTypes = new();

        public EqualDamageDistributionContent EqualDamageDistribution => @equalDamageDistribution;
        [JsonProperty, CreateProperty]
        internal EqualDamageDistributionContent @equalDamageDistribution = new();

        public ExpansionPenalty? ExpansionPenalty => @expansionPenalty;
        [JsonProperty, CreateProperty]
        internal ExpansionPenalty? @expansionPenalty;

        public FakeOrdersContent? FakeOrders => @fakeOrders;
        [JsonProperty, CreateProperty]
        internal FakeOrdersContent? @fakeOrders;

        public IReadOnlyList<ModFeature> Features => @features;
        [JsonProperty, CreateProperty]
        internal List<ModFeature> @features = new();

        public IReadOnlyDictionary<float, float> FloorSpacePenalty => @floorSpacePenalty;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @floorSpacePenalty = new();

        public float FortressConcealmentThreshold => @fortressConcealmentThreshold;
        [JsonProperty, CreateProperty]
        internal float @fortressConcealmentThreshold;

        public IReadOnlyList<ContentItemId> GameGoals => @gameGoals;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @gameGoals = new();

        public TimeSpan HarbourCosts => @harbourCosts;
        [JsonProperty, CreateProperty]
        internal TimeSpan @harbourCosts;

        public HeroConfigContent? HeroConfig => @heroConfig;
        [JsonProperty, CreateProperty]
        internal HeroConfigContent? @heroConfig;

        public IReadOnlyDictionary<GameEventType, int> IngameEvents => @ingameEvents;
        [JsonProperty, CreateProperty]
        internal Dictionary<GameEventType, int> @ingameEvents = new();

        public IReadOnlyList<ContentItemId> LeagueScenarios => @leagueScenarios;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @leagueScenarios = new();

        public LossOfCapitalPenalty? LossOfCapitalPenalty => @lossOfCapitalPenalty;
        [JsonProperty, CreateProperty]
        internal LossOfCapitalPenalty? @lossOfCapitalPenalty;

        public ManpowerContent? Manpower => @manpower;
        [JsonProperty, CreateProperty]
        internal ManpowerContent? @manpower;

        public float? MarketFee => @marketFee;
        [JsonProperty, CreateProperty]
        internal float? @marketFee;

        public int MaxMoralePenaltyWars => @maxMoralePenaltyWars;
        [JsonProperty, CreateProperty]
        internal int @maxMoralePenaltyWars;

        public int MaxPlayerTokenSlots => @maxPlayerTokenSlots;
        [JsonProperty, CreateProperty]
        internal int @maxPlayerTokenSlots;

        public int MinPlayersRanked => @minPlayersRanked;
        [JsonProperty, CreateProperty]
        internal int @minPlayersRanked;

        public float? MoneyForPremium => @moneyForPremium;
        [JsonProperty, CreateProperty]
        internal float? @moneyForPremium;

        public IReadOnlyDictionary<float, float> MoraleBasedConstructionTime => @moraleBasedConstructionTime;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedConstructionTime = new();

        public IReadOnlyDictionary<float, float> MoraleBasedDmgFactor => @moraleBasedDmgFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedDmgFactor = new();

        public IReadOnlyDictionary<float, float> MoraleBasedProductionTime => @moraleBasedProductionTime;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedProductionTime = new();

        public IReadOnlyDictionary<float, float> MoraleBasedResourceProductionRate => @moraleBasedResourceProductionRate;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedResourceProductionRate = new();

        public IReadOnlyDictionary<float, float> MoraleBasedSpeedFactor => @moraleBasedSpeedFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedSpeedFactor = new();

        public int MoraleDamageOnConquer => @moraleDamageOnConquer;
        [JsonProperty, CreateProperty]
        internal int @moraleDamageOnConquer;

        public NewspaperContent? Newspaper => @newspaper;
        [JsonProperty, CreateProperty]
        internal NewspaperContent? @newspaper;

        public bool NoGoldRounds => @noGoldRounds;
        [JsonProperty, CreateProperty]
        internal bool @noGoldRounds;

        public float NoncoreProductionFactor => @noncoreProductionFactor;
        [JsonProperty, CreateProperty]
        internal float @noncoreProductionFactor;

        public IReadOnlyDictionary<float, float> NoobBonus => @noobBonus;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @noobBonus = new();

        public TimeSpan? OldestGameEventAge => @oldestGameEventAge;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @oldestGameEventAge;

        public TimeSpan? PatrolInterval => @patrolInterval;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @patrolInterval;

        public ContentItemId? PlaneTrainId => @planeTrainId;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @planeTrainId;

        public ContentItemId? PremiumAccountItem => @premiumAccountItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @premiumAccountItem;

        public TimeSpan? PremiumActionAfterAttackCooldown => @premiumActionAfterAttackCooldown;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @premiumActionAfterAttackCooldown;

        public ContentItemId PremiumBoughtCurrencyItem => @premiumBoughtCurrencyItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId @premiumBoughtCurrencyItem;

        public ContentItemId PremiumCurrencyItem => @premiumCurrencyItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId @premiumCurrencyItem;

        public IReadOnlyList<ContentItemId> PremiumOffers => @premiumOffers;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @premiumOffers = new();

        public int PremiumOrderDefaultAmount => @premiumOrderDefaultAmount;
        [JsonProperty, CreateProperty]
        internal int @premiumOrderDefaultAmount;

        public float PremiumOrderStartPrice => @premiumOrderStartPrice;
        [JsonProperty, CreateProperty]
        internal float @premiumOrderStartPrice;

        public IReadOnlyList<PremiumSpyJobType> PremiumSpyJobTypes => @premiumSpyJobTypes;
        [JsonProperty, CreateProperty]
        internal List<PremiumSpyJobType> @premiumSpyJobTypes = new();

        public IReadOnlyList<ContentItemId> Premiums => @premiums;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @premiums = new();

        public ProvinceMoraleContent ProvinceMorale => @provinceMorale;
        [JsonProperty, CreateProperty]
        internal ProvinceMoraleContent @provinceMorale = new();

        public int ProvinceNeighbourMoraleFactor => @provinceNeighbourMoraleFactor;
        [JsonProperty, CreateProperty]
        internal int @provinceNeighbourMoraleFactor;

        public int ProvinceNeighbourMoraleThreshold => @provinceNeighbourMoraleThreshold;
        [JsonProperty, CreateProperty]
        internal int @provinceNeighbourMoraleThreshold;

        public int ProvinceStartMorale => @provinceStartMorale;
        [JsonProperty, CreateProperty]
        internal int @provinceStartMorale;

        public IReadOnlyDictionary<GameEventType, int> PushNotifications => @pushNotifications;
        [JsonProperty, CreateProperty]
        internal Dictionary<GameEventType, int> @pushNotifications = new();

        public QuestContent? QuestConfig => @questConfig;
        [JsonProperty, CreateProperty]
        internal QuestContent? @questConfig;

        public IReadOnlyDictionary<float, float> RandomizedAiMood => @randomizedAiMood;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @randomizedAiMood = new();

        public IReadOnlyList<ContentItemId> Ranks => @ranks;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @ranks = new();

        public RealisticBuildingDamage? RealisticBuildingDamage => @realisticBuildingDamage;
        [JsonProperty, CreateProperty]
        internal RealisticBuildingDamage? @realisticBuildingDamage;

        public TimeSpan RefuelDuration => @refuelDuration;
        [JsonProperty, CreateProperty]
        internal TimeSpan @refuelDuration;

        public Relation? RelationAfterLeaveCoalition => @relationAfterLeaveCoalition;
        [JsonProperty, CreateProperty]
        internal Relation? @relationAfterLeaveCoalition;

        public IReadOnlyList<Relation> Relations => @relations;
        [JsonProperty, CreateProperty]
        internal List<Relation> @relations = new();

        public int ResearchSlots => @researchSlots;
        [JsonProperty, CreateProperty]
        internal int @researchSlots;

        public float? ResearchSpeedUpTimeFactor => @researchSpeedUpTimeFactor;
        [JsonProperty, CreateProperty]
        internal float? @researchSpeedUpTimeFactor;

        public IReadOnlyList<ContentItemId> Researches => @researches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @researches = new();

        public IReadOnlyDictionary<ResourceId, float> ResourceConsumption => @resourceConsumption;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @resourceConsumption = new();

        public IReadOnlyDictionary<ResourceId, ResourceContent> Resources => @resources;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, ResourceContent> @resources = new();

        public ContentItemId RewardOfferId => @rewardOfferId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @rewardOfferId;

        public IReadOnlyList<Faction> SelectableFactions => @selectableFactions;
        [JsonProperty, CreateProperty]
        internal List<Faction> @selectableFactions = new();

        public SeparateMoraleDamage? SeparateMoraleDamage => @separateMoraleDamage;
        [JsonProperty, CreateProperty]
        internal SeparateMoraleDamage? @separateMoraleDamage;

        public SoftCurrencyContent? SoftCurrency => @softCurrency;
        [JsonProperty, CreateProperty]
        internal SoftCurrencyContent? @softCurrency;

        public int SpyCost => @spyCost;
        [JsonProperty, CreateProperty]
        internal int @spyCost;

        public IReadOnlyDictionary<SpyMission, List<SpyJob>> SpyMissionJobs => @spyMissionJobs;
        [JsonProperty, CreateProperty]
        internal Dictionary<SpyMission, List<SpyJob>> @spyMissionJobs = new();

        public int StartSetup => @startSetup;
        [JsonProperty, CreateProperty]
        internal int @startSetup;

        public ContentItemId? StartingUnitTypeId => @startingUnitTypeId;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @startingUnitTypeId;

        public float StockMarketPriceWeight => @stockMarketPriceWeight;
        [JsonProperty, CreateProperty]
        internal float @stockMarketPriceWeight;

        public IReadOnlyDictionary<string, string> StringOptions => @stringOptions;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, string> @stringOptions = new();

        public IReadOnlyDictionary<int, SubFactionContent> SubFactions => @subFactions;
        [JsonProperty, CreateProperty]
        internal Dictionary<int, SubFactionContent> @subFactions = new();

        public float SuppressRevoltArmyStrength => @suppressRevoltArmyStrength;
        [JsonProperty, CreateProperty]
        internal float @suppressRevoltArmyStrength;

        public int? SystemGameGenerationScale => @systemGameGenerationScale;
        [JsonProperty, CreateProperty]
        internal int? @systemGameGenerationScale;

        public TimeSpan TeamJoinCDAfterKick => @teamJoinCDAfterKick;
        [JsonProperty, CreateProperty]
        internal TimeSpan @teamJoinCDAfterKick;

        public TimeSpan TeamJoinCDAfterLeave => @teamJoinCDAfterLeave;
        [JsonProperty, CreateProperty]
        internal TimeSpan @teamJoinCDAfterLeave;

        public IReadOnlyList<TerrainType> TerrainTypes => @terrainTypes;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @terrainTypes = new();

        public bool ToggleProvincePerTerrainType => @toggleProvincePerTerrainType;
        [JsonProperty, CreateProperty]
        internal bool @toggleProvincePerTerrainType;

        public IReadOnlyList<ContentItemId> Tokens => @tokens;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @tokens = new();

        public IReadOnlyList<ContentItemId> TokensForPlayerTokenSlots => @tokensForPlayerTokenSlots;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @tokensForPlayerTokenSlots = new();

        public ContentItemId? TransportShipId => @transportShipId;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @transportShipId;

        public int? TutorialFactory => @tutorialFactory;
        [JsonProperty, CreateProperty]
        internal int? @tutorialFactory;

        public int TutorialTaskGoldReward => @tutorialTaskGoldReward;
        [JsonProperty, CreateProperty]
        internal int @tutorialTaskGoldReward;

        public IReadOnlyList<ContentItemId> Units => @units;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @units = new();

        public IReadOnlyList<ContentItemId> Upgrades => @upgrades;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @upgrades = new();

        public bool UrbanProvinceListMode => @urbanProvinceListMode;
        [JsonProperty, CreateProperty]
        internal bool @urbanProvinceListMode;

        public bool UseStatsColumnIdForUnits => @useStatsColumnIdForUnits;
        [JsonProperty, CreateProperty]
        internal bool @useStatsColumnIdForUnits;

        public IReadOnlyList<string> VisualTerrainTypes => @visualTerrainTypes;
        [JsonProperty, CreateProperty]
        internal List<string> @visualTerrainTypes = new();

        [UnityEngine.Scripting.Preserve]
        public ModContent() { }
    }
}
