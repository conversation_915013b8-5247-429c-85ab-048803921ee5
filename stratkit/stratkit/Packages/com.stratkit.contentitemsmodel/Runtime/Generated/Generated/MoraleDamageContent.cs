using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class MoraleDamageContent {
        public float MoraleDamage => @moraleDamage;
        [JsonProperty, CreateProperty]
        internal float @moraleDamage;

        [UnityEngine.Scripting.Preserve]
        public MoraleDamageContent() { }
    }
}
