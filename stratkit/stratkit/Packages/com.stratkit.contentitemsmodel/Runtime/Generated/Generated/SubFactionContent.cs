using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class SubFactionContent {
        public Faction Faction => @faction;
        [JsonProperty, CreateProperty]
        internal Faction @faction;

        [UnityEngine.Scripting.Preserve]
        public SubFactionContent() { }
    }
}
