using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ArmyModifiersContent {
        public IReadOnlyDictionary<string, ArmyModifierSet> ArmyModifierSets => @armyModifierSets;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, ArmyModifierSet> @armyModifierSets = new();

        [UnityEngine.Scripting.Preserve]
        public ArmyModifiersContent() { }
    }
}
