using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitTypeRatingEntryContent {
        public bool Exceptional => @exceptional;
        [JsonProperty, CreateProperty]
        internal bool @exceptional;

        public int Order => @order;
        [JsonProperty, CreateProperty]
        internal int @order;

        public int RatingGroup => @ratingGroup;
        [JsonProperty, CreateProperty]
        internal int @ratingGroup;

        public float Value => @value;
        [JsonProperty, CreateProperty]
        internal float @value;

        [UnityEngine.Scripting.Preserve]
        public UnitTypeRatingEntryContent() { }
    }
}
