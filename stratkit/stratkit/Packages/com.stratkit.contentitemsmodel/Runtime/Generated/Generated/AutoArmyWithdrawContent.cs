using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AutoArmyWithdrawContent {
        public TimeSpan MaxWithdrawDuration => @maxWithdrawDuration;
        [JsonProperty, CreateProperty]
        internal TimeSpan @maxWithdrawDuration;

        public TimeSpan MinWithdrawDuration => @minWithdrawDuration;
        [JsonProperty, CreateProperty]
        internal TimeSpan @minWithdrawDuration;

        public IReadOnlyDictionary<float, float> SuppressionFactor => @suppressionFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @suppressionFactor = new();

        public float WithdrawBaseSpeed => @withdrawBaseSpeed;
        [JsonProperty, CreateProperty]
        internal float @withdrawBaseSpeed;

        [UnityEngine.Scripting.Preserve]
        public AutoArmyWithdrawContent() { }
    }
}
