using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class PremiumContent : IContent {
        public bool AllianceSynced => @allianceSynced;
        [JsonProperty, CreateProperty]
        internal bool @allianceSynced;

        public string Description => @description;
        [JsonProperty, CreateProperty]
        internal string @description = string.Empty;

        public ContentItemId? FirstLevelUpgradeItem => @firstLevelUpgradeItem;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @firstLevelUpgradeItem;

        public bool GlobalItem => @globalItem;
        [JsonProperty, CreateProperty]
        internal bool @globalItem;

        public bool HasDuration => @hasDuration;
        [JsonProperty, CreateProperty]
        internal bool @hasDuration;

        public bool Hidden => @hidden;
        [JsonProperty, CreateProperty]
        internal bool @hidden;

        public float? HitpointBoost => @hitpointBoost;
        [JsonProperty, CreateProperty]
        internal float? @hitpointBoost;

        public string Icon => @icon;
        [JsonProperty, CreateProperty]
        internal string @icon = string.Empty;

        public string IncludedPremiums => @includedPremiums;
        [JsonProperty, CreateProperty]
        internal string @includedPremiums = string.Empty;

        public bool InventoryItem => @inventoryItem;
        [JsonProperty, CreateProperty]
        internal bool @inventoryItem;

        public InventorySystemItemContent? InventorySystemItem => @inventorySystemItem;
        [JsonProperty, CreateProperty]
        internal InventorySystemItemContent? @inventorySystemItem;

        public int MaxQuantity => @maxQuantity;
        [JsonProperty, CreateProperty]
        internal int @maxQuantity;

        public float? MoraleBoost => @moraleBoost;
        [JsonProperty, CreateProperty]
        internal float? @moraleBoost;

        public string Name => @name;
        [JsonProperty, CreateProperty]
        internal string @name = string.Empty;

        public ContentItemId? OfferID => @offerID;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @offerID;

        public PremiumCollectible? PremiumCollectible => @premiumCollectible;
        [JsonProperty, CreateProperty]
        internal PremiumCollectible? @premiumCollectible;

        public PremiumType? PremiumType => @premiumType;
        [JsonProperty, CreateProperty]
        internal PremiumType? @premiumType;

        public ContentItemId? PreviousPremiumItemId => @previousPremiumItemId;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @previousPremiumItemId;

        public bool PublicContent => @publicContent;
        [JsonProperty, CreateProperty]
        internal bool @publicContent;

        public ResourceId? ResourceID => @resourceID;
        [JsonProperty, CreateProperty]
        internal ResourceId? @resourceID;

        public TimeSpan? SkipConstructionTime => @skipConstructionTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @skipConstructionTime;

        public TimeSpan? SkipProductionTime => @skipProductionTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @skipProductionTime;

        public TimeSpan? SkipResearchTime => @skipResearchTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @skipResearchTime;

        public ContentItemId? UnitID => @unitID;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @unitID;

        [UnityEngine.Scripting.Preserve]
        public PremiumContent() { }
    }
}
