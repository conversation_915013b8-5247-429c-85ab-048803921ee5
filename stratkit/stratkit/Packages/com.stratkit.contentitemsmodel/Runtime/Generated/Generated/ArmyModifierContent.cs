using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ArmyModifierContent {
        public IReadOnlyList<ContentItemId> AffectedUnitTypes => @affectedUnitTypes;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @affectedUnitTypes = new();

        public float ArmyVisionFactorModifier => @armyVisionFactorModifier;
        [JsonProperty, CreateProperty]
        internal float @armyVisionFactorModifier;

        public float ArmyVisionFlatModifier => @armyVisionFlatModifier;
        [JsonProperty, CreateProperty]
        internal float @armyVisionFlatModifier;

        public float AttackFactorModifier => @attackFactorModifier;
        [JsonProperty, CreateProperty]
        internal float @attackFactorModifier;

        public float DefenceFactorModifier => @defenceFactorModifier;
        [JsonProperty, CreateProperty]
        internal float @defenceFactorModifier;

        public DeployUnitContent? DeployUnit => @deployUnit;
        [JsonProperty, CreateProperty]
        internal DeployUnitContent? @deployUnit;

        public float DeployableUnitDamageAreaRef => @deployableUnitDamageAreaRef;
        [JsonProperty, CreateProperty]
        internal float @deployableUnitDamageAreaRef;

        public float DeployableUnitDamageRef => @deployableUnitDamageRef;
        [JsonProperty, CreateProperty]
        internal float @deployableUnitDamageRef;

        public IReadOnlyList<ContentItemId> EnablingOpponentUnitTypes => @enablingOpponentUnitTypes;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @enablingOpponentUnitTypes = new();

        public IReadOnlyList<TerritoryType> EnablingTerritoryTypes => @enablingTerritoryTypes;
        [JsonProperty, CreateProperty]
        internal List<TerritoryType> @enablingTerritoryTypes = new();

        public float HitpointsFactorModifier => @hitpointsFactorModifier;
        [JsonProperty, CreateProperty]
        internal float @hitpointsFactorModifier;

        public float SpeedFactorModifier => @speedFactorModifier;
        [JsonProperty, CreateProperty]
        internal float @speedFactorModifier;

        public float SpeedFlatModifier => @speedFlatModifier;
        [JsonProperty, CreateProperty]
        internal float @speedFlatModifier;

        public StackStrategy? StackStrategy => @stackStrategy;
        [JsonProperty, CreateProperty]
        internal StackStrategy? @stackStrategy;

        [UnityEngine.Scripting.Preserve]
        public ArmyModifierContent() { }
    }
}
