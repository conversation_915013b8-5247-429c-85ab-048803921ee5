using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitRevoltSpawnContent {
        public float Amount => @amount;
        [JsonProperty, CreateProperty]
        internal float @amount;

        public float Spread => @spread;
        [JsonProperty, CreateProperty]
        internal float @spread;

        [UnityEngine.Scripting.Preserve]
        public UnitRevoltSpawnContent() { }
    }
}
