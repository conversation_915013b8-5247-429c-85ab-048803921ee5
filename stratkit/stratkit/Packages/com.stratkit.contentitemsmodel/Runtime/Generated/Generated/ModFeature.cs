using System.Runtime.Serialization;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode]
    public enum ModFeature {
        [EnumMember(Value = "RESOURCES")]
        Resources = 1,
        [EnumMember(Value = "PROVINCE_MORALE")]
        ProvinceMorale = 2,
        [EnumMember(Value = "STOCK_MARKET")]
        StockMarket = 3,
        [EnumMember(Value = "SPIES")]
        Spies = 5,
        [EnumMember(Value = "BUILD_AND_PRODUCE")]
        BuildAndProduce = 6,
        [EnumMember(Value = "INCREMENTAL_TECHTREE")]
        IncrementalTechtree = 7,
        [EnumMember(Value = "CAPITAL")]
        Capital = 8,
        [EnumMember(Value = "PROVINCE_VIEW")]
        ProvinceView = 9,
        [EnumMember(Value = "FOW")]
        Fow = 10,
        [EnumMember(Value = "PREMIUM_RESOURCE_OFFERS")]
        PremiumResourceOffers = 16,
        [EnumMember(Value = "ALLIED_FORTRESS")]
        AlliedFortress = 22,
        [EnumMember(Value = "CUSTOM_EMBARK_TIMES")]
        CustomEmbarkTimes = 26,
        [EnumMember(Value = "DAMAGE_AREA")]
        DamageArea = 30,
        [EnumMember(Value = "SOFT_IP_CONFLICT_WARNING")]
        SoftIpConflictWarning = 33,
        [EnumMember(Value = "IN_GAME_ADS")]
        InGameAds = 36,
        [EnumMember(Value = "TARGETED_MARKETING")]
        TargetedMarketing = 37,
        [EnumMember(Value = "SCALABLE_OFFERS")]
        ScalableOffers = 38,
        [EnumMember(Value = "VICTORY_POINTS")]
        VictoryPoints = 39,
        [EnumMember(Value = "CORE_PROVINCES")]
        CoreProvinces = 40,
        [EnumMember(Value = "RESEARCH")]
        Research = 41,
        [EnumMember(Value = "FACTIONS")]
        Factions = 42,
        [EnumMember(Value = "MANPOWER")]
        Manpower = 43,
        [EnumMember(Value = "UPGRADES_INHERIT")]
        UpgradesInherit = 46,
        [EnumMember(Value = "STRATEGIC_ADVISER")]
        StrategicAdviser = 47,
        [EnumMember(Value = "AIRPLANES_WW2")]
        AirplanesWw2 = 48,
        [EnumMember(Value = "REPORT_ALL_UNIT_LOSSES")]
        ReportAllUnitLosses = 50,
        [EnumMember(Value = "GROWTH_HACKING")]
        GrowthHacking = 51,
        [EnumMember(Value = "INGAME_EVENTS")]
        IngameEvents = 53,
        [EnumMember(Value = "LESS_VARIANCE_BATTLE_SYSTEM")]
        LessVarianceBattleSystem = 54,
        [EnumMember(Value = "FRONT_WIDTH")]
        FrontWidth = 59,
        [EnumMember(Value = "PROVINCE_CONQUER_QUESTS")]
        ProvinceConquerQuests = 60,
        [EnumMember(Value = "MORALE_BASED_CONSTRUCTION_TIME")]
        MoraleBasedConstructionTime = 63,
        [EnumMember(Value = "RANDOMIZED_RESOURCES")]
        RandomizedResources = 65,
        [EnumMember(Value = "COALITION")]
        Coalition = 67,
        [EnumMember(Value = "MORALE_BASED_PRODUCTION_TIME")]
        MoraleBasedProductionTime = 68,
        [EnumMember(Value = "IMMUTABLE_PROVINCE")]
        ImmutableProvince = 79,
        [EnumMember(Value = "NEUTRAL_PROVINCES")]
        NeutralProvinces = 85,
        [EnumMember(Value = "EXPANSION_PENALTY")]
        ExpansionPenalty = 86,
        [EnumMember(Value = "EQUAL_DAMAGE_DISTRIBUTION")]
        EqualDamageDistribution = 87,
        [EnumMember(Value = "MORALE_BASED_RESOURCE_PRODUCTION_RATE")]
        MoraleBasedResourceProductionRate = 89,
        [EnumMember(Value = "FREE_FIRE_CONTROL")]
        FreeFireControl = 90,
        [EnumMember(Value = "CAPITAL_DISTANCE_PENALTY")]
        CapitalDistancePenalty = 91,
        [EnumMember(Value = "MORALE_BASED_DMG_FACTOR")]
        MoraleBasedDmgFactor = 92,
        [EnumMember(Value = "MORALE_BASED_SPEED_FACTOR")]
        MoraleBasedSpeedFactor = 93,
        [EnumMember(Value = "FLOOR_SPACE_PENALTY")]
        FloorSpacePenalty = 94,
        [EnumMember(Value = "CONFIGURED_DEFENSIVE_AI")]
        ConfiguredDefensiveAi = 95,
        [EnumMember(Value = "RESET_MINIMAL_PLAYER_RESOURCES_ON_JOIN")]
        ResetMinimalPlayerResourcesOnJoin = 96,
        [EnumMember(Value = "BLOCK_ATTACKED_AI_SLOTS_FOR_PLAYERS")]
        BlockAttackedAiSlotsForPlayers = 97,
        [EnumMember(Value = "NATIVE_AI_NOT_PICKABLE_IN_TEAM_MODE")]
        NativeAiNotPickableInTeamMode = 98,
        [EnumMember(Value = "NOOB_BONUS")]
        NoobBonus = 100,
        [EnumMember(Value = "EFFECTIVE_RANKING_REWARD_SYSTEM")]
        EffectiveRankingRewardSystem = 101,
        [EnumMember(Value = "POINTS_AS_REWARD_AMOUNT")]
        PointsAsRewardAmount = 102,
        [EnumMember(Value = "ADVANCED_URBAN_PROVINCE_LOOK")]
        AdvancedUrbanProvinceLook = 110,
        [EnumMember(Value = "FACTION_SPECIFIC_BALANCING")]
        FactionSpecificBalancing = 111,
        [EnumMember(Value = "RANDOMIZED_AI_MOOD")]
        RandomizedAiMood = 112,
        [EnumMember(Value = "SCOUTABLE_PROVINCES")]
        ScoutableProvinces = 113,
        [EnumMember(Value = "FOCUS_BUILDING_AND_MORALE_DAMAGE_ON_PROVINCE_CENTRE")]
        FocusBuildingAndMoraleDamageOnProvinceCentre = 114,
        [EnumMember(Value = "MANUAL_ARMY_JOIN")]
        ManualArmyJoin = 115,
        [EnumMember(Value = "DESTROY_UPGRADE_ON_CANCEL")]
        DestroyUpgradeOnCancel = 116,
        [EnumMember(Value = "IGNORE_MAX_AMOUNT_FOR_NON_PRODUCED_RESOURCES")]
        IgnoreMaxAmountForNonProducedResources = 117,
        [EnumMember(Value = "MONEY_AS_CONFIGURED_RESOURCE_PRODUCTION")]
        MoneyAsConfiguredResourceProduction = 118,
        [EnumMember(Value = "STOP_ARMY_NO_RETURN_TO_SNAPPING_POINT")]
        StopArmyNoReturnToSnappingPoint = 119,
        [EnumMember(Value = "PROVINCE_OWNER_CHANGE_CANCELS_UNIT_PRODUCTION")]
        ProvinceOwnerChangeCancelsUnitProduction = 120,
        [EnumMember(Value = "RESTRICTED_RAILROAD_USAGE")]
        RestrictedRailroadUsage = 121,
    }
}
