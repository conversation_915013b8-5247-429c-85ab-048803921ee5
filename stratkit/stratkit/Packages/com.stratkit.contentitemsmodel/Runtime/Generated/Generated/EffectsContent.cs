using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class EffectsContent {
        public IReadOnlyDictionary<string, ArmyModifierContent> ArmyEffects => @armyEffects;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, ArmyModifierContent> @armyEffects = new();

        public IReadOnlyDictionary<string, ProvinceEffectContent> ProvinceEffects => @provinceEffects;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, ProvinceEffectContent> @provinceEffects = new();

        [UnityEngine.Scripting.Preserve]
        public EffectsContent() { }
    }
}
