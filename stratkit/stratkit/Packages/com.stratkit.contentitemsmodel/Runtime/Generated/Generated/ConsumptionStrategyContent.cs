using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ConsumptionStrategyContent {
        public ConsumptionBehavior Behavior => @behavior;
        [JsonProperty, CreateProperty]
        internal ConsumptionBehavior @behavior;

        public IReadOnlyList<ConsumptionEvent> ConsumptionEvents => @consumptionEvents;
        [JsonProperty, CreateProperty]
        internal List<ConsumptionEvent> @consumptionEvents = new();

        public ConsumptionInsufficientRule InsufficientRule => @insufficientRule;
        [JsonProperty, CreateProperty]
        internal ConsumptionInsufficientRule @insufficientRule;

        [UnityEngine.Scripting.Preserve]
        public ConsumptionStrategyContent() { }
    }
}
