using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class CollectibleRewardOfferContent {
        public int DisplayOrderId => @displayOrderId;
        [JsonProperty, CreateProperty]
        internal int @displayOrderId;

        public int RewardShopOfferId => @rewardShopOfferId;
        [JsonProperty, CreateProperty]
        internal int @rewardShopOfferId;

        [UnityEngine.Scripting.Preserve]
        public CollectibleRewardOfferContent() { }
    }
}
