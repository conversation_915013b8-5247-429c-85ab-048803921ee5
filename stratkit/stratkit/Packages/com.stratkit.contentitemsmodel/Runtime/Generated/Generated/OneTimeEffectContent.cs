using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class OneTimeEffectContent {
        public DeployUnitEffectContent? DeployUnitEffect => @deployUnitEffect;
        [JsonProperty, CreateProperty]
        internal DeployUnitEffectContent? @deployUnitEffect;

        [UnityEngine.Scripting.Preserve]
        public OneTimeEffectContent() { }
    }
}
