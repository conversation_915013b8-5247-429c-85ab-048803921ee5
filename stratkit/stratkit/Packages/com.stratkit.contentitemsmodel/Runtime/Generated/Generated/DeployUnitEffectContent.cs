using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DeployUnitEffectContent {
        public IReadOnlyDictionary<string, MapRestrictionContent> DeployRestrictions => @deployRestrictions;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, MapRestrictionContent> @deployRestrictions = new();

        public TimeSpan DeployTime => @deployTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @deployTime;

        public IReadOnlyDictionary<ContentItemId, int> DeployUnits => @deployUnits;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @deployUnits = new();

        [UnityEngine.Scripting.Preserve]
        public DeployUnitEffectContent() { }
    }
}
