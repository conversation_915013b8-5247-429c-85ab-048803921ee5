using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class RequiredItemEffects {
        public ContentItemId RequiredInventoryItemId => @requiredInventoryItemId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @requiredInventoryItemId;

        public TimeSpan? ResurrectionCooldown => @resurrectionCooldown;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @resurrectionCooldown;

        public IReadOnlyDictionary<ContentItemId, int> Units => @units;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @units = new();

        [UnityEngine.Scripting.Preserve]
        public RequiredItemEffects() { }
    }
}
