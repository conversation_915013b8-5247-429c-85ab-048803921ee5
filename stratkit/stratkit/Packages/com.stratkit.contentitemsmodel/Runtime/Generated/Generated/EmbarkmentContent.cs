using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class EmbarkmentContent {
        public TimeSpan EnemyEmbarkmentTime => @enemyEmbarkmentTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @enemyEmbarkmentTime;

        public TimeSpan FriendlyEmbarkmentTime => @friendlyEmbarkmentTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @friendlyEmbarkmentTime;

        [UnityEngine.Scripting.Preserve]
        public EmbarkmentContent() { }
    }
}
