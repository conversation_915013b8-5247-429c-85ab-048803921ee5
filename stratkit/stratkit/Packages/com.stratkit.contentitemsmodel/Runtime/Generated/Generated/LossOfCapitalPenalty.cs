using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class LossOfCapitalPenalty {
        public float CaptureCapitalMoneyBonus => @captureCapitalMoneyBonus;
        [JsonProperty, CreateProperty]
        internal float @captureCapitalMoneyBonus;

        public int CaptureCapitalMoralBonus => @captureCapitalMoralBonus;
        [JsonProperty, CreateProperty]
        internal int @captureCapitalMoralBonus;

        public float LossOfCapitalMoneyPenalty => @lossOfCapitalMoneyPenalty;
        [JsonProperty, CreateProperty]
        internal float @lossOfCapitalMoneyPenalty;

        public int LossOfCapitalMoralPenalty => @lossOfCapitalMoralPenalty;
        [JsonProperty, CreateProperty]
        internal int @lossOfCapitalMoralPenalty;

        [UnityEngine.Scripting.Preserve]
        public LossOfCapitalPenalty() { }
    }
}
