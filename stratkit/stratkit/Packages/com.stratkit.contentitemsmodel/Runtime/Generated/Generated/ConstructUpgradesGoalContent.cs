using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ConstructUpgradesGoalContent {
        public int NumberOfUpgrades => @numberOfUpgrades;
        [JsonProperty, CreateProperty]
        internal int @numberOfUpgrades;

        [UnityEngine.Scripting.Preserve]
        public ConstructUpgradesGoalContent() { }
    }
}
