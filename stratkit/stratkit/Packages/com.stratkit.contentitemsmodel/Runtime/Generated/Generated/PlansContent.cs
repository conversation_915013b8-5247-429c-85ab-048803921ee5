using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class PlansContent {
        public int MaxAmount => @maxAmount;
        [JsonProperty, CreateProperty]
        internal int @maxAmount;

        public int MinAmount => @minAmount;
        [JsonProperty, CreateProperty]
        internal int @minAmount;

        [UnityEngine.Scripting.Preserve]
        public PlansContent() { }
    }
}
