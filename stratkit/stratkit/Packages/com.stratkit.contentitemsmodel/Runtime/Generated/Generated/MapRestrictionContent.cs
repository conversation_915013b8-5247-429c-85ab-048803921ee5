using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class MapRestrictionContent {
        public CombatRestrictionContent? Combat => @combat;
        [JsonProperty, CreateProperty]
        internal CombatRestrictionContent? @combat;

        public bool IgnoreFogOfWar => @ignoreFogOfWar;
        [JsonProperty, CreateProperty]
        internal bool @ignoreFogOfWar;

        public IReadOnlyList<OwnershipRestriction> Ownership => @ownership;
        [JsonProperty, CreateProperty]
        internal List<OwnershipRestriction> @ownership = new();

        public IReadOnlyList<PositionRestriction> Path => @path;
        [JsonProperty, CreateProperty]
        internal List<PositionRestriction> @path = new();

        public IReadOnlyList<TerrainType> TerrainType => @terrainType;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @terrainType = new();

        [UnityEngine.Scripting.Preserve]
        public MapRestrictionContent() { }
    }
}
