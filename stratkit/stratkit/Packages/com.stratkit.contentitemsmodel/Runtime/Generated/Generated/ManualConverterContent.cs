using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ManualConverterContent {
        public TimeSpan ConversionTime => @conversionTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @conversionTime;

        public ContentItemId? CooldownToken => @cooldownToken;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @cooldownToken;

        public IReadOnlyDictionary<ResourceId, int> Cost => @cost;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, int> @cost = new();

        public ContentItemId SpawnedUnitTypeId => @spawnedUnitTypeId;
        [JsonProperty, CreateProperty]
        internal ContentItemId @spawnedUnitTypeId;

        [UnityEngine.Scripting.Preserve]
        public ManualConverterContent() { }
    }
}
