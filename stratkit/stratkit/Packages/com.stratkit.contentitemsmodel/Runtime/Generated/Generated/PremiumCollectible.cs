using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class PremiumCollectible {
        public CollectibleType CollectibleType => @collectibleType;
        [JsonProperty, CreateProperty]
        internal CollectibleType @collectibleType;

        public PremiumCollectibleRarity Rarity => @rarity;
        [JsonProperty, CreateProperty]
        internal PremiumCollectibleRarity @rarity;

        [UnityEngine.Scripting.Preserve]
        public PremiumCollectible() { }
    }
}
