using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class OwnSpecificProvincesGoalContent {
        public float Distance => @distance;
        [JsonProperty, CreateProperty]
        internal float @distance;

        public int NumberOfProvinces => @numberOfProvinces;
        [JsonProperty, CreateProperty]
        internal int @numberOfProvinces;

        public int Owners => @owners;
        [JsonProperty, CreateProperty]
        internal int @owners;

        [UnityEngine.Scripting.Preserve]
        public OwnSpecificProvincesGoalContent() { }
    }
}
