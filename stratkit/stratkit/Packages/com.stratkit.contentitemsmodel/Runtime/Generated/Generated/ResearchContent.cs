using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class ResearchContent : IContent {
        public float AiResearchFactor => @aiResearchFactor;
        [JsonProperty, CreateProperty]
        internal float @aiResearchFactor;

        public TimeSpan BuildTime => @buildTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @buildTime;

        public IReadOnlyDictionary<ResourceId, float> Costs => @costs;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @costs = new();

        public int DayAvailable => @dayAvailable;
        [JsonProperty, CreateProperty]
        internal int @dayAvailable;

        public string Desc => @desc;
        [JsonProperty, CreateProperty]
        internal string @desc = string.Empty;

        public ContentItemId? FactionBaseItemID => @factionBaseItemID;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @factionBaseItemID;

        public IReadOnlyList<Faction> Factions => @factions;
        [JsonProperty, CreateProperty]
        internal List<Faction> @factions = new();

        public string Identifier => @identifier;
        [JsonProperty, CreateProperty]
        internal string @identifier = string.Empty;

        public string Name => @name;
        [JsonProperty, CreateProperty]
        internal string @name = string.Empty;

        public ContentItemId? ReplacedResearch => @replacedResearch;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @replacedResearch;

        public IReadOnlyDictionary<ContentItemId, int> RequiredPlans => @requiredPlans;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @requiredPlans = new();

        public IReadOnlyList<ContentItemId> RequiredResearches => @requiredResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @requiredResearches = new();

        public string RequirementExpression => @requirementExpression;
        [JsonProperty, CreateProperty]
        internal string @requirementExpression = string.Empty;

        public int Set => @set;
        [JsonProperty, CreateProperty]
        internal int @set;

        public int SetOrderID => @setOrderID;
        [JsonProperty, CreateProperty]
        internal int @setOrderID;

        public int TrackingOptionID => @trackingOptionID;
        [JsonProperty, CreateProperty]
        internal int @trackingOptionID;

        public bool TransportShip => @transportShip;
        [JsonProperty, CreateProperty]
        internal bool @transportShip;

        public int UnitPack => @unitPack;
        [JsonProperty, CreateProperty]
        internal int @unitPack;

        public string UnlockedMaxLevel => @unlockedMaxLevel;
        [JsonProperty, CreateProperty]
        internal string @unlockedMaxLevel = string.Empty;

        public string UpgradeGroup => @upgradeGroup;
        [JsonProperty, CreateProperty]
        internal string @upgradeGroup = string.Empty;

        [UnityEngine.Scripting.Preserve]
        public ResearchContent() { }
    }
}
