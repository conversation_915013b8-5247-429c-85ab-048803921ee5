using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitTypeRatingConfigContent {
        public bool EpicUnit => @epicUnit;
        [JsonProperty, CreateProperty]
        internal bool @epicUnit;

        public float PowerLevel => @powerLevel;
        [JsonProperty, CreateProperty]
        internal float @powerLevel;

        public IReadOnlyDictionary<UnitTypeRatingEntryEnum, UnitTypeRatingEntryContent> RatingEntryMap => @ratingEntryMap;
        [JsonProperty, CreateProperty]
        internal Dictionary<UnitTypeRatingEntryEnum, UnitTypeRatingEntryContent> @ratingEntryMap = new();

        public int RoleID => @roleID;
        [JsonProperty, CreateProperty]
        internal int @roleID;

        public IReadOnlyList<UnitTypeRole> UnitRoles => @unitRoles;
        [JsonProperty, CreateProperty]
        internal List<UnitTypeRole> @unitRoles = new();

        [UnityEngine.Scripting.Preserve]
        public UnitTypeRatingConfigContent() { }
    }
}
