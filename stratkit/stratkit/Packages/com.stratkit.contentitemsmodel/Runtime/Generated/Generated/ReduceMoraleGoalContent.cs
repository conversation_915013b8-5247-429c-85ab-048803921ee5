using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ReduceMoraleGoalContent {
        public int MoralePoints => @moralePoints;
        [JsonProperty, CreateProperty]
        internal int @moralePoints;

        [UnityEngine.Scripting.Preserve]
        public ReduceMoraleGoalContent() { }
    }
}
