using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class UnitContent : IContent {
        public AdditionalProductionContent? AdditionalProduction => @additionalProduction;
        [JsonProperty, CreateProperty]
        internal AdditionalProductionContent? @additionalProduction;

        public AirplaneContent? Airplane => @airplane;
        [JsonProperty, CreateProperty]
        internal AirplaneContent? @airplane;

        public IReadOnlyDictionary<BaseTerrainType, float> AntiAirRanges => @antiAirRanges;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @antiAirRanges = new();

        public ArmyModifiersContent? ArmyModifiersConfig => @armyModifiersConfig;
        [JsonProperty, CreateProperty]
        internal ArmyModifiersContent? @armyModifiersConfig;

        public bool AttackAllowed => @attackAllowed;
        [JsonProperty, CreateProperty]
        internal bool @attackAllowed;

        public string AttackPainter => @attackPainter;
        [JsonProperty, CreateProperty]
        internal string @attackPainter = string.Empty;

        public AutoRetreatContent? AutoRetreat => @autoRetreat;
        [JsonProperty, CreateProperty]
        internal AutoRetreatContent? @autoRetreat;

        public TimeSpan BuildTime => @buildTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan @buildTime;

        public IReadOnlyList<TerrainType> Camouflage => @camouflage;
        [JsonProperty, CreateProperty]
        internal List<TerrainType> @camouflage = new();

        public string CombatSoundSet => @combatSoundSet;
        [JsonProperty, CreateProperty]
        internal string @combatSoundSet = string.Empty;

        public bool Conquerer => @conquerer;
        [JsonProperty, CreateProperty]
        internal bool @conquerer;

        public ManualConverterContent? ConvertManually => @convertManually;
        [JsonProperty, CreateProperty]
        internal ManualConverterContent? @convertManually;

        public IReadOnlyDictionary<ResourceId, float> Costs => @costs;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @costs = new();

        public ContentItemId? CustomSeaUnitTypeId => @customSeaUnitTypeId;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @customSeaUnitTypeId;

        public IReadOnlyDictionary<ResourceId, float> DailyCosts => @dailyCosts;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, float> @dailyCosts = new();

        public IReadOnlyDictionary<BaseTerrainType, float> DamageArea => @damageArea;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @damageArea = new();

        public IReadOnlyDictionary<TerrainType, float> DamageAreaFactors => @damageAreaFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<TerrainType, float> @damageAreaFactors = new();

        public DamageAuraContent? DamageAura => @damageAura;
        [JsonProperty, CreateProperty]
        internal DamageAuraContent? @damageAura;

        public IReadOnlyDictionary<DamageType, float> DamageFactors => @damageFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<DamageType, float> @damageFactors = new();

        public IReadOnlyDictionary<BaseTerrainType, float> DamageTypes => @damageTypes;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @damageTypes = new();

        public float? DeathThreshold => @deathThreshold;
        [JsonProperty, CreateProperty]
        internal float? @deathThreshold;

        public IReadOnlyDictionary<BaseTerrainType, float> Defence => @defence;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @defence = new();

        public IReadOnlyDictionary<TerrainType, float> DefenceFactors => @defenceFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<TerrainType, float> @defenceFactors = new();

        public DeployUnitContent? DeployUnit => @deployUnit;
        [JsonProperty, CreateProperty]
        internal DeployUnitContent? @deployUnit;

        public EmbarkmentContent? Embarkment => @embarkment;
        [JsonProperty, CreateProperty]
        internal EmbarkmentContent? @embarkment;

        public TimeSpan? Expirable => @expirable;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @expirable;

        public ContentItemId? FactionBaseItemID => @factionBaseItemID;
        [JsonProperty, CreateProperty]
        internal ContentItemId? @factionBaseItemID;

        public IReadOnlyList<Faction> Factions => @factions;
        [JsonProperty, CreateProperty]
        internal List<Faction> @factions = new();

        public float? FirstStrikeFactor => @firstStrikeFactor;
        [JsonProperty, CreateProperty]
        internal float? @firstStrikeFactor;

        public bool FlyingTarget => @flyingTarget;
        [JsonProperty, CreateProperty]
        internal bool @flyingTarget;

        public double ForeignSpeedFactor => @foreignSpeedFactor;
        [JsonProperty, CreateProperty]
        internal double @foreignSpeedFactor;

        public string FormationNameBig => @formationNameBig;
        [JsonProperty, CreateProperty]
        internal string @formationNameBig = string.Empty;

        public string FormationNameSmall => @formationNameSmall;
        [JsonProperty, CreateProperty]
        internal string @formationNameSmall = string.Empty;

        public double FriendlySpeedFactor => @friendlySpeedFactor;
        [JsonProperty, CreateProperty]
        internal double @friendlySpeedFactor;

        public bool Garrison => @garrison;
        [JsonProperty, CreateProperty]
        internal bool @garrison;

        public IReadOnlyDictionary<BaseTerrainType, float> HitPoints => @hitPoints;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @hitPoints = new();

        public IReadOnlyDictionary<float, float> HitpointSizeFactors => @hitpointSizeFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @hitpointSizeFactors = new();

        public string Identifier => @identifier;
        [JsonProperty, CreateProperty]
        internal string @identifier = string.Empty;

        public IReadOnlyDictionary<string, int> Images => @images;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, int> @images = new();

        public string IngameDesc => @ingameDesc;
        [JsonProperty, CreateProperty]
        internal string @ingameDesc = string.Empty;

        public string IngameName => @ingameName;
        [JsonProperty, CreateProperty]
        internal string @ingameName = string.Empty;

        public IReadOnlyDictionary<float, float> LifestealHitpoints => @lifestealHitpoints;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @lifestealHitpoints = new();

        public IReadOnlyList<int> MergeGroupId => @mergeGroupId;
        [JsonProperty, CreateProperty]
        internal List<int> @mergeGroupId = new();

        public TimeSpan? MinProductionTime => @minProductionTime;
        [JsonProperty, CreateProperty]
        internal TimeSpan? @minProductionTime;

        public double MinimumTechLevel => @minimumTechLevel;
        [JsonProperty, CreateProperty]
        internal double @minimumTechLevel;

        public IReadOnlyDictionary<string, UnitMobilizationContent> MobilizationContent => @mobilizationContent;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, UnitMobilizationContent> @mobilizationContent = new();

        public IReadOnlyDictionary<float, float> MoraleBasedDmgFactor => @moraleBasedDmgFactor;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @moraleBasedDmgFactor = new();

        public MoraleDamageContent? MoraleDamage => @moraleDamage;
        [JsonProperty, CreateProperty]
        internal MoraleDamageContent? @moraleDamage;

        public UnitMoraleDecayContent? MoraleDecay => @moraleDecay;
        [JsonProperty, CreateProperty]
        internal UnitMoraleDecayContent? @moraleDecay;

        public string Name => @name;
        [JsonProperty, CreateProperty]
        internal string @name = string.Empty;

        public string NameFaction1 => @nameFaction1;
        [JsonProperty, CreateProperty]
        internal string @nameFaction1 = string.Empty;

        public string NameFaction2 => @nameFaction2;
        [JsonProperty, CreateProperty]
        internal string @nameFaction2 = string.Empty;

        public string NameFaction3 => @nameFaction3;
        [JsonProperty, CreateProperty]
        internal string @nameFaction3 = string.Empty;

        public string NameFaction4 => @nameFaction4;
        [JsonProperty, CreateProperty]
        internal string @nameFaction4 = string.Empty;

        public string PinPainter => @pinPainter;
        [JsonProperty, CreateProperty]
        internal string @pinPainter = string.Empty;

        public IReadOnlyDictionary<BaseTerrainType, float> Ranges => @ranges;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @ranges = new();

        public double RankingFactor => @rankingFactor;
        [JsonProperty, CreateProperty]
        internal double @rankingFactor;

        public UnitTypeRatingConfigContent? RatingConfig => @ratingConfig;
        [JsonProperty, CreateProperty]
        internal UnitTypeRatingConfigContent? @ratingConfig;

        public IReadOnlyList<ContentItemId> RequiredResearches => @requiredResearches;
        [JsonProperty, CreateProperty]
        internal List<ContentItemId> @requiredResearches = new();

        public IReadOnlyDictionary<ContentItemId, float> RequiredUpgrades => @requiredUpgrades;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @requiredUpgrades = new();

        public RetreatSuppression? RetreatSuppression => @retreatSuppression;
        [JsonProperty, CreateProperty]
        internal RetreatSuppression? @retreatSuppression;

        public ScoutConfigContent? ScoutConfig => @scoutConfig;
        [JsonProperty, CreateProperty]
        internal ScoutConfigContent? @scoutConfig;

        public int Set => @set;
        [JsonProperty, CreateProperty]
        internal int @set;

        public bool ShowProductionInNews => @showProductionInNews;
        [JsonProperty, CreateProperty]
        internal bool @showProductionInNews;

        public int? SkinTierId => @skinTierId;
        [JsonProperty, CreateProperty]
        internal int? @skinTierId;

        public string SoundSet => @soundSet;
        [JsonProperty, CreateProperty]
        internal string @soundSet = string.Empty;

        public UnitSpawnerContent? SpawnUnit => @spawnUnit;
        [JsonProperty, CreateProperty]
        internal UnitSpawnerContent? @spawnUnit;

        public IReadOnlyDictionary<TerrainType, float> SpeedFactors => @speedFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<TerrainType, float> @speedFactors = new();

        public IReadOnlyDictionary<BaseTerrainType, float> Speeds => @speeds;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @speeds = new();

        public int StatsColumnID => @statsColumnID;
        [JsonProperty, CreateProperty]
        internal int @statsColumnID;

        public StealthConfigContent? StealthConfig => @stealthConfig;
        [JsonProperty, CreateProperty]
        internal StealthConfigContent? @stealthConfig;

        public IReadOnlyDictionary<BaseTerrainType, float> Strength => @strength;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @strength = new();

        public IReadOnlyDictionary<TerrainType, float> StrengthFactors => @strengthFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<TerrainType, float> @strengthFactors = new();

        public IReadOnlyDictionary<float, float> StrengthSizeFactors => @strengthSizeFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @strengthSizeFactors = new();

        public bool SuppressingRevolts => @suppressingRevolts;
        [JsonProperty, CreateProperty]
        internal bool @suppressingRevolts;

        public int Tier => @tier;
        [JsonProperty, CreateProperty]
        internal int @tier;

        public string TypeSizeName => @typeSizeName;
        [JsonProperty, CreateProperty]
        internal string @typeSizeName = string.Empty;

        public int UnitClass => @unitClass;
        [JsonProperty, CreateProperty]
        internal int @unitClass;

        public IReadOnlyDictionary<UnitFeature, float> UnitFeatures => @unitFeatures;
        [JsonProperty, CreateProperty]
        internal Dictionary<UnitFeature, float> @unitFeatures = new();

        public int UnitPack => @unitPack;
        [JsonProperty, CreateProperty]
        internal int @unitPack;

        public int? UnitPortraitId => @unitPortraitId;
        [JsonProperty, CreateProperty]
        internal int? @unitPortraitId;

        public int? UnitTypeId => @unitTypeId;
        [JsonProperty, CreateProperty]
        internal int? @unitTypeId;

        public string UnitUpgradeGroup => @unitUpgradeGroup;
        [JsonProperty, CreateProperty]
        internal string @unitUpgradeGroup = string.Empty;

        public ManualUpgradeContent? UpgradeManually => @upgradeManually;
        [JsonProperty, CreateProperty]
        internal ManualUpgradeContent? @upgradeManually;

        public IReadOnlyDictionary<TerrainType, float> ViewWidthFactors => @viewWidthFactors;
        [JsonProperty, CreateProperty]
        internal Dictionary<TerrainType, float> @viewWidthFactors = new();

        public IReadOnlyDictionary<BaseTerrainType, float> ViewWidths => @viewWidths;
        [JsonProperty, CreateProperty]
        internal Dictionary<BaseTerrainType, float> @viewWidths = new();

        [UnityEngine.Scripting.Preserve]
        public UnitContent() { }
    }
}
