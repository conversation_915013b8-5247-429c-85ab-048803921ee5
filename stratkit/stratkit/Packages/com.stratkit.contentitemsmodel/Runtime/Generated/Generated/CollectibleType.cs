using System.Runtime.Serialization;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode]
    public enum CollectibleType {
        [EnumMember(Value = "PROFILE_AVATAR")]
        ProfileAvatar = 1,
        [EnumMember(Value = "PROFILE_FRAME")]
        ProfileFrame = 2,
        [EnumMember(Value = "PROFILE_BACKGROUND")]
        ProfileBackground = 3,
        [EnumMember(Value = "COALITION_IMAGE")]
        CoalitionImage = 4,
        [EnumMember(Value = "ALLIANCE_IMAGE")]
        AllianceImage = 5,
    }
}
