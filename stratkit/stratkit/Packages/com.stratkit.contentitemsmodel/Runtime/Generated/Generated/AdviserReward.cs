using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AdviserReward {
        public IReadOnlyDictionary<ContentItemId, int> PremiumIDToRewardAmountMap => @premiumIDToRewardAmountMap;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @premiumIDToRewardAmountMap = new();

        public IReadOnlyDictionary<ResourceId, int> ResourceIDToRewardAmountMap => @resourceIDToRewardAmountMap;
        [JsonProperty, CreateProperty]
        internal Dictionary<ResourceId, int> @resourceIDToRewardAmountMap = new();

        public IReadOnlyDictionary<ContentItemId, int> UnitTypeIDToRewardAmountMap => @unitTypeIDToRewardAmountMap;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @unitTypeIDToRewardAmountMap = new();

        [UnityEngine.Scripting.Preserve]
        public AdviserReward() { }
    }
}
