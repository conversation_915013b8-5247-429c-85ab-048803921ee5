using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class SingleFakeOrderContent {
        public float AmountFactor => @amountFactor;
        [JsonProperty, CreateProperty]
        internal float @amountFactor;

        public float PricePenaltyFactor => @pricePenaltyFactor;
        [JsonProperty, CreateProperty]
        internal float @pricePenaltyFactor;

        [UnityEngine.Scripting.Preserve]
        public SingleFakeOrderContent() { }
    }
}
