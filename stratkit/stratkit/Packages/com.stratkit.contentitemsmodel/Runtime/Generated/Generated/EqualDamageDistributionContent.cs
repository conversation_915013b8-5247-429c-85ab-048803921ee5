using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class EqualDamageDistributionContent {
        public float DeathThreshold => @deathThreshold;
        [<PERSON>sonProperty, CreateProperty]
        internal float @deathThreshold;

        public float DistributionLimit => @distributionLimit;
        [JsonProperty, CreateProperty]
        internal float @distributionLimit;

        public float DistributionStdDev => @distributionStdDev;
        [JsonProperty, CreateProperty]
        internal float @distributionStdDev;

        [UnityEngine.Scripting.Preserve]
        public EqualDamageDistributionContent() { }
    }
}
