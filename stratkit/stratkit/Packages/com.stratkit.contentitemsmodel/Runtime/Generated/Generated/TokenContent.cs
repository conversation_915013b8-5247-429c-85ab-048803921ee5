using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed partial class TokenContent : IContent {
        public AffectedUnitsContent? AffectedUnits => @affectedUnits;
        [JsonProperty, CreateProperty]
        internal AffectedUnitsContent? @affectedUnits;

        public bool ApplyPerUnit => @applyPerUnit;
        [JsonProperty, CreateProperty]
        internal bool @applyPerUnit;

        public ConsumptionStrategyContent? ConsumptionStrategy => @consumptionStrategy;
        [JsonProperty, CreateProperty]
        internal ConsumptionStrategyContent? @consumptionStrategy;

        public ConvertStrategyContent? ConvertStrategy => @convertStrategy;
        [JsonProperty, CreateProperty]
        internal ConvertStrategyContent? @convertStrategy;

        public CooldownStrategyContent? CooldownStrategy => @cooldownStrategy;
        [JsonProperty, CreateProperty]
        internal CooldownStrategyContent? @cooldownStrategy;

        public SplitStrategyContent? DeployStrategy => @deployStrategy;
        [JsonProperty, CreateProperty]
        internal SplitStrategyContent? @deployStrategy;

        public DurationStrategyContent? DurationStrategy => @durationStrategy;
        [JsonProperty, CreateProperty]
        internal DurationStrategyContent? @durationStrategy;

        public EffectsContent? Effects => @effects;
        [JsonProperty, CreateProperty]
        internal EffectsContent? @effects;

        public IReadOnlyDictionary<string, OneTimeEffectContent> EffectsOnApply => @effectsOnApply;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, OneTimeEffectContent> @effectsOnApply = new();

        public IReadOnlyDictionary<string, MapRestrictionContent> MapRestrictions => @mapRestrictions;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, MapRestrictionContent> @mapRestrictions = new();

        public float Priority => @priority;
        [JsonProperty, CreateProperty]
        internal float @priority;

        public IReadOnlyDictionary<ContentItemId, int> ProvidedTokens => @providedTokens;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @providedTokens = new();

        public PurchaseStrategyContent? PurchaseStrategy => @purchaseStrategy;
        [JsonProperty, CreateProperty]
        internal PurchaseStrategyContent? @purchaseStrategy;

        public string RequirementExpression => @requirementExpression;
        [JsonProperty, CreateProperty]
        internal string @requirementExpression = string.Empty;

        public SplitStrategyContent? SpawnOnAttackStrategy => @spawnOnAttackStrategy;
        [JsonProperty, CreateProperty]
        internal SplitStrategyContent? @spawnOnAttackStrategy;

        public SplitStrategyContent? SplitStrategy => @splitStrategy;
        [JsonProperty, CreateProperty]
        internal SplitStrategyContent? @splitStrategy;

        public int? TokenClass => @tokenClass;
        [JsonProperty, CreateProperty]
        internal int? @tokenClass;

        public IReadOnlyDictionary<ContentItemId, int> TokensOnApplyToArmy => @tokensOnApplyToArmy;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @tokensOnApplyToArmy = new();

        public IReadOnlyDictionary<ContentItemId, int> TokensOnApplyToPlayerState => @tokensOnApplyToPlayerState;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @tokensOnApplyToPlayerState = new();

        public IReadOnlyDictionary<ContentItemId, int> TokensOnApplyToProvince => @tokensOnApplyToProvince;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, int> @tokensOnApplyToProvince = new();

        public UnitDeadStrategyContent? UnitDeadStrategy => @unitDeadStrategy;
        [JsonProperty, CreateProperty]
        internal UnitDeadStrategyContent? @unitDeadStrategy;

        public VisibilityStrategyContent? VisibilityStrategy => @visibilityStrategy;
        [JsonProperty, CreateProperty]
        internal VisibilityStrategyContent? @visibilityStrategy;

        [UnityEngine.Scripting.Preserve]
        public TokenContent() { }
    }
}
