using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class SplitArmiesGoalContent {
        public int NumberOfArmies => @numberOfArmies;
        [JsonProperty, CreateProperty]
        internal int @numberOfArmies;

        [UnityEngine.Scripting.Preserve]
        public SplitArmiesGoalContent() { }
    }
}
