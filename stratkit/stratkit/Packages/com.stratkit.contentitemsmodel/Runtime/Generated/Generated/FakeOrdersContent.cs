using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class FakeOrdersContent {
        public IReadOnlyDictionary<string, SingleFakeOrderContent> Buy => @buy;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, SingleFakeOrderContent> @buy = new();

        public IReadOnlyDictionary<string, SingleFakeOrderContent> Sell => @sell;
        [JsonProperty, CreateProperty]
        internal Dictionary<string, SingleFakeOrderContent> @sell = new();

        [UnityEngine.Scripting.Preserve]
        public FakeOrdersContent() { }
    }
}
