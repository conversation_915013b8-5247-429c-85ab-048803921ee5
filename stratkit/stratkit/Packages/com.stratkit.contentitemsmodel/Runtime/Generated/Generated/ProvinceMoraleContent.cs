using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ProvinceMoraleContent {
        public float BaseTargetMorale => @baseTargetMorale;
        [JsonProperty, CreateProperty]
        internal float @baseTargetMorale;

        public float HappyThreshold => @happyThreshold;
        [JsonProperty, CreateProperty]
        internal float @happyThreshold;

        public float MaxMoralAfterRecapture => @maxMoralAfterRecapture;
        [JsonProperty, CreateProperty]
        internal float @maxMoralAfterRecapture;

        public float MinMoraleAfterCapture => @minMoraleAfterCapture;
        [JsonProperty, CreateProperty]
        internal float @minMoraleAfterCapture;

        public float MoraleChangeAfterCapture => @moraleChangeAfterCapture;
        [JsonProperty, CreateProperty]
        internal float @moraleChangeAfterCapture;

        public float MoraleChangeAfterRecapture => @moraleChangeAfterRecapture;
        [JsonProperty, CreateProperty]
        internal float @moraleChangeAfterRecapture;

        public float MoraleDecFactor => @moraleDecFactor;
        [JsonProperty, CreateProperty]
        internal float @moraleDecFactor;

        public float MoraleIncFactor => @moraleIncFactor;
        [JsonProperty, CreateProperty]
        internal float @moraleIncFactor;

        public float RevoltMaxBuildingDamage => @revoltMaxBuildingDamage;
        [JsonProperty, CreateProperty]
        internal float @revoltMaxBuildingDamage;

        public float RevoltMaxDamage => @revoltMaxDamage;
        [JsonProperty, CreateProperty]
        internal float @revoltMaxDamage;

        public float RevoltMaxMoraleDamage => @revoltMaxMoraleDamage;
        [JsonProperty, CreateProperty]
        internal float @revoltMaxMoraleDamage;

        public float RevoltMaxUnitDamage => @revoltMaxUnitDamage;
        [JsonProperty, CreateProperty]
        internal float @revoltMaxUnitDamage;

        public float RevoltMoraleThreshold => @revoltMoraleThreshold;
        [JsonProperty, CreateProperty]
        internal float @revoltMoraleThreshold;

        public IReadOnlyDictionary<ContentItemId, UnitRevoltSpawnContent> RevoltSpawnUnits => @revoltSpawnUnits;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, UnitRevoltSpawnContent> @revoltSpawnUnits = new();

        public IReadOnlyDictionary<RevolterSelectionCriteria, float> RevolterSelectionCriterias => @revolterSelectionCriterias;
        [JsonProperty, CreateProperty]
        internal Dictionary<RevolterSelectionCriteria, float> @revolterSelectionCriterias = new();

        public RevolterSelectionOrder RevolterSelectionOrder => @revolterSelectionOrder;
        [JsonProperty, CreateProperty]
        internal RevolterSelectionOrder @revolterSelectionOrder;

        [UnityEngine.Scripting.Preserve]
        public ProvinceMoraleContent() { }
    }
}
