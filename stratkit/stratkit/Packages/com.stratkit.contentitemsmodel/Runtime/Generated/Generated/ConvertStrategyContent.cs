using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ConvertStrategyContent {
        public ConvertStrategy Strategy => @strategy;
        [JsonProperty, CreateProperty]
        internal ConvertStrategy @strategy;

        [UnityEngine.Scripting.Preserve]
        public ConvertStrategyContent() { }
    }
}
