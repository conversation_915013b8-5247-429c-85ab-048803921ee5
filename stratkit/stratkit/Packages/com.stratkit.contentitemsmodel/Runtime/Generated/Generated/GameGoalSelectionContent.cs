using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class GameGoalSelectionContent {
        public IReadOnlyDictionary<ContentItemId, float> Pool => @pool;
        [JsonProperty, CreateProperty]
        internal Dictionary<ContentItemId, float> @pool = new();

        public string RequirementExpression => @requirementExpression;
        [JsonProperty, CreateProperty]
        internal string @requirementExpression = string.Empty;

        [UnityEngine.Scripting.Preserve]
        public GameGoalSelectionContent() { }
    }
}
