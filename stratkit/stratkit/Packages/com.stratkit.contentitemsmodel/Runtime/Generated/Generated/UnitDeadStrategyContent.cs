using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class UnitDeadStrategyContent {
        public UnitDeadStrategy Strategy => @strategy;
        [JsonProperty, CreateProperty]
        internal UnitDeadStrategy @strategy;

        [UnityEngine.Scripting.Preserve]
        public UnitDeadStrategyContent() { }
    }
}
