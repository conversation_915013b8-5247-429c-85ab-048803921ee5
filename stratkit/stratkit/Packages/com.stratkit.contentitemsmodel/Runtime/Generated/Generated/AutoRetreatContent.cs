using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AutoRetreatContent {
        public float MoraleOnRestoreLowerBound => @moraleOnRestoreLowerBound;
        [JsonProperty, CreateProperty]
        internal float @moraleOnRestoreLowerBound;

        public float MoraleOnRestoreUpperBound => @moraleOnRestoreUpperBound;
        [JsonProperty, CreateProperty]
        internal float @moraleOnRestoreUpperBound;

        public IReadOnlyDictionary<float, float> SurviveProbability => @surviveProbability;
        [JsonProperty, CreateProperty]
        internal Dictionary<float, float> @surviveProbability = new();

        [UnityEngine.Scripting.Preserve]
        public AutoRetreatContent() { }
    }
}
