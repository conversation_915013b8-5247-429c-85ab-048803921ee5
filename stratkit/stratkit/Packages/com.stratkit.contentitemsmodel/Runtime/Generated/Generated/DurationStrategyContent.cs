using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class DurationStrategyContent {
        public TimeSpan Duration => @duration;
        [JsonProperty, CreateProperty]
        internal TimeSpan @duration;

        public DurationStrategy Strategy => @strategy;
        [JsonProperty, CreateProperty]
        internal DurationStrategy @strategy;

        [UnityEngine.Scripting.Preserve]
        public DurationStrategyContent() { }
    }
}
