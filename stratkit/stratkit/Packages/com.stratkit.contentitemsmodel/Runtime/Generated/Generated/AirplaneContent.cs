using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class AirplaneContent {
        public bool OperationalWithoutAirfield => @operationalWithoutAirfield;
        [JsonProperty, CreateProperty]
        internal bool @operationalWithoutAirfield;

        public bool PatrolAllowed => @patrolAllowed;
        [JsonProperty, CreateProperty]
        internal bool @patrolAllowed;

        [UnityEngine.Scripting.Preserve]
        public AirplaneContent() { }
    }
}
