using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class RealisticBuildingDamage {
        public bool UseDamageOnlyFromProvinceCenter => @useDamageOnlyFromProvinceCenter;
        [JsonProperty, CreateProperty]
        internal bool @useDamageOnlyFromProvinceCenter;

        public float XFactorLimit => @xFactorLimit;
        [JsonProperty, CreateProperty]
        internal float @xFactorLimit;

        public float XFactorStdDev => @xFactorStdDev;
        [JsonProperty, CreateProperty]
        internal float @xFactorStdDev;

        [UnityEngine.Scripting.Preserve]
        public RealisticBuildingDamage() { }
    }
}
