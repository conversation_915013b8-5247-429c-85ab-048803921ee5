using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Unity.Properties;

namespace Stratkit.ContentItemsModel.Generated {
    [GeneratedCode, GeneratePropertyBag]
    public sealed class ExpansionPenalty {
        public float BeginsAt => @beginsAt;
        [JsonProperty, CreateProperty]
        internal float @beginsAt;

        public float MaximizesAt => @maximizesAt;
        [JsonProperty, CreateProperty]
        internal float @maximizesAt;

        public int MaximumMoralPenalty => @maximumMoralPenalty;
        [JsonProperty, CreateProperty]
        internal int @maximumMoralPenalty;

        [UnityEngine.Scripting.Preserve]
        public ExpansionPenalty() { }
    }
}
