using Stratkit.ContentItems.Level;
using Stratkit.ContentItemsModel.Data;
using Stratkit.ContentItemsModel.Generated;
using Stratkit.ServerCommunication;
using System.Linq;
using Unity.Collections;
using Unity.Entities;

namespace Stratkit.ContentItemsModel.DOTS {
    public struct ResearchContentDots : IContentDots<ResearchContent> {
        public Entity Entity { get; set; }

        public ResearchContentDots(Entity entity) {
            Entity = entity;
        }

        public void Initialize(
            EntityManager entityManager,
            IdMapper idMapper,
            BlobAssetStore blobAssetStore,
            ResearchContent content
        ) {
            // Research Details
            {
                entityManager.AddComponentData(
                    Entity,
                    new ResearchComponent {
                        AiResearchFactor = content.AiResearchFactor,
                        DayAvailable = content.DayAvailable,
                        TrackingOptionId = content.TrackingOptionID,
                        UnitPack = content.UnitPack,
                    }
                );
            }

            // Replaced Research
            {
                if (content.ReplacedResearch.HasValue) {
                    ReplacedResearchComponent data = new() {
                        ReplacedResearchId = content.ReplacedResearch.Value,
                    };
                    entityManager.AddComponentData(Entity, data);

                    (FixedString64Bytes, long) supId = ("ResearchType", data.ReplacedResearchId);
                    Entity referredEntity = idMapper.GetEntity(supId);
                    entityManager.AddComponent<ContentItemPreviousLevel>(Entity);
                    entityManager.SetComponentData(
                        Entity,
                        new ContentItemPreviousLevel {
                            Value = referredEntity,
                        }
                    );
                }
            }

            // Research Faction Base Item
            {
                if (content.FactionBaseItemID.HasValue) {
                    ContentItemId factionBaseItemID = content.FactionBaseItemID.Value;
                    Entity factionBaseItemEntity = idMapper.GetEntity(("ResearchType", factionBaseItemID));
                    ResearchFactionBaseItemComponent data = new() {
                        Value = factionBaseItemEntity,
                    };
                    entityManager.AddComponentData(Entity, data);
                }
            }

            // Research Is Transport Ship
            {
                if (content.TransportShip) {
                    entityManager.AddComponent<ResearchIsTransportShipComponentTag>(Entity);
                }
            }

            // Research Required Plans
            {
                if (content.RequiredPlans.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new ResearchRequiredPlansComponent {
                            RequiredPlans = content
                                .RequiredPlans.Select(plan => new IdValueTuple {
                                        Id = plan.Key.Value,
                                        Amount = plan.Value,
                                    }
                                )
                                .ToArray(),
                        }
                    );
                }
            }

            // Research Required Researches
            {
                if (content.RequiredResearches.Count > 0) {
                    int[] requiredResearches = content
                        .RequiredResearches.Select(research => (int)research)
                        .ToArray();
                    RequiredResearchesComponent data = new() {
                        RequiredResearchesIds = requiredResearches,
                    };
                    entityManager.AddComponentData(Entity, data);

                    DynamicBuffer<RequiredResearches> requiredResearchesEnumerable =
                        entityManager.AddBuffer<RequiredResearches>(Entity);
                    foreach (int dataRequiredResearchesId in data.RequiredResearchesIds) {
                        requiredResearchesEnumerable.Add(
                            new RequiredResearches {
                                ResearchId = dataRequiredResearchesId,
                                ResearchType = idMapper.GetEntity(("ResearchType", dataRequiredResearchesId)),
                            }
                        );
                    }
                }
            }

            // Research Factions
            {
                if (content.Factions.Count > 0) {
                    int[] factions = content
                        .Factions.Select(faction => (int)faction)
                        .ToArray();

                    entityManager.AddComponentData(
                        Entity,
                        new ResearchFactionsComponent {
                            FactionIds = factions,
                        }
                    );
                }
            }

            // Research Build Time
            {
                entityManager.AddComponentData(
                    Entity,
                    new ResearchBuildTime {
                        BuildTimeSeconds = content.BuildTimeSeconds(),
                    }
                );
            }

            // Research Set
            {
                entityManager.AddComponentData(
                    Entity,
                    new ResearchSet {
                        Value = content.Set,
                    }
                );
            }

            // Research Set Order ID
            {
                entityManager.AddComponentData(
                    Entity,
                    new ResearchSetOrderId {
                        Value = content.SetOrderID,
                    }
                );
            }

            // Requirements Expression
            {
                if (!string.IsNullOrEmpty(content.RequirementExpression)) {
                    entityManager.AddComponentData(
                        Entity,
                        new RequirementExpression {
                            Value = content.RequirementExpression.BuildBlob(blobAssetStore),
                        }
                    );
                }
            }

            // Costs
            {
                Costs data = new();
                IdValueTuple[] costsValues = content
                    .Costs
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    costsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }
        }
    }
}
