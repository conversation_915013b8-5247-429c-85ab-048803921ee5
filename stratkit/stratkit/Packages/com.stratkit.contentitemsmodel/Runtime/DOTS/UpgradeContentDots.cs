using Stratkit.Collections;
using Stratkit.ContentItems.Level;
using Stratkit.ContentItemsModel.Generated;
using System.Collections.Generic;
using System.Linq;
using Unity.Collections;
using Unity.Entities;
using Stratkit.ContentItemsModel.Data;
using Stratkit.ServerCommunication;

namespace Stratkit.ContentItemsModel.DOTS {
    public struct UpgradeContentDots : IContentDots<UpgradeContent> {
        public Entity Entity { get; set; }

        public UpgradeContentDots(Entity entity) {
            Entity = entity;
        }

        public void Initialize(
            EntityManager entityManager,
            IdMapper idMapper,
            BlobAssetStore blobAssetStore,
            UpgradeContent content
        ) {
            // BuildingEnableableComponentTag
            {
                if (content.Enableable) {
                    entityManager.AddComponent<BuildingEnableableComponentTag>(Entity);
                }
            }

            // BuildingDayOfAvailability
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingDayOfAvailability { Value = content.DayOfAvailability }
                );
            }

            // BuildingFeatures
            {
                using BlobHashMapBuilder<int, float> featuresBuilder = new(Allocator.Temp);
                foreach ((UpgradeFeature feature, float value) in content.Features) {
                    featuresBuilder.Add((int)feature, value);
                }

                BlobAssetReference<BlobHashMap<int, float>> features =
                    featuresBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref features);
                entityManager.AddComponentData(
                    Entity,
                    new BuildingFeatures { Blob = features }
                );
            }

            // BuildingForbiddenTerrain
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingForbiddenTerrain { Value = content.ForbiddenTerrain.Cast<int>().ToArray() }
                );
            }

            // BuildingForbiddenTerrainCoreComponent
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingForbiddenTerrainCoreComponent {
                        Value = content.ForbiddenTerrainCore?.Cast<int>().ToArray() ?? new int[0]
                    }
                );
            }

            // BuildingFactory
            {
                if (content.TryGetFeature(UpgradeFeature.Factory, out float factoryValue)) {
                    entityManager.AddComponentData(
                        Entity,
                        new BuildingFactory { Value = factoryValue }
                    );
                }
            }

            // BuildingFortress
            {
                if (content.TryGetFeature(UpgradeFeature.Fortress, out float fortressValue)) {
                    entityManager.AddComponentData(
                        Entity,
                        new BuildingFortress { Value = fortressValue }
                    );
                }
            }

            // BuildingProvinceSpeedBonus
            {
                if (content.TryGetFeature(UpgradeFeature.Railroad, out float railroadValue)) {
                    entityManager.AddComponentData(
                        Entity,
                        new BuildingProvinceSpeedBonus { Value = railroadValue }
                    );
                }
            }

            // BuildingCapitalTag
            {
                if (content.HasFeature(UpgradeFeature.Capital)) {
                    entityManager.AddComponentData(
                        Entity,
                        new BuildingCapitalTag()
                    );
                }
            }

            // BuildingUpgradeGrounds
            {
                float requireUpgradeGroundValue =
                    content.Features.GetValueOrDefault(UpgradeFeature.RequireUpgradeGround, 0);
                float unlockUpgradeGroundsValue =
                    content.Features.GetValueOrDefault(UpgradeFeature.UnlockUpgradeGrounds, 0);
                entityManager.AddComponentData(
                    Entity,
                    new BuildingUpgradeGrounds {
                        RequireUpgradeGround = (int)requireUpgradeGroundValue,
                        UnlockUpgradeGround = (int)unlockUpgradeGroundsValue,
                    }
                );
            }

            // BuildingMorale
            {
                if (content.TryGetFeature(UpgradeFeature.MoraleBonus, out float moraleBonusValue)) {
                    entityManager.AddComponentData(
                        Entity,
                        new BuildingMorale { Value = moraleBonusValue }
                    );
                }
            }

            // HarbourContent
            {
                if (content.Harbour != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new Data.HarbourContent {
                            EmbarkmentFactor = content.Harbour.EmbarkmentFactor,
                            ForbiddenTerrain = content.Harbour.ForbiddenTerrain.Cast<int>().ToArray(),
                        }
                    );
                }
            }

            // BuildingMaxCondition
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingMaxCondition { Value = content.MaxCondition }
                );
            }

            // BuildingMinCondition
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingMinCondition { Value = content.MinCondition }
                );
            }

            // BuildingBuildCondition
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingBuildCondition { Value = content.BuildCondition }
                );
            }

            // DailyProductions
            {
                DailyProductionsComponent dailyProductions = new();
                IdValueTuple[] dailyProductionsValues = content
                    .DailyProductions
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                dailyProductions.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyProductionsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, dailyProductions);
            }

            // ProductionBonus
            {
                ProductionBonus productionsBonus = new();
                IdValueTuple[] productionsBonusValues = content
                    .ProdBonus
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                productionsBonus.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    productionsBonusValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, productionsBonus);
            }

            // BuildingRequiredUpgrades
            {
                BuildingRequiredUpgrades buildingRequiredUpgrades = new();
                IdValueTuple[] idValueTuples = content
                    .RequiredUpgrades
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                EntityValueTuple[] entityValueTuples =
                    EntityValueTupleUtils.ConvertIds(idMapper, idValueTuples, "UpgradeType");
                buildingRequiredUpgrades.Value = entityValueTuples.Select(x => x.Entity).ToArray();
                entityManager.AddComponentData(Entity, buildingRequiredUpgrades);
            }

            // RequiredResearchesComponent
            {
                RequiredResearchesComponent requiredResearches = new();
                DynamicBuffer<RequiredResearches> requiredResearchesBuffer =
                    entityManager.AddBuffer<RequiredResearches>(Entity);
                foreach (int dataRequiredResearchesId in content.RequiredResearches) {
                    requiredResearchesBuffer.Add(
                        new RequiredResearches {
                            ResearchId = dataRequiredResearchesId,
                            ResearchType = idMapper.GetEntity(("ResearchType", dataRequiredResearchesId)),
                        }
                    );
                }

                entityManager.AddComponentData(Entity, requiredResearches);
            }

            // ReplacedUpgradeByIdComponent
            {
                if (content.ReplacedUpgrade != null) {
                    ContentItemId replacedUpgrade = content.ReplacedUpgrade.Value;
                    (FixedString64Bytes, long) supId = ("UpgradeType", replacedUpgrade);
                    Entity referredEntity = idMapper.GetEntity(supId);
                    entityManager.AddComponent<ContentItemPreviousLevel>(Entity);
                    entityManager.SetComponentData(
                        Entity,
                        new ContentItemPreviousLevel {
                            Value = referredEntity,
                        }
                    );
                }
            }

            // BuildTime
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildTimeComponent {
                        BuildTime = content.BuildTimeSeconds(),
                    }
                );
            }

            // BuildingSortOrder
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildingSortOrder {
                        SortOrder = int.Parse(content.SortOrders),
                    }
                );
            }

            // Costs
            {
                Costs data = new();
                IdValueTuple[] dailyProductionsValues = content
                    .Costs
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyProductionsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }

            // DailyCosts
            {
                DailyCosts data = new();
                IdValueTuple[] dailyCostsValues = content
                    .DailyCosts
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyCostsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }
        }
    }
}
