using Stillfront.Logging;
using Stratkit.Collections;
using Stratkit.ContentItemsModel.Generated;
using Stratkit.ContentItemsModel.Data;
using Stratkit.ServerCommunication;
using System.Collections.Generic;
using System.Linq;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using AirplaneContent = Stratkit.ContentItemsModel.Data.AirplaneContent;
using DamageAuraContent = Stratkit.ContentItemsModel.Data.DamageAuraContent;
using Range = Stratkit.ContentItemsModel.Data.Range;

namespace Stratkit.ContentItemsModel.DOTS {
    public struct UnitContentDots : IContentDots<UnitContent> {
        public Entity Entity { get; set; }

        public UnitContentDots(Entity entity) {
            Entity = entity;
        }

        public void Initialize(
            EntityManager entityManager,
            IdMapper idMapper,
            BlobAssetStore blobAssetStore,
            UnitContent content
        ) {
            entityManager
                .AddBuffer<Range>(Entity)
                .Fill((k, v) => new Range(k, v), content.Ranges);

            entityManager
                .AddBuffer<AntiAirRange>(Entity)
                .Fill((k, v) => new AntiAirRange(k, v), content.AntiAirRanges);

            // Costs
            {
                Costs data = new();
                IdValueTuple[] dailyProductionsValues = content
                    .Costs
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyProductionsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }

            // DailyCosts
            {
                DailyCosts data = new();
                IdValueTuple[] dailyCostsValues = content
                    .DailyCosts
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                data.ValueEntities = EntityValueTupleUtils.ConvertIds(
                    idMapper,
                    dailyCostsValues,
                    "ResourceType"
                );
                entityManager.AddComponentData(Entity, data);
            }

            // Speed
            {
                using BlobHashMapBuilder<int, float> unitSpeedBuilder = new(Allocator.Temp);

                foreach (KeyValuePair<BaseTerrainType, float> speed in content.Speeds) {
                    unitSpeedBuilder.Add((int)speed.Key, speed.Value / 60f);
                }

                BlobAssetReference<BlobHashMap<int, float>> unitSpeed =
                    unitSpeedBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref unitSpeed);
                entityManager.AddComponentData(Entity, new UnitSpeed { Blob = unitSpeed });
            }

            // UnitManualConverterContent
            {
                if (content.ConvertManually != null) {
                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", content.ConvertManually.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitManualConverter {
                            SpawnedUnitType = spawnedUnitType,
                            ConversionTime = (int)content.ConvertManually.ConversionTime.TotalSeconds,
                        }
                    );
                }
            }

            // AirplaneContent
            {
                if (content.Airplane != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new AirplaneContent {
                            PatrolAllowed = content.Airplane.PatrolAllowed,
                            OperationalWithoutAirfield = content.Airplane.OperationalWithoutAirfield,
                        }
                    );
                }
            }

            // UnitSpawnerContent
            {
                if (content.SpawnUnit != null) {
                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", content.SpawnUnit.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitSpawner {
                            SpawnedUnitType = spawnedUnitType,
                            MobilizationTime = (int)content.SpawnUnit.MobilizationTime.TotalSeconds,
                            CanAttackOnSea = content.SpawnUnit.CanAttackOnSea,
                        }
                    );
                }
            }

            // UnitDeployContent
            {
                if (content.DeployUnit != null) {
                    Entity spawnedUnitType = idMapper.GetEntity(("UnitType", content.DeployUnit.SpawnedUnitTypeId));
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDeployUnit {
                            SpawnedUnitType = spawnedUnitType,
                            DeployAmount = content.DeployUnit.DeployAmount,
                            DeployTime = (int)content.DeployUnit.DeployTime.TotalSeconds,
                            DeployRange = content.DeployUnit.DeployRange,
                            MaxAmmunition = 0,
                            InitialAmmunition = 0,
                            ReloadTime = 0,
                        }
                    );

                    DynamicBuffer<UnitDeployCost> deployCosts = entityManager.AddBuffer<UnitDeployCost>(Entity);
                    foreach (KeyValuePair<ResourceId, int> cost in content.DeployUnit.DeployCost) {
                        Entity resource = idMapper.GetEntity(("ResourceType", (int)cost.Key));
                        deployCosts.Add(
                            new UnitDeployCost {
                                Resource = resource,
                                Amount = cost.Value,
                            }
                        );
                    }

                }
            }

            // DamageAuraContent
            {
                if (content.DamageAura != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new DamageAuraContent {
                            Radius = content.DamageAura.Radius,
                            DamageTickInterval = (int)content.DamageAura.DamageTickInterval.TotalSeconds,
                        }
                    );
                }
            }

            // BuildTime
            {
                entityManager.AddComponentData(
                    Entity,
                    new BuildTimeComponent {
                        BuildTime = content.BuildTimeSeconds(),
                    }
                );
            }

            // BuildingRequiredUpgrades
            {
                BuildingRequiredUpgrades data = new();
                IdValueTuple[] idValueTuples = content
                    .RequiredUpgrades
                    .Select(x => new IdValueTuple { Id = x.Key.Value, Amount = x.Value })
                    .ToArray();
                EntityValueTuple[] entityValueTuples =
                    EntityValueTupleUtils.ConvertIds(idMapper, idValueTuples, "UpgradeType");
                data.Value = entityValueTuples.Select(x => x.Entity).ToArray();
                entityManager.AddComponentData(Entity, data);
            }

            // UnitEmbarkmentComponent
            {
                if (content.Embarkment != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitEmbarkmentComponent {
                            EnemyEmbarkmentTime = (int)content.Embarkment.EnemyEmbarkmentTime.TotalSeconds,
                            FriendlyEmbarkmentTime = (int)content.Embarkment.FriendlyEmbarkmentTime.TotalSeconds,
                        }
                    );
                }
            }

            // UnitCustomSeaUnitTypeIdComponent
            {
                if (content.CustomSeaUnitTypeId != null) {
                    (FixedString64Bytes, long) valueTuple = ("UnitType", content.CustomSeaUnitTypeId.Value);
                    Entity referencedEntity = idMapper.GetEntity(valueTuple);
                    entityManager.AddComponentData(Entity, new UnitCustomSeaUnitType { Value = referencedEntity });
                }
            }

            // UnitExpirableSecondsComponent
            {
                if (content.Expirable != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitExpirableSecondsComponent {
                            Value = (int)content.Expirable.Value.TotalSeconds,
                        }
                    );
                }
            }

            // UnitFactionBaseItemId
            {
                if (content.FactionBaseItemID != null) {
                    UnitFactionBaseItemId data = new() {
                        Value = content.FactionBaseItemID.Value,
                    };
                    entityManager.AddComponentData(Entity, data);

                    (FixedString64Bytes, long) supId = ("UnitType", data.Value);
                    UnitFactionBaseItemComponent component = new() {
                        Value = idMapper.GetEntity(supId),
                    };
                    entityManager.AddComponent<UnitFactionBaseItemComponent>(Entity);
                    entityManager.SetComponentData(Entity, component);
                }
            }

            // RequiredResearchesComponent
            {
                RequiredResearchesComponent data = new() {
                    RequiredResearchesIds = content
                        .RequiredResearches
                        .Select(x => x.Value)
                        .ToArray(),
                };
                entityManager.AddComponentData(Entity, data);

                DynamicBuffer<RequiredResearches> requiredResearchesEnumerable =
                    entityManager.AddBuffer<RequiredResearches>(Entity);
                foreach (int dataRequiredResearchesId in data.RequiredResearchesIds) {
                    requiredResearchesEnumerable.Add(
                        new RequiredResearches {
                            ResearchId = dataRequiredResearchesId,
                            ResearchType = idMapper.GetEntity(("ResearchType", dataRequiredResearchesId)),
                        }
                    );
                }
            }

            // UnitMinProductionTimeComponent
            {
                if (content.MinProductionTime != null) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitMinProductionTimeComponent {
                            MinProductionTime = (int)content.MinProductionTime.Value.TotalSeconds,
                        }
                    );
                }
            }

            // UnitLevel
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitLevel {
                        Value = content.Tier,
                    }
                );
            }

            // UnitDamageTypeComponent
            {
                if (content.DamageTypes.Count > 0) {
                    if (content.DamageTypes.Count > 1) {
                        Log.Error($"Unit Content Damage types has more than one damage type: {content.DamageTypes.Count}");
                    }

                    long damageTypeId = (long)math.floor(content.DamageTypes.First().Value);
                    (FixedString64Bytes, long) valueTuple = ("DamageType", damageTypeId);
                    Entity referencedEntity = idMapper.GetEntity(valueTuple);
                    UnitDamageType component = new() {
                        Value = referencedEntity,
                    };

                    entityManager.AddComponent<UnitDamageType>(Entity);
                    entityManager.SetComponentData(Entity, component);
                }
            }

            // UnitHitPoints
            {
                if (content.HitPoints.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitHitPoints {
                            Value = new BaseTerrainTypeValueMap(content.hitPoints),
                        }
                    );
                }
            }

            // UnitIngameName
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitIngameName {
                        Value = content.IngameName,
                    }
                );
            }

            // UnitDamageFactorsComponent
            {
                if (content.DamageFactors.Count > 0) {
                    UnitDamageFactorsComponent data = new() {
                        DamageFactorIds = content
                            .DamageFactors
                            .Select(x => new IdValueTuple { Id = (int)x.Key, Amount = x.Value })
                            .ToArray(),
                    };
                    data.DamageFactorEntities = data
                        .DamageFactorIds
                        .Select(x => new EntityValueTuple {
                                Entity = idMapper.GetEntity(("DamageType", x.Id)),
                                Amount = x.Amount,
                            }
                        )
                        .ToArray();
                    entityManager.AddComponentData(Entity, data);
                }
            }

            // UnitDefenceFactorsComponent
            {
                if (content.DefenceFactors.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDefenceFactorsComponent {
                            DefenceFactors = content
                                .DefenceFactors
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitDefenceComponent
            {
                if (content.Defence.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitDefenceComponent {
                            Defence = content
                                .Defence
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitStrengthComponent
            {
                if (content.Strength.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitStrengthComponent {
                            Strength = content
                                .Strength
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitSpeedFactorsComponent
            {
                using BlobHashMapBuilder<int, float> unitSpeedFactorsBuilder = new(Allocator.Temp);

                foreach (KeyValuePair<TerrainType, float> speedFactor in content.SpeedFactors) {
                    unitSpeedFactorsBuilder.Add((int)speedFactor.Key, speedFactor.Value);
                }

                BlobAssetReference<BlobHashMap<int, float>> unitSpeedFactors =
                    unitSpeedFactorsBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref unitSpeedFactors);

                entityManager.AddComponentData(
                    Entity,
                    new UnitSpeedFactors {
                        TerrainBlob = unitSpeedFactors,
                        Friendly = (float)content.FriendlySpeedFactor,
                        Foreign = (float)content.ForeignSpeedFactor,
                    }
                );
            }

            // UnitStrengthFactorsComponent
            {
                if (content.StrengthFactors.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitStrengthFactorsComponent {
                            StrengthFactors = content
                                .StrengthFactors
                                .Select(x => new StringValueTuple { Id = x.Key.ToString().ToUpper(), Amount = x.Value })
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitStatsColumnId
            {
                entityManager.AddComponentData(
                    Entity,
                    new UnitStatsColumnId {
                        Value = content.StatsColumnID,
                    }
                );
            }

            // UnitClassComponent
            {
                (FixedString64Bytes, long) valueTuple = ("UnitClass", content.UnitClass);
                Entity referencedEntity = idMapper.GetEntity(valueTuple);
                entityManager.AddComponentData(
                    Entity,
                    new UnitClass {
                        Value = referencedEntity,
                    }
                );
            }

            // UnitViewWidthsComponent
            {
                if (content.ViewWidths.Count > 0) {
                    using BlobHashMapBuilder<int, float> viewWidthsBuilder = new(Allocator.Temp);
                    foreach ((BaseTerrainType terrain, float value) in content.ViewWidths) {
                        viewWidthsBuilder.Add((int)terrain, value);
                    }

                    BlobAssetReference<BlobHashMap<int, float>> viewWidths =
                        viewWidthsBuilder.CreateBlobAssetReference(Allocator.Persistent);
                    blobAssetStore.TryAdd(ref viewWidths);
                    entityManager.AddComponentData(Entity, new UnitViewWidths { Blob = viewWidths });
                }
            }

            // BelongsToFactionsIdComponent
            {
                DynamicBuffer<BelongsToFactionComponent> belongsToFactionComponents =
                    entityManager.AddBuffer<BelongsToFactionComponent>(Entity);
                foreach (int factionId in content.Factions) {
                    if (idMapper.TryGetEntity(("FactionsType", factionId), out Entity factionEntity)) {
                        belongsToFactionComponents.Add(
                            new BelongsToFactionComponent {
                                Value = factionEntity,
                            }
                        );
                    }
                }
            }

            // UnitProductionSpeedupCostFactor
            {
                if (content.UnitFeatures.TryGetValue(UnitFeature.ProductionSpeedupCostFactor, out float value)) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitProductionSpeedupCostFactor {
                            Value = value,
                        }
                    );
                }
            }

            // UnitTypeTag
            {
                entityManager.AddComponent<UnitTypeTag>(Entity);
            }

            // UnitMoraleBasedDamageFactorComponent
            {
                if (content.MoraleBasedDmgFactor.Count > 0) {
                    entityManager.AddComponentData(
                        Entity,
                        new UnitMoraleBasedDamageFactorComponent {
                            Value = content
                                .MoraleBasedDmgFactor
                                .Select(x => new ValueValueTuple {
                                        Key = x.Key,
                                        Value = x.Value,
                                    }
                                )
                                .ToArray(),
                        }
                    );
                }
            }

            // UnitHitpointSizeFactorsComponent
            {
                if (content.HitpointSizeFactors.Count > 0) {
                    DynamicBuffer<UnitHitpointSizeFactors> unitSizeFactorsEnumerable =
                        entityManager.AddBuffer<UnitHitpointSizeFactors>(Entity);
                    foreach (KeyValuePair<float, float> sizeFactor in content.HitpointSizeFactors) {
                        unitSizeFactorsEnumerable.Add(
                            new UnitHitpointSizeFactors { Size = (int)sizeFactor.Key, Value = sizeFactor.Value }
                        );
                    }
                }
            }

            // UnitStrengthSizeFactorsComponent
            {
                if (content.StrengthSizeFactors.Count > 0) {
                    DynamicBuffer<UnitStrengthSizeFactors> unitSizeFactorsEnumerable =
                        entityManager.AddBuffer<UnitStrengthSizeFactors>(Entity);
                    foreach (KeyValuePair<float, float> tuple in content.StrengthSizeFactors) {
                        unitSizeFactorsEnumerable.Add(
                            new UnitStrengthSizeFactors { Size = (int)tuple.Key, Value = tuple.Value }
                        );
                    }
                }
            }

            // UnitAdditionalProductionComponent
            {
                if (content.AdditionalProduction != null) {
                    static NativeNumericPiecewiseLinearFunction CreateNativeNumericPiecewiseLinearFunction(
                        IReadOnlyDictionary<float, float> dict
                    ) {
                        ValueValueTuple[] tupleArray;
                        int index = 0;
                        //Create array with appropriate size ensuring factor starts with (1,1)
                        if (dict.Count == 0 || !Mathf.Approximately(dict.First().Key, 1f)) {
                            tupleArray = new ValueValueTuple[dict.Count + 1];
                            tupleArray[index++] = new ValueValueTuple { Key = 1, Value = 1f };
                        } else {
                            tupleArray = new ValueValueTuple[dict.Count];
                        }

                        foreach (KeyValuePair<float, float> dictEntry in dict) {
                            tupleArray[index++] = new ValueValueTuple() {
                                Key = dictEntry.Key,
                                Value = dictEntry.Value,
                            };
                        }

                        return new NativeNumericPiecewiseLinearFunction(tupleArray, Allocator.Persistent);
                    }

                    UnitAdditionalProduction unitAdditionalProduction = new UnitAdditionalProduction();

                    unitAdditionalProduction.MaxAmount = new(content.AdditionalProduction.MaxAmount.Count, Allocator.Persistent);

                    foreach (KeyValuePair<ContentItemId, int> maxAmountData in content.AdditionalProduction.MaxAmount) {
                        (FixedString64Bytes, long) supId = ("UpgradeType", maxAmountData.Key);
                        Entity entity = idMapper.GetEntity(supId);
                        unitAdditionalProduction.MaxAmount[entity] = maxAmountData.Value;
                    }

                    unitAdditionalProduction.BuildTimeFactor = CreateNativeNumericPiecewiseLinearFunction(content.AdditionalProduction.BuildTimeFactor);
                    unitAdditionalProduction.CostFactor = CreateNativeNumericPiecewiseLinearFunction(content.AdditionalProduction.CostFactor);

                    entityManager.AddComponentData(Entity, unitAdditionalProduction);
                }
            }

            // UnitSet
            entityManager.AddComponentData(Entity, new UnitSet { Value = content.Set });

            // UnitIdentifierComponent
            entityManager.AddComponentData(Entity, new UnitIdentifierComponent { Value = content.Identifier });

            // Extras
            {
                using BlobHashMapBuilder<int, float> featuresBuilder = new(Allocator.Temp);
                foreach ((UnitFeature id, float value) in content.UnitFeatures) {
                    featuresBuilder.Add((int)id, value);
                }

                BlobAssetReference<BlobHashMap<int, float>> features =
                    featuresBuilder.CreateBlobAssetReference(Allocator.Persistent);
                blobAssetStore.TryAdd(ref features);
                entityManager.AddComponentData(Entity, new UnitFeatures { Blob = features });

                UnitDamageArea damageArea = default;
                foreach (KeyValuePair<BaseTerrainType, float> tuple in content.DamageArea) {
                    damageArea.Value.Add((tuple.Key.ToString().ToUpper(), tuple.Value));
                }

                entityManager.AddComponentData(Entity, damageArea);
                entityManager.AddComponentData(
                    Entity,
                    new UnitAllowedToAttack {
                        Value = content.AttackAllowed,
                    }
                );
            }

            // army modifiers
            {
                if (content.ArmyModifiersConfig != null) {
                    DynamicBuffer<UnitArmyModifierConfig> buffer =
                        entityManager.AddBuffer<UnitArmyModifierConfig>(Entity);
                    foreach (ArmyModifierSet set in content.ArmyModifiersConfig.ArmyModifierSets.Values) {
                        foreach (ArmyModifierContent modifier in set.ArmyModifiers.Values) {
                            Entity affectedUnitTypesEntity =
                                entityManager.CreateEntity(
                                    stackalloc ComponentType[] {
                                        ComponentType.ReadWrite<UnitArmyModifierAffectedUnitTypeId>(),
                                    }
                                );
                            DynamicBuffer<UnitArmyModifierAffectedUnitTypeId> affectedUnitTypesBuffer =
                                entityManager.GetBuffer<UnitArmyModifierAffectedUnitTypeId>(affectedUnitTypesEntity);
                            foreach (ContentItemId id in modifier.AffectedUnitTypes) {
                                affectedUnitTypesBuffer.Add(new UnitArmyModifierAffectedUnitTypeId { Value = id });
                            }

                            Entity enablingTerritoryTypesEntity =
                                entityManager.CreateEntity(
                                    stackalloc ComponentType[] {
                                        ComponentType.ReadWrite<UnitArmyModifierEnablingTerritoryType>()
                                    }
                                );
                            DynamicBuffer<UnitArmyModifierEnablingTerritoryType> enablingTerritoryTypes =
                                entityManager.GetBuffer<UnitArmyModifierEnablingTerritoryType>(
                                    enablingTerritoryTypesEntity
                                );
                            foreach (TerritoryType type in modifier.EnablingTerritoryTypes) {
                                enablingTerritoryTypes.Add(new UnitArmyModifierEnablingTerritoryType { Value = type });
                            }

                            buffer.Add(
                                new UnitArmyModifierConfig {
                                    RequiredInventoryItemId = set.RequiredInventoryItem
                                        ?? new ContentItemId(ContentItemsIdSpace.BackEnd, -1),
                                    AffectedUnitTypesEntity = affectedUnitTypesEntity,
                                    EnablingTerritoryTypesEntity = enablingTerritoryTypesEntity,
                                    ArmyVisionFactorModifier = modifier.ArmyVisionFactorModifier,
                                    ArmyVisionFlatModifier = modifier.ArmyVisionFlatModifier,
                                    AttackFactorModifier = modifier.AttackFactorModifier,
                                    DefenceFactorModifier = modifier.DefenceFactorModifier,
                                    HitPointsFactorModifier = modifier.HitpointsFactorModifier,
                                    SpeedFactorModifier = modifier.SpeedFactorModifier,
                                    SpeedFlatModifier = modifier.SpeedFlatModifier,
                                }
                            );
                        }
                    }
                }
            }
        }

        public DynamicBuffer<Range> GetRanges(ref SystemState state) {
            DynamicBuffer<Range> buffer = state.EntityManager.GetBuffer<Range>(Entity);
            return buffer;
        }

        public float GetRange(ref SystemState state, BaseTerrainType key) {
            DynamicBuffer<Range> buffer = state.EntityManager.GetBuffer<Range>(Entity);
            return buffer.FindEnumKey<Range, BaseTerrainType, float>(key);
        }

        public DynamicBuffer<AntiAirRange> GetAntiAirRanges(ref SystemState state) {
            DynamicBuffer<AntiAirRange> buffer = state.EntityManager.GetBuffer<AntiAirRange>(Entity);
            return buffer;
        }

        public float GetAntiAirRange(ref SystemState state, BaseTerrainType key) {
            DynamicBuffer<AntiAirRange> buffer =
                state.EntityManager.GetBuffer<AntiAirRange>(Entity);
            return buffer.FindEnumKey<AntiAirRange, BaseTerrainType, float>(key);
        }
    }
}
