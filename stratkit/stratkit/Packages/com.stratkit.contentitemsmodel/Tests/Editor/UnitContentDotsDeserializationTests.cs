using NUnit.Framework;
using Stratkit.ContentItemsModel.Data;
using Stratkit.ContentItemsModel.DOTS;
using Stratkit.ContentItemsModel.Generated;
using Stratkit.ServerCommunication;
using System.Linq;
using Unity.Collections;
using Unity.Entities;
using Range = Stratkit.ContentItemsModel.Data.Range;

namespace Stratkit.ContentItemsModel.Tests {
    public sealed class UnitContentDotsDeserializationTests {
        private EntityManager EntityManager => _world.EntityManager;

        private World _world = null!;
        private ContentItem<UnitContent> _unitContentItem = null!;
        private UnitContent _unitContent = null!;
        private TestSystem _system = null!;
        private Entity _unitContentEntity;
        private UnitContentDots _unitContentDots;

        private partial class TestSystem : SystemBase {
            protected override void OnUpdate() {
                Enabled = false;
            }
        }

        [SetUp]
        public void SetUp() {
            _world = new World("TestWorld");
            _system = _world.CreateSystemManaged<TestSystem>();

            IdMapper idMapper = IdMapper.Create();
            SetupDefaultEntities(idMapper);

            _unitContentItem = ContentItemNewtonsoftJsonDeserializer<UnitContent>.Deserialize(DefaultUnitContentJsonString)!;
            _unitContent = _unitContentItem.Versions[1];
            _unitContentEntity = EntityManager.CreateEntity();
            _unitContentDots = new UnitContentDots(_unitContentEntity);
            _unitContentDots.Initialize(
                _world.EntityManager,
                idMapper,
                new BlobAssetStore(32),
                _unitContent!
            );
        }

        private void SetupDefaultEntities(IdMapper idMapper) {
            Entity unitClassEntity = EntityManager.CreateEntity();
            idMapper.Add(("UnitClass", 0), unitClassEntity);
        }

        [TearDown]
        public void TearDown() {
            _world.Dispose();
        }

        [Test]
        public void DotsDeserialization_Ranges() {
            using NativeArray<Range> ranges =
                _unitContentDots.GetRanges(ref _system.CheckedStateRef).ToNativeArray(Allocator.Temp);
            Range[] expected = {
                new() { Key = BaseTerrainType.Land, Value = 50.0f },
                new() { Key = BaseTerrainType.Air, Value = 25.0f },
            };
            Assert.IsTrue(ranges.SequenceEqual(expected));
        }

        [Test]
        public void DotsDeserialization_SingleRange() {
            float range = _unitContentDots.GetRange(ref _system.CheckedStateRef, BaseTerrainType.Land);
            Assert.AreEqual(50f, range);
        }

        private const string DefaultUnitContentJsonString = @"{
  ""@type"" : ""content.model.contentitem.UnitContentItem"",
  ""goldFeature"" : false,
  ""id"" : ""0_AntiAir1 (41030)"",
  ""isAbstract"" : false,
  ""name"" : ""0_AntiAir1"",
  ""status"" : 0,
  ""versions"" : {
    ""1"" : {
      ""ranges"" : {
        ""LAND"" : 50.0,
        ""AIR"" : 25.0,
      },
      ""antiAirRanges"" : {
        ""LAND"" : 50.0,
      },
      ""attackAllowed"" : true,
      ""buildTime"" : ""PT2H""
    }
  }
}";
    }
}
