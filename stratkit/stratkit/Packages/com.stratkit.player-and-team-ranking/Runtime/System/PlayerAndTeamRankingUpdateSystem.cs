using Stratkit.Data.Provinces;
using Stratkit.ContentItemsModel.Data;
using Stratkit.GameInfoStateLoader;
using Stratkit.NewspaperStateLoader;
using Stratkit.PlayerAndTeamRanking.Util;
using Stratkit.PlayerStateLoader;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;

namespace Stratkit.PlayerAndTeamRanking {
    /// <summary>
    /// A system which listens to the changes in newspaper states ranking & team ranking
    /// updates and recalculates the player, team and effective ranking entities.
    /// </summary>
    [BurstCompile]
    [UpdateAfter(typeof(PlayerStatePostUpdateSystem))]
    public partial struct PlayerAndTeamRankingUpdateSystem : ISystem {
        private EntityQuery _newspaperDayQuery;
        private EntityQuery _pointsQuery;
        private EntityQuery _teamSettingsQuery;
        private EntityQuery _gameInfoQuery;
        private EntityQuery _poiProvinceOwnerQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state) {
            _newspaperDayQuery = SystemAPI.QueryBuilder()
                .WithAll<NewspaperDay>()
                .Build();
            _pointsQuery = SystemAPI.QueryBuilder().WithAll<VictoryPointsRequiredToWinSingleton>()
                .Build();
            _teamSettingsQuery = SystemAPI.QueryBuilder().WithAll<GameInfoTeamSettings>()
                .Build();
            _gameInfoQuery = SystemAPI.QueryBuilder().WithAll<GameInfoComponent>()
                .Build();

            _newspaperDayQuery.SetChangedVersionFilter(ComponentType.ReadOnly<NewspaperDay>());

            _poiProvinceOwnerQuery = SystemAPI.QueryBuilder().WithAll<ProvincePoiTag>().Build();

            state.RequireForUpdate(_pointsQuery);
            state.RequireForUpdate(_teamSettingsQuery);
            state.RequireForUpdate(_gameInfoQuery);
            state.RequireForUpdate(_newspaperDayQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state) {
            PlayerAndTeamRankingUtil.GetFilteredDayEntities(
                _gameInfoQuery.ToComponentDataArray<GameInfoComponent>(Allocator.Temp)[0].DayOfGame,
                in _newspaperDayQuery,
                out NativeArray<Entity> filteredEntities);
            if (filteredEntities.Length > 0) {
                CalculateRanking(ref state, filteredEntities);
            }

            filteredEntities.Dispose();
        }

        /// <summary>
        /// Calculates player, ranking and effective ranking for the given day.
        /// </summary>
        /// <param name="state">The system sate</param>
        /// <param name="dayEntities">Days of interest</param>
        [BurstCompile]
        private void CalculateRanking(ref SystemState state, NativeArray<Entity> dayEntities) {
            EntityManager em = state.EntityManager;
            GameInfoTeamSettings teamSettings =
                _teamSettingsQuery.ToComponentDataArray<GameInfoTeamSettings>(Allocator.Temp)[0];
            GameInfoComponent gameInfo = _gameInfoQuery.ToComponentDataArray<GameInfoComponent>(Allocator.Temp)[0];
            VictoryPointsRequiredToWinSingleton pointInfo =
                _pointsQuery.ToComponentDataArray<VictoryPointsRequiredToWinSingleton>(Allocator.Temp)[0];
            int requiredPoints = pointInfo.VictoryPoints;

            // dominion game mode has its own unique way of calculating the ranking
            // therefore, we gotta pass some additional data.
            NativeArray<ProvinceOwner> poiProvinceOwners;
            bool isDominionGame = em.HasComponent<ScenarioPoiConfigComponent>(gameInfo.ScenarioRef);
            if (isDominionGame) {
                requiredPoints = em.GetComponentData<ScenarioPoiConfigComponent>(gameInfo.ScenarioRef).WinningAmount;
                poiProvinceOwners = _poiProvinceOwnerQuery.ToComponentDataArray<ProvinceOwner>(Allocator.Temp);
            } else {
                poiProvinceOwners = new NativeArray<ProvinceOwner>(0, Allocator.Temp);
            }

            bool isDynamicTeamGame = teamSettings.IsDynamicTeamGame();
            foreach (Entity entity in dayEntities) {
                PlayerAndTeamRankingCalculator.CalculateAndUpdatePlayerRanking(
                    in entity,
                    requiredPoints,
                    isDominionGame,
                    in poiProvinceOwners,
                    in em);
                PlayerAndTeamRankingCalculator.CalculateAndUpdateTeamRanking(
                    in entity,
                    isDynamicTeamGame,
                    pointInfo.VictoryPoints,
                    pointInfo.TeamVictoryPoints,
                    in em);
                PlayerAndTeamRankingCalculator.CalculateAndUpdateEffectiveRanking(
                    in entity,
                    teamSettings.IsTeamGame(),
                    teamSettings.IsStaticTeamGame(),
                    in em);
            }

            poiProvinceOwners.Dispose();
        }
    }
}
