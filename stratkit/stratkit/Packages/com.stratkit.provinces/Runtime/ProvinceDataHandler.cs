using Stratkit.Data.Resources;
using Stratkit.PlayerStateLoader;
using Stratkit.Properties.Common;
using Stratkit.ServerCommunication;
using Unity.Entities;
using UnityEngine;

namespace Stratkit.Data.Provinces {
    /// <summary>
    /// Parses the data from the Province data into the corresponding entities
    /// </summary>
    /// <see cref="https://github.com/bytro/client-api-json-schema/wiki/ClientProvince"/>
    internal sealed class ProvinceDataHandler : IDataHandler {
        public const string StaticTypeIdentifier = Province.TypeIdentifier;

        public string TypeIdentifier => StaticTypeIdentifier;
        public string IdPropertyName => "id";
        private static string MoralePropertyName => "m";

        /// <see cref="https://github.com/bytro/client-api-json-schema/wiki/ClientProvince#mpm---maxresourceproductions"/>
        private static string MaxProductionMapPropertyName => "mpm";

        /// <see cref="https://github.com/bytro/client-api-json-schema/wiki/ClientProvince#arp---allresourceproductions"/>
        private static string AllResourceProductionsName =>
            "arp"; // The resource amount produced by this province per day

        public void Invoke(IDeserializationContext context, EntityCommandBuffer ecb) {
            if (!context.Node.TryGetInt64Value("o", out long ownerPlayerId)) {
                context.LogError("province node missing \"o\" owner property");
                return;
            }

            if (!context.TryResolveEntity((PlayerProfile.TypeIdentifier, ownerPlayerId), out Entity ownerPlayer)) {
                context.LogError($"Failed to resolve player with id {ownerPlayerId}");
                ownerPlayer = Entity.Null;
            }

            if (!context.Node.TryGetInt64Value(IdPropertyName, out long id)) {
                context.LogError($"Could not find Property '{IdPropertyName}' on context");
                return;
            }

            Entity provinceEntity = context.GetOrCreateEntity(ecb, $"Province{id}");

            // If we don't get morale from server it means morale is 0.
            context.Node.TryGetInt32Value(MoralePropertyName, out int morale);

            // Try get the daily regular and maximum resource production
            ParseResources<ResourceUnitsPerDay>(context, AllResourceProductionsName, ecb, provinceEntity);
            ParseResources<ProvinceDailyResourceMaxResourceProduction>(context, MaxProductionMapPropertyName, ecb, provinceEntity);

            ecb.AddComponent<ProvinceTag>(provinceEntity);
            ecb.AddComponent(provinceEntity, new ProvinceMorale { Morale = morale });

            context.Node.TryGetBoolValue("poi", out bool isPoi);
            if (isPoi) {
                ecb.AddComponent<ProvincePoiTag>(provinceEntity);
            }
            Debug.Log("poi check: " + context.Node);

            bool hasStationaryArmy =
                context.Node.TryGetInt64Value("sa", out long stationaryArmyId) && stationaryArmyId > 0;
            if (hasStationaryArmy) {
                ecb.AddComponent(provinceEntity, new ProvinceStationaryArmy { ArmyId = stationaryArmyId });
            } else {
                ecb.AddComponent(provinceEntity, new ProvinceStationaryArmy { ArmyId = -1 });
            }

            ProvinceOwner ownerComponent = new() {
                Value = ownerPlayer,
                Dirty = true,
            };
            ownerComponent.Publish(ecb, provinceEntity);
            ecb.AddComponent(provinceEntity, ownerComponent);

            if (ownerPlayerId == context.GameSessionDescriptor.PlayerId) {
                ecb.AddComponent<MyPlayerTag>(provinceEntity);
            } else {
                ecb.RemoveComponent<MyPlayerTag>(provinceEntity);
            }

            context.Node.TryGetInt32Value("uuc", out int unknownUpgradesCount);
            ecb.AddComponent(provinceEntity, new ProvinceUnknownUpgradesCount { Value = unknownUpgradesCount });

            context.StopRecurse = false;
        }

        /// <summary>
        /// Common functionality to parse the daily and maximum daily production for a province
        /// for all its resources
        /// </summary>
        /// <param name="context">Context with the data to parse</param>
        /// <param name="propertyName">Name of the property to fetch</param>
        /// <param name="ecb">The EntityCommandBuffer</param>
        /// <param name="provinceEntity">Entity to append the buffer to</param>
        /// <typeparam name="T">See implementors of <see cref="IProvinceDailyResourceProduction"/></typeparam>
        private static void ParseResources<T>(
            IDeserializationContext context,
            string propertyName,
            EntityCommandBuffer ecb,
            Entity provinceEntity
        ) where T : unmanaged, IBufferElementData, IProvinceDailyResourceProduction {
            DynamicBuffer<T> resourceProductionBuffer = ecb.AddBuffer<T>(provinceEntity);
            if (context.Node.TryGetObjectValue(propertyName, out DataObject? maxResources)) {
                foreach (string resourceIdString in maxResources.PropertyNames) {
                    int resourceId = int.Parse(resourceIdString);
                    if (maxResources.TryGetInt32Value(resourceIdString, out int production)) {
                        (string Value, long) resourceTypeId = (ResourceTypeIdentifier.Value, resourceId);
                        Entity resourceBalancing = context.ResolveOrCreateEntity(ecb, resourceTypeId);

                        T resourceProduction = default;
                        resourceProduction.Initialize(resourceId, (uint)production);
                        resourceProductionBuffer.Add(resourceProduction);
                    } else {
                        context.LogError(
                            $"{propertyName} node missing \"{resourceIdString}\" property."
                        );
                    }
                }
            }
        }
    }
}
