using Stratkit.Data.Provinces;
using Stratkit.MapMaker.ServerExport.Data;
using Stratkit.Properties.Loader;
using System.Collections.Generic;
using System.Linq;

namespace Stratkit.MapMaker.Editor {
    /// <summary>
    /// Importer implementation for getting point of interest (POI) information of province.
    /// Used for Dominion games to mark provinces as POI.
    /// </summary>
    public sealed class POIMjsBalancingImporter : ILocationMjsBalancingImporter {
        /// <summary>
        /// Method for invoking the import of province POI information.
        /// </summary>
        /// <param name="propertyScriptable">Provinces propertyScriptable</param>
        /// <param name="location">Reference to location</param>
        /// <param name="locationId">Id of location</param>
        public void ImportBalancingData(BasePropertyScriptable propertyScriptable, MjsLocation location, int locationId) {
            if (propertyScriptable is not ProvincePoiTagScriptable provincePoiTagScriptable) {
                return;
            }

            if (location == null) {
                return;
            }

            // Get POI information from JSON, default to false if not present
            bool isPointOfInterest = location.PointOfInterest ?? false;

            if (isPointOfInterest) {
                // Create a new POI tag entry for this province
                PropertyScriptable<ProvincePoiTag>.IdTuple poiTagEntry = new ProvincePoiTagScriptable.IdTuple {
                    Id = locationId,
                    Data = new ProvincePoiTag()
                };

                // Add the entry to the scriptable's values list
                List<PropertyScriptable<ProvincePoiTag>.IdTuple> currentValues = 
                    provincePoiTagScriptable.Values?.ToList() ?? new List<ProvincePoiTagScriptable.IdTuple>();
                
                // Remove any existing entry for this location ID to avoid duplicates
                currentValues.RemoveAll(entry => entry.Id == locationId);
                
                // Add the new entry
                currentValues.Add(poiTagEntry);
                
                // Update the scriptable
                provincePoiTagScriptable.Values = currentValues.ToArray();
            }
        }
    }
}
