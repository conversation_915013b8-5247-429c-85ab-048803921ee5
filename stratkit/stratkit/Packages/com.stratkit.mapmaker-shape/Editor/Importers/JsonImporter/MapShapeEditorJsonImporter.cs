using Newtonsoft.Json;
using Stillfront.Logging;
using Stratkit.Data.Provinces;
using Stratkit.Data.UpgradeGrounds;
using Stratkit.MapMaker.ServerExport.Data;
using Stratkit.Properties.Common;
using Stratkit.Properties.Loader;
using System;
using System.Collections.Generic;
using System.Linq;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.U2D;

namespace Stratkit.MapMaker.Editor {
    /// <summary>
    /// Static class that provides methods to import map data from JSON files.
    /// </summary>
    public static class MapShapeEditorJsonImporter {
        private const TerrainType DefaultTerrainType = TerrainType.Plains;

        /// <summary>
        /// Imports the geometry part of the map (provinces, connections, seapoints).
        /// </summary>
        /// <param name="mapJsonAsset">Reference to <see cref="TextAsset"/> containing definition of map to import.</param>
        /// <param name="shapeEditorMap">Reference to MapShapeEditorMap map.</param>
        /// <param name="graphicalInfo">Reference to graphical info file.</param>
        public static void ImportGeometry(
            TextAsset mapJsonAsset,
            MapShapeEditorMap shapeEditorMap,
            GraphicalInfo graphicalInfo) {
            if (!TryParseMap(mapJsonAsset, out MjsMap? map) || map == null) {
                return;
            }

            shapeEditorMap.Provinces = GetProvinces(map);
            shapeEditorMap.Connections = GetConnections(map);
            shapeEditorMap.Seapoints = GetSeapoints(map);

            graphicalInfo.Bounds = shapeEditorMap.CalculateMapBounds();
            graphicalInfo.Width = map.Width != null ? (int)map.Width : (int)math.ceil(graphicalInfo.Bounds.Max.x);
            graphicalInfo.Height = map.Height != null ? (int)map.Height : (int)math.ceil(graphicalInfo.Bounds.Max.y);
            graphicalInfo.OverlapX = map.OverlapX != null ? (int)map.OverlapX : 0;

            MapShapeEditorConnectionLocationIdCorrection connectionLocationIdCorrection = new();
            connectionLocationIdCorrection.CheckAndCorrect(shapeEditorMap);
        }

        /// <summary>
        /// Import the balancing part of the map (province data, players data).
        /// </summary>
        /// <param name="mapJsonAsset">Reference to <see cref="TextAsset"/> containing definition of map.</param>
        /// <param name="playerBalancingData">Reference to data scriptable containing map's player balancing data.</param>
        /// <param name="provinceBalancingData">Reference to data scriptable containing map's province balancing data.</param>
        /// <param name="provinceDataCityExtender">Reference to data scriptable containing title's province balancing data</param>
        public static void ImportBalancing(
            TextAsset mapJsonAsset,
            PropertiesCollectionScriptable playerBalancingData,
            PropertiesCollectionScriptable provinceBalancingData,
            ProvinceDataCityExtender provinceDataCityExtender,
            SpriteAtlas? flagsSpriteAtlas = null,
            IconAtlasedPropertyScriptable? flagsBalancingData = null
        ) {
            if (!TryParseMap(mapJsonAsset, out MjsMap? map) || map == null) {
                return;
            }

            List<ILocationMjsBalancingImporter> provinceBalancingImporters = new() {
                new ResourcesMjsBalancingImporter(),
                new ProvinceNameMjsBalancingImporter(),
                new ProvinceLocalizationKeyMjsBalancingImporter(),
                new OwnershipMjsBalancingImporter(),
                new CoreOwnershipMjsBalancingImporter(),
                new PopulationMjsBalancingImporter(),
                new TerrainTypeMjsBalancingImporter(DefaultTerrainType),
                new CityPresetsMjsBalancingImporter(provinceDataCityExtender),
                new POIMjsBalancingImporter(),
            };
            List<IPlayerMjsBalancingImporter> playerBalancingImporters = new() {
                new NationNameMjsBalancingImporter(),
                new LocalizationKeyMjsBalancingImporting(playerBalancingData),
                new InitialCapitalMjsBalancingImporter(),
                new FactionMjsBalancingImporter(),
                new DifficultyMjsBalancingImporter(),
                new ColorMjsBalancingImporter(),
                new IsPlayerAIMjsBalancingImporter(),
                new FlagMjsBalancingImporter(flagsSpriteAtlas),
            };

            ImportProvincesData(map, provinceBalancingData, provinceBalancingImporters);
            ImportPlayersData(map, playerBalancingData, playerBalancingImporters);
        }

        /// <summary>
        /// Import the geometry and balancing part of the map (provinces, connections, seapoints, province data, players data).
        /// </summary>
        /// <param name="mapJsonAsset">Reference to <see cref="TextAsset"/> containing definition of map.</param>
        /// <param name="shapeEditorMap">Reference to MapShapeEditorMap map.</param>
        /// <param name="graphicalInfo">Reference to graphical info file.</param>
        /// <param name="playerBalancingData">Reference to data scriptable containing map's player balancing data.</param>
        /// <param name="provinceBalancingData">Reference to data scriptable containing map's province balancing data.</param>
        /// <param name="provinceDataCityExtender">Reference to data scriptable containing title's province balancing data</param>
        public static void ImportAll(
            TextAsset mapJsonAsset,
            MapShapeEditorMap shapeEditorMap,
            GraphicalInfo graphicalInfo,
            PropertiesCollectionScriptable playerBalancingData,
            PropertiesCollectionScriptable provinceBalancingData,
            ProvinceDataCityExtender provinceDataCityExtender,
            SpriteAtlas? flagsSpriteAtlas = null,
            IconAtlasedPropertyScriptable? flagsBalancingData = null
        ) {
            ImportGeometry(mapJsonAsset, shapeEditorMap, graphicalInfo);
            ImportBalancing(mapJsonAsset, playerBalancingData, provinceBalancingData, provinceDataCityExtender, flagsSpriteAtlas, flagsBalancingData);
        }

        private static List<MapShapeEditorProvince> GetProvinces(MjsMap mjsMap) {
            List<MapShapeEditorProvince> provinces = new();

            foreach (MjsLocation location in mjsMap.Locations) {
                // If location is type of seapoint, skip it
                if (location.LocationType == MjsLocationType.SeaPoint) {
                    continue;
                }

                Dictionary<int, MapShapeEditorPrimitive> borderShapes = new();
                int currentBorder = 0;
                foreach (double[] border in location.Borders) {
                    // Calculate bounds for each border
                    List<Vector3> borderPoints = new();
                    for (int i = 0; i < border.Length; i += 2) {
                        Vector3 point2 = new((float)border[i], 0, (float)(mjsMap.Height! - (float)border[i + 1]));
                        borderPoints.Add(point2);
                    }

                    // Reverse the order of points to make sure they are in correct winding order,
                    // influencing polygon facing of province borders and extrusions
                    borderPoints.Reverse();

                    borderShapes.Add(currentBorder, new MapShapeEditorPrimitive {
                            Points = borderPoints,
                            Index = currentBorder++,
                    });
                }

                TerrainType terrainType = string.IsNullOrEmpty(location.TerrainType) ? DefaultTerrainType : TerrainHelper.ToTerrainType(location.TerrainType);
                MapShapeEditorProvince province = new() {
                    Id = location.Id == null ? 0 : (int)location.Id,
                    NationId = location.OwnerId == null ? string.Empty : location.OwnerId.ToString(),
                    CapitalPos = new Vector3((float)location.Capital[0], 0, (float)(mjsMap.Height! - (float)location.Capital[1])),
                    TerrainType = (MapShapeEditorTerrainType)terrainType,
                    ShapePrimitives = borderShapes,
                };

                provinces.Add(province);
            }

            return provinces;
        }

        private static MapShapeEditorConnection GetConnections(MjsMap mjsMap) {
            MapShapeEditorConnection connectionContainer = new() {
                Name = MapShapeEditorConstants.ConnectionsObjectName,
                Color = Color.yellow,
            };

            int connectionId = 0;
            foreach (MjsConnection mjsConnection in mjsMap.Connections) {
                Vector3 startPoint = new((float)mjsConnection.Path[0], 0, (float)(mjsMap.Height! - (float)mjsConnection.Path[1]));
                Vector3 endPoint = new((float)mjsConnection.Path[2], 0, (float)(mjsMap.Height! - (float)mjsConnection.Path[3]));

                MapShapeEditorPrimitive connectionPrimitive = new() {
                    Index = connectionId++,
                    Points = new List<Vector3> { startPoint, endPoint },
                    Closed = false,
                };

                connectionContainer.ShapePrimitives.Add(connectionPrimitive.Index, connectionPrimitive);
            }

            return connectionContainer;
        }

        private static List<MapShapeEditorSeapoint> GetSeapoints(MjsMap mjsMap) {
            List<MapShapeEditorSeapoint> seapoints = new();

            foreach (MjsLocation location in mjsMap.Locations) {
                // If location is not type of seapoint, skip it
                if (location.LocationType != MjsLocationType.SeaPoint) {
                    continue;
                }

                MapShapeEditorSeapoint seapoint = new() {
                    Id = location.Id == null ? 0 : (int)location.Id,
                    Position = new Vector3((float)location.Capital[0], 0, (float)(mjsMap.Height! - (float)location.Capital[1])),
                    NeighbourIds = location.NeighbourIds == null ? new List<int>() : Array.ConvertAll(location.NeighbourIds, x => (int)x).ToList(),
                    Color = Color.blue,
                };
                seapoints.Add(seapoint);
            }

            return seapoints;
        }

        private static void ImportProvincesData(
            MjsMap mjsMap,
            PropertiesCollectionScriptable provinceBalancingData,
            List<ILocationMjsBalancingImporter> locationBalancingImporters
        ) {
            PropertiesCollectionImportUtils.ClearAll(provinceBalancingData.Data);
            provinceBalancingData.Ids.Values = Array.Empty<DataId>();

            foreach (MjsLocation location in mjsMap.Locations) {
                // If location is type of seapoint, skip it
                if (location.LocationType == MjsLocationType.SeaPoint) {
                    continue;
                }

                int locationId = location.Id != null ? (int)location.Id : 0;
                provinceBalancingData.Ids.AddNewDefault(locationId);

                foreach (BasePropertyScriptable propertyScriptable in provinceBalancingData.Data) {
                    if (!propertyScriptable.HasElement(locationId)) {
                        propertyScriptable.AddNewDefault(locationId);
                    }

                    // Invoke each importer to import specific data
                    foreach (ILocationMjsBalancingImporter locationImporter in locationBalancingImporters) {
                        locationImporter.ImportBalancingData(propertyScriptable, location, locationId);
                    }

                    EditorUtility.SetDirty(propertyScriptable);
                }
            }

            EditorUtility.SetDirty(provinceBalancingData.Ids);
            AssetDatabase.SaveAssets();
        }

        private static void ImportPlayersData(
            MjsMap mjsMap,
            PropertiesCollectionScriptable playersBalancingData,
            List<IPlayerMjsBalancingImporter> playerBalancingImporters
        ) {
            PropertiesCollectionImportUtils.ClearAll(playersBalancingData.Data);
            playersBalancingData.Ids.Values = Array.Empty<DataId>();

            foreach (MjsPlayer player in mjsMap.Players) {
                int playerId = player.Id != null ? (int)player.Id : 0;
                playersBalancingData.Ids.AddNewDefault(playerId);

                foreach (BasePropertyScriptable propertyScriptable in playersBalancingData.Data) {
                    if (!propertyScriptable.HasElement(playerId)) {
                        propertyScriptable.AddNewDefault(playerId);
                    }

                    // Invoke each importer to import specific data
                    foreach (IPlayerMjsBalancingImporter playerImporter in playerBalancingImporters) {
                        playerImporter.ImportBalancingData(propertyScriptable, player, playerId);
                    }

                    EditorUtility.SetDirty(propertyScriptable);
                }
            }

            EditorUtility.SetDirty(playersBalancingData.Ids);
            AssetDatabase.SaveAssets();
        }

        private static bool TryParseMap(TextAsset mapJsonAsset, out MjsMap? map) {
            try {
                map = MjsMap.FromJson(mapJsonAsset.text);
            } catch (JsonReaderException e) {
                Log.Error($"Failed to parse map json file, make sure provided file contains json of proper schema. {e.Message}"
                );
                map = null;
                return false;
            }

            if (map == null) {
                Log.Error($"Failed to parse map json file, make sure provided file contains json of proper schema.");
                return false;
            }

            return true;
        }
    }
}
