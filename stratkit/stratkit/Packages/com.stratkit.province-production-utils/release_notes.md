# com.stratkit.province-production-utils

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 1.0.0
- Initial version

## Version 1.0.1
- Fix required building in build queue not being detected as replaced

## Version 1.1.0
 - Add more info into production items

## Version 1.2.0
 - Add ProvinceProductionRequirementsUtils

## Version 1.3.0
 - Add support for build queue actions

## Version 1.4.0
 - Adjust ProvinceProductionRequirementUtils::GetRequiredUpgradesInfo to only check if upgrade is fully built

## Version 1.5.0
 - Refactor ProvinceProductionUtils to have separate methods for ongoing items

## Version 1.6.0
 - Use merged army command package.

## Version 1.7.0
 - Apply auto-refactor for BE-Content-Items

## Version 1.8.0
 - Compatibility fixes for ContentItemsModel

## Version 1.9.0
 - Compatibility fixes for ContentItemsModel

## Version 1.10.0
 - Compatibility fixes for ContentItemsModel

## Version 1.10.1
 - Make province production utils get buffers as read-only

## Version 1.11.0
 - removed a no longer existing package from packages.json

## Version 1.11.1
 - Fix ProvinceProductionManagementUtils cannot be reused without domain reloading

## Version 1.11.2
 - Fix incorrect repair amount for constructions

## Version 1.12.0
- Updated to support new assemblies in ContentItems package

## Version 1.13.0
- Add commands to set and remove rally points from a province
