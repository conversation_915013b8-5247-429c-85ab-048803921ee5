{"name": "com.stratkit.province-production-utils", "version": "1.13.0", "displayName": "Stratkit Province production utils", "unity": "2022.3", "unityRelease": "10f1", "description": "A utility class responsible for providing information about unit production and building construction info about a province", "publishConfig": {"registry": "https://npm.pkg.github.com/@bytro"}, "dependencies": {"com.stratkit.army-air": "1.13.0", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.2.0", "com.stratkit.buildings": "3.2.0", "com.stratkit.collections": "1.6.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.data": "1.5.0", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.foreign-affairs-state-loader": "1.7.0", "com.stratkit.game-state-loader": "1.5.1", "com.stratkit.moddable-upgrade-state-loader": "1.16.0", "com.stratkit.mods": "1.7.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.production-core": "1.1.0", "com.stratkit.production-units": "1.5.0", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.6.0", "com.stratkit.provinces": "1.13.0", "com.stratkit.railroad": "1.2.0", "com.stratkit.resources": "0.2.0", "com.stratkit.server-communication": "1.13.1", "com.stratkit.units": "3.3.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.mathematics": "1.3.1"}, "author": {"name": "Bytro", "email": "<EMAIL>", "url": "https://www.bytro.com"}}