using Stratkit.GameStateLoader;
using Stratkit.Map;
using Stratkit.ModdableUpgrade.StateLoader;
using Stratkit.Production;
using Stratkit.Properties.Loader;
using Stratkit.ServerCommunication;
using Stratkit.UnitsProduction;
using System.Collections.Generic;
using Unity.Entities;

namespace Stratkit.ProvinceProductionUtils {
    /// <summary>
    /// Responsible for managing province production and related actions:
    /// - starting & queueing productions
    /// - canceling productions
    /// - todo: moving build queue items around
    /// </summary>
    public static class ProvinceProductionManagementUtils {
        /// <summary>
        /// Starts the given production on the given provinces.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">list of provinces to start production on</param>
        /// <param name="unitOrUpgradeType">type entity</param>
        /// <param name="isUnit">true if it's for a unit</param>
        public static void StartProduction(
            EntityManager em,
            List<Entity> provinces,
            Entity unitOrUpgradeType,
            bool isUnit
        ) {
            if (!isUnit) {
                StartConstruction(em, provinces, unitOrUpgradeType);
            } else {
                StartUnitProduction(em, provinces, unitOrUpgradeType);
            }
        }

        /// <summary>
        /// Enqueues the given production on the given provinces.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">list of provinces to enqueue production on</param>
        /// <param name="unitOrUpgradeType">type entity</param>
        /// <param name="isUnit">true if it's for a unit</param>
        public static void EnqueueProduction(
            EntityManager em,
            List<Entity> provinces,
            Entity unitOrUpgradeType,
            bool isUnit
        ) {
            if (!isUnit) {
                EnqueueConstruction(em, provinces, unitOrUpgradeType);
            } else {
                EnqueueUnitProduction(em, provinces, unitOrUpgradeType);
            }
        }

        /// <summary>
        /// Cancels the production on the given provinces.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">list of provinces to cancel production on</param>
        /// <param name="isUnit">true if it's for a unit</param>
        public static void CancelProduction(EntityManager em, List<Entity> provinces, bool isUnit) {
            UpdateProvinceAction action = new UpdateProvinceActionBuilder(
                    !isUnit ? UpdateProvinceMode.CancelBuilding : UpdateProvinceMode.CancelUnit,
                    provinces
                )
                .Build();
            SendAction(em, action);
        }

        /// <summary>
        /// Removes the item from the global build queue
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="index">index of the build queue item</param>
        public static void CancelBuildQueueItem(EntityManager em, int index) {
            BuildQueueAction action = BuildQueueAction.CreateRemoveAction(new[] { index });
            SendAction(em, action);
        }

        /// <summary>
        /// Starts a building constructon on the desired provinces
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">provinces</param>
        /// <param name="upgradeType">the upgrade type</param>
        /// <param name="groundIndex">ground index slot</param>
        public static void StartConstruction(
            EntityManager em,
            List<Entity> provinces,
            Entity upgradeType,
            int groundIndex = 0
        ) {
            int upgradeId = em.GetComponentData<DataId>(upgradeType).Id;
            SendAction(
                em,
                new UpdateProvinceActionBuilder(UpdateProvinceMode.Building, provinces)
                    .SetUpgradeType(new SpecialUpgradeBuilding(upgradeId, groundIndex))
                    .Build()
            );
        }

        /// <summary>
        /// Starts unit production on the desired provinces
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">provinces</param>
        /// <param name="unitType">unit type</param>
        /// <param name="count">count to produce</param>
        public static void StartUnitProduction(
            EntityManager em,
            List<Entity> provinces,
            Entity unitType,
            int count = 1
        ) =>
            SendAction(
                em,
                new UpdateProvinceActionBuilder(UpdateProvinceMode.Unit, provinces)
                    .SetUpgradeType(new SpecialUpgradeUnit(new ProductionUnit(unitType, count)))
                    .Build()
            );


        /// <summary>
        /// Sends the command to set the given rally point for the province
        /// </summary>
        /// <param name="rallyPoint">If null, this will clear the rally point instead</param>
        public static void SetRallyPoint(EntityManager em, Entity province, MapCoordinates? rallyPoint) {
            SendAction(
                em,
                new UpdateProvinceActionBuilder(UpdateProvinceMode.DeploymentTarget, province)
                    .SetDeploymentTarget(rallyPoint)
                    .Build()
            );
        }

        /// <summary>
        /// Sends the command to remove the existing rally point of a province
        /// </summary>
        public static void ClearRallyPoint(EntityManager em, Entity province) {
            SetRallyPoint(em, province, null);
        }

        /// <summary>
        /// Enqueues unit production on the desired provinces
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">provinces</param>
        /// <param name="unitType">unit type</param>
        /// <param name="count">count to produce</param>
        public static void EnqueueUnitProduction(
            EntityManager em,
            List<Entity> provinces,
            Entity unitType,
            int count = 1
        ) {
            List<ServerBuildQueueEntry> entriesToUse = new();

            foreach (Entity province in provinces) {
                int provinceId = em.GetComponentData<DataId>(province).Id;
                ProductionUnit unit = new(unitType, count);
                SpecialUpgradeUnit specialUpgradeUnit = new(unit);
                entriesToUse.Add(new ServerBuildQueueEntry(provinceId, specialUpgradeUnit));
            }

            SendAction(em, BuildQueueAction.CreateAddAction(entriesToUse));
        }

        /// <summary>
        /// Enqueues construction on the desired provinces
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="provinces">provinces</param>
        /// <param name="upgradeType">upgrade type</param>
        /// <param name="groundIndex">ground index slot</param>
        public static void EnqueueConstruction(
            EntityManager em,
            List<Entity> provinces,
            Entity upgradeType,
            int groundIndex = 0
        ) {
            List<ServerBuildQueueEntry> entriesToUse = new();

            foreach (Entity province in provinces) {
                int provinceId = em.GetComponentData<DataId>(province).Id;
                int typeId = em.GetComponentData<DataId>(upgradeType).Id;
                SpecialUpgradeBuilding specialUpgradeBuilding = new(typeId, groundIndex);
                entriesToUse.Add(new ServerBuildQueueEntry(provinceId, specialUpgradeBuilding));
            }

            SendAction(em, BuildQueueAction.CreateAddAction(entriesToUse));
        }

        /// <summary>
        /// Triggers sending the action to the game server
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="action">action to send</param>
        private static void SendAction(EntityManager em, ServerCommand action) =>
            em.SetComponentData(
                em.CreateEntity(
                    stackalloc ComponentType[] {
                        ComponentType.ReadWrite<ServerCommandRequest>(),
                    }
                ),
                new ServerCommandRequest { Value = action }
            );
    }
}
