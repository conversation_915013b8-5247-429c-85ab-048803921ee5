{"name": "Stratkit.ProvinceProductionUtils", "rootNamespace": "Stratkit.ProvinceProductionUtils", "references": ["Stratkit.ArmyAir", "Stratkit.ArmyAttackCommand", "Stratkit.ArmyMoveCommand", "Stratkit.ArmyStateLoader", "Stratkit.BuildQueueState", "Stratkit.BuildSystem", "Stratkit.Collections", "Stratkit.ContentItems.Level", "Stratkit.Data.Buildings", "Stratkit.Data.Mods", "Stratkit.Data.Provinces", "Stratkit.Data.Resources", "Stratkit.Data.Units", "Stratkit.Entities", "Stratkit.EntitiesReactive", "Stratkit.ForeignAffairsStateLoader", "Stratkit.GameInfoStateLoader", "Stratkit.GameStateLoader", "Stratkit.ModdableUpgrade.StateLoader", "Stratkit.PlayerStateLoader", "Stratkit.Production", "Stratkit.Properties.Common", "Stratkit.Properties.Loader", "Stratkit.Railroad", "Stratkit.ServerCommunication", "Stratkit.UnitsProduction", "Unity.Burst", "Unity.Collections", "Unity.Entities", "Unity.Mathematics", "Stratkit.ContentItemsModel", "Stratkit.ContentItemsModel.Common", "Stratkit.ContentItemsModel.Generated", "Stratkit.Ui.Core", "Stratkit.Map"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}