﻿using Stillfront.Logging;
using Stratkit.Entities;
using Stratkit.Entities.Reactive.ProducerConsumer;
using Stratkit.RtsCamera.Components;
using Stratkit.RtsCamera.Config;
using Stratkit.RtsCamera.Internal;
using Stratkit.RtsCamera.Tween;
using Stratkit.UserInputActions;
using Stratkit.UserInputActions.Components;
using System;
using System.Diagnostics;
using Unity.Entities;
using UnityEngine;

namespace Stratkit.RtsCameraController {
    /// <summary>
    /// This system controls camera movement in a different "focus" mode, where camera position is not related
    /// to user input actions but by commands.
    /// </summary>
    public sealed partial class CameraFocusTweenScheduleSystem : SystemBase, IConsumerManaged<CameraMoveRequest>, IConsumerManaged<CameraStopRequest> {
        protected override void OnCreate() {
            RequireForUpdate<CameraConfig>();
            RequireForUpdate<CameraContainer>();
            RequireForUpdate<CameraControllerConfig>();

            Entity entity = EntityManager.CreateEntity(
                typeof(CameraControllerTweenTag),
                typeof(CameraFocusTweenComponent),
                typeof(CameraTransformationLock)
            );

            EntityManager.SetComponentData(entity, new CameraFocusTweenComponent());
            EntityManager.SetComponentEnabled<CameraFocusTweenComponent>(entity, false);
            EntityManager.SetComponentEnabled<CameraTransformationLock>(entity, false);

            ProducerManager.GetOrCreateManaged<CameraMoveRequest>(EntityManager).RegisterConsumer(this);
            ProducerManager.GetOrCreateManaged<CameraStopRequest>(EntityManager).RegisterConsumer(this);
        }

        protected override void OnUpdate() {
            this.ConsumeAll<CameraMoveRequest>();
            this.ConsumeAll<CameraStopRequest>();
        }

        protected override void OnDestroy() {
            ProducerManager.TryGetManaged<CameraMoveRequest>(EntityManager)?.UnregisterConsumer(this);
            ProducerManager.TryGetManaged<CameraStopRequest>(EntityManager)?.UnregisterConsumer(this);
            this.DestroyConsumer<CameraMoveRequest>();
            this.DestroyConsumer<CameraStopRequest>();
        }

        private void ScheduleTween(CameraMoveRequest request) {
            // Get camera transform
            CameraContainer cameraContainer = SystemAPI.ManagedAPI.GetSingleton<CameraContainer>();
            Camera camera = cameraContainer.Camera;
            if (camera == null) {
                return;
            }

            Transform transform = camera.transform;

            // Get configs
            CameraConfig configRef = SystemAPI.ManagedAPI.GetSingleton<CameraConfig>();
            ConfigData cameraConfig = configRef.Current;
            CameraControllerConfig config = SystemAPI.ManagedAPI.GetSingleton<CameraControllerConfig>();
            ScheduleTween(transform, request, config.DefaultDuration, config.DefaultEasing, cameraConfig.Zoom);
        }

        [Conditional("UNITY_EDITOR")]
        private static void ValidateEasing(AnimationCurve easing) {
            if (easing.keys.Length < 2) {
                Log.Error($"To small count of key frames in the passed easing.");
            }

            if (easing.keys[0].time != 0f) {
                Log.Error($"Passed easing should start with time = 0");
            }

            if (easing.keys[0].value != 0f) {
                Log.Error($"Passed easing should start with value = 0");
            }

            if (Math.Abs(easing.keys[^1].time - 1f) > 0f) {
                Log.Error($"Passed easing should end with time = 1");
            }

            if (Math.Abs(easing.keys[^1].value - 1f) > 0f) {
                Log.Error($"Passed easing should end with value = 1");
            }
        }

        private void ScheduleTween(Transform target, CameraMoveRequest request, float defaultDuration, AnimationCurve defaultEasing, ZoomConfig zoomConfig) {
            AnimationCurve easing = request.Easing ?? defaultEasing;
            ValidateEasing(easing);

            // store previous camera position in the "free mode"
            if (request.MovementMode == CameraMovementMode.WithStoreLastFreeModePosition) {
                TryStoreFreeModePosition();
            }

            // determine destination
            CameraFocusData destination;
            Entity entity = SystemAPI.GetSingletonEntity<CameraControllerTweenTag>();
            if (request.MovementMode == CameraMovementMode.ToLastFreeModePosition) {
                if (EntityManager.HasComponent<LastFreeModeCameraPositionComponent>(entity)) {
                    destination = EntityManager.GetComponentData<LastFreeModeCameraPositionComponent>(entity).Value;

                    // "consume" stored position
                    EntityManager.RemoveComponent<LastFreeModeCameraPositionComponent>(entity);
                } else {
                    Log.Error($"Camera tween entity doesn't have {nameof(LastFreeModeCameraPositionComponent)}");
                    return;
                }
            } else {
                destination = new CameraFocusData { Distance = request.Distance, LookAtPoint = request.LookAtPoint };
            }

            // compute duration
            float duration = request.Duration >= 0f ? request.Duration : defaultDuration;

            // create the tween
            CameraFocusTweenComponent tween = EntityManager.GetComponentData<CameraFocusTweenComponent>(entity);
            tween.Data = new CameraFiniteTweenData(duration, target);
            tween.From = CameraControllerUtils.GetCurrentCameraFocusData(target);
            tween.To = destination;
            tween.ZoomConfig = zoomConfig;
            tween.Easing = easing;
            tween.FollowTarget = request.FollowTarget;
            tween.OnComplete = request.OnComplete;

            if (duration <= 0f) {
                tween.Step(0f);
            } else {
                // block user actions
                tween.UserActionsLock ??= new SystemLock<UserActionInWorldDetectionSystem>(World);

                // fire the tween
                EntityManager.SetComponentEnabled<CameraFocusTweenComponent>(entity, true);
                EntityManager.SetComponentEnabled<CameraTransformationLock>(entity, true);
            }
        }

        private void TryStoreFreeModePosition() {
            Entity entity = SystemAPI.GetSingletonEntity<CameraControllerTweenTag>();
            if (EntityManager.HasComponent<LastFreeModeCameraPositionComponent>(entity)) {
                return;
            }

            // Get camera transform
            CameraContainer cameraContainer = SystemAPI.ManagedAPI.GetSingleton<CameraContainer>();
            Camera camera = cameraContainer.Camera;
            if (camera == null) {
                return;
            }

            EntityManager.AddComponent<LastFreeModeCameraPositionComponent>(entity);
            EntityManager.SetComponentData(
                entity,
                new LastFreeModeCameraPositionComponent {
                    Value = CameraControllerUtils.GetCurrentCameraFocusData(camera.transform),
                }
            );
        }

        private void StopTween() {
            foreach ((CameraFocusTweenComponent tween, Entity entity) in
                SystemAPI
                    .Query<CameraFocusTweenComponent>()
                    .WithOptions(EntityQueryOptions.IgnoreComponentEnabledState)
                    .WithEntityAccess()) {
                tween.Data = default;
                tween.From = new CameraFocusData { Distance = 0f, LookAtPoint = Vector3.zero };
                tween.To = new CameraFocusData { Distance = 0f, LookAtPoint = Vector3.zero };
                tween.ZoomConfig = null;
                tween.Easing = null;
                tween.FollowTarget = null;
                tween.TryUnlockUserActions();

                EntityManager.SetComponentEnabled<CameraFocusTweenComponent>(entity, false);
                EntityManager.SetComponentEnabled<CameraTransformationLock>(entity, false);
            }
        }

        public void Consume(Entity subjectEntity, CameraMoveRequest request) {
            ScheduleTween(request);
        }

        public void Consume(Entity subjectEntity, CameraStopRequest request) {
            StopTween();
        }
    }
}
