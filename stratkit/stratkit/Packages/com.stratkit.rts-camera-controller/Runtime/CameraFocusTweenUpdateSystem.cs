﻿using Stratkit.RtsCamera;
using Stratkit.RtsCamera.Components;
using Stratkit.UserInputActions;
using Unity.Entities;

namespace Stratkit.RtsCameraController {
    /// <summary>
    /// This system is responsible for updating the tween animation
    /// </summary>
    [UpdateBefore(typeof(CameraTransformationSystem))]
    public sealed partial class CameraFocusTweenUpdateSystem : SystemBase {
        protected override void OnCreate() {
            RequireForUpdate<DeviceAPIComponent>();
        }

        protected override void OnUpdate() {
            UpdateTween(SystemAPI.ManagedAPI.GetSingleton<DeviceAPIComponent>().Time.DeltaTime);
        }

        private void UpdateTween(float dt) {
            foreach ((CameraFocusTweenComponent tween, Entity entity) in SystemAPI.Query<CameraFocusTweenComponent>().WithEntityAccess()) {
                if (!tween.Step(dt)) {
                    continue;
                }

                EntityManager.SetComponentEnabled<CameraFocusTweenComponent>(entity, false);
                EntityManager.SetComponentEnabled<CameraTransformationLock>(entity, false);
                tween.TryUnlockUserActions();
                tween.OnComplete?.Invoke();
            }
        }
    }
}
