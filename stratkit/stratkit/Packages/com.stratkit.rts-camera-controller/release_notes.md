# com.stratkit.rts-camera-controller

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 0.1.0
 - Initial version

## Version 0.1.1
 - Add utils class

## Version 0.1.2
 - Add Missing RequireForUpdate on System

## Version 0.1.3
 - Add `CameraControllerUtils.AlignMapToScreenPoint`

## Version 0.1.4
 - Disable automatic reference to assembly.

## Version 0.2.0
 - Add `onComplete` callback after camera movement.

## Version 0.2.1
 - Lock camera during tweening.
