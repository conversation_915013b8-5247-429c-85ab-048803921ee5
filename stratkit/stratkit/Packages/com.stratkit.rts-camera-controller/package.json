﻿{
  "name": "com.stratkit.rts-camera-controller",
  "version": "0.2.1",
  "displayName": "Stratkit Rts Camera Controller",
  "unity": "2022.3",
  "unityRelease": "0f1",
  "description": "Additional features for controlling camera based on com.stratkit.rts-camera module.",
  "dependencies": {
    "com.unity.entities": "1.0.16",
    "com.stratkit.core": "1.0.3",
    "com.stratkit.entities-core": "2.10.1",
    "com.stratkit.entities-reactive": "1.0.1",
    "com.stratkit.rts-camera": "1.3.1",
    "com.stratkit.service-injector": "1.0.1",
    "com.stratkit.user-input-actions": "1.2.1",
    "com.unity.collections": "2.1.4",
    "com.unity.mathematics": "1.2.6",
    "com.stillfront.logging": "3.1.2",
    "com.unity.burst": "1.8.8"
  },
  "publishConfig": {
    "registry": "https://npm.pkg.github.com/@bytro"
  },
  "author": {
    "name": "Bytro",
    "email": "<EMAIL>",
    "url": "https://www.bytro.com"
  }
}
