using Newtonsoft.Json.Linq;
using Stratkit.Data.Provinces;
using System;
using System.Collections.Generic;
using Unity.Mathematics;

namespace Stratkit.GameServerPretender {
    /// <summary>
    /// Provides access to location (province, sea point, ...) data contained in a JSON object.
    /// </summary>
    public struct LocationView {
        public const string ProvinceType = Province.TypeIdentifier;
        public const string SeaPointType = "sp";

        public readonly JObject Object;

        public LocationView(JObject locationObject) {
            if (!locationObject.TryGetStringValue("@c", out string? type)
                || (type != ProvinceType && type != SeaPointType)
            ) {
                throw new Exception("invalid type");
            }

            Object = locationObject;
        }

        public string Type => Object.TryGetStringValue("@c", out string? value) ? value : string.Empty;

        public long Id => Object.TryGetInt64Value("id", out long value) ? value : -1;

        public IEnumerable<ModdableUpgradeView> ModdableUpgrades() {
            if (Object.TryGetArrayValue("us", out JArray? array)) {
                foreach (JToken token in array) {
                    if (token.Type == JTokenType.Object) {
                        yield return new ModdableUpgradeView((JObject)token);
                    }
                }
            }
        }

        public void SetModdableUpgrade(ModdableUpgradeView upgrade) {
            Object.GetOrCreateArrayValue("us").Add(upgrade.Object);
        }

        public CurrentProductionView? CurrentProduction {
            get {
                if (!Object.TryGetObjectValue("pi", out JObject? value)) {
                    return null;
                }
                return new CurrentProductionView(value);
            }
            set {
                if (value != null) {
                    Object["pi"] = value.Value.Object;
                } else {
                    Object.Remove("pi");
                }
            }
        }

        public long OwnerId {
            get => Object.TryGetInt64Value("o", out long value) ? value : -1;
            set => Object["o"] = value;
        }

        // position of province capital or sea point
        public float2 Position {
            get => Object.TryGetFloat2Value("c", out float2 value) ? value : default;
            set => Object["c"] = JObjectExtensions.FromFloat2(value);
        }

        public string Name {
            get => Object.TryGetStringValue("n", out string? value) ? value : string.Empty;
            set => Object["n"] = value;
        }

        public bool? PointOfInterest {
            get => Object.TryGetBoolValue("poi", out bool value) ? value : null;
            set {
                if (value.HasValue && value.Value) {
                    Object["poi"] = true;
                } else {
                    Object.Remove("poi"); // Remove the property if false or null to minimize JSON size
                }
            }
        }

        public void SetResourceProduction(long resourceId, double value) {
            Object.GetOrCreateObjectValue("arp")[resourceId.ToString()] = value;
        }

        public double GetResourceProduction(long resourceId) {
            JToken? token = Object.GetOrCreateObjectValue("arp")[resourceId.ToString()];
            if (token is not { Type: JTokenType.Float }) {
                return 0;
            }

            return token.Value<double>();
        }

        public IEnumerable<(long ResourceId, double UnitsPerDay)> ResourceProductions() {
            if (Object.TryGetObjectValue("arp", out JObject? resourceProductions)) {
                foreach (JProperty production in resourceProductions.Properties()) {
                    if (!long.TryParse(production.Name, out long resourceId)
                        || production.Value.Type != JTokenType.Float
                    ) {
                        continue;
                    }
                    yield return (resourceId, production.Value.Value<double>());
                }
            }
        }

        public static LocationView CreateProvince(long id) => new(new() {
            { "@c", ProvinceType },
            { "id", id },
            { "n", string.Empty }, // name
            { "c", JObjectExtensions.FromFloat2(default(float2)) }, // position
            { "o", -1 }, // owner player id
        });
    }
}
