using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Stratkit.Data.Players;
using Stratkit.Data.Provinces;
using Stratkit.Data.Resources;
using Stratkit.Map;
using Stratkit.Properties.Common;
using Stratkit.Properties.Loader;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Stratkit.GameServerPretender {
    internal static class GameStateFactory {
        public static async Task<JObject> GetOrCreateGameStateAsync(GameServerPretenderConfig config) {
            if (config.LoadSnapshot) {
                TextAsset gameStateAsset = await Addressables.LoadAssetAsync<TextAsset>(config.GameStateSnapshot).Task;
                try {
                    return JObject.Parse(gameStateAsset.text);
                } catch (JsonReaderException error) {
                    throw new Exception("Failed to parse game state JSON.", error);
                }
            }

            GameStateView gameState = GameStateView.Create();

            PlayerStateView playerState = PlayerStateView.Create();
            gameState.AddState(playerState);
            MapStateView mapState = MapStateView.Create();
            gameState.AddState(mapState);
            ResourceStateView resourceState = ResourceStateView.Create();
            gameState.AddState(resourceState);
            ForeignAffairsStateView foreignAffairsState = ForeignAffairsStateView.Create();
            gameState.AddState(foreignAffairsState);
            ArmyStateView armyState = ArmyStateView.Create();
            gameState.AddState(armyState);
            GameInfoStateView gameInfoState = GameInfoStateView.Create();
            gameState.AddState(gameInfoState);

            PropertiesCollectionScriptable resourceTypeData = config.ResourceTypeBalancingData;
            PropertiesCollectionScriptable playersData = config.PlayersBalancingData;
            PropertiesCollectionScriptable provincesData = config.ProvincesBalancingData;

            IList<int> resourceTypeIdList = resourceTypeData.Ids.IdList;
            PropertyScriptableManaged<ContentItemName>.IdTuple[] resourceTypeNames = resourceTypeData.GetPropertyData<ContentItemName>();

            IList<int> playerIdList = playersData.Ids.IdList;
            PropertyScriptableManaged<ContentItemName>.IdTuple[] playerNames = playersData.GetPropertyData<ContentItemName>();
            PropertyScriptableUnmanaged<PlayerInitialCapitalId>.IdTuple[] playerCapitalIds = playersData.GetPropertyData<PlayerInitialCapitalId>();

            for (int playerIndex = 0; playerIndex < playerIdList.Count; ++playerIndex) {
                int playerId = playerIdList[playerIndex];
                PlayerProfileView playerProfile = PlayerProfileView.Create(playerId);
                playerProfile.Name = GetOwnedById(playerNames, playerId).Data.Value;
                playerProfile.CapitalId = GetOwnedById(playerCapitalIds, playerId).Data.Value;
                playerState.AddPlayer(playerProfile);

                ResourceProfileView resourceProfile = ResourceProfileView.Create(playerProfile.Id);
                resourceState.AddResourceProfile(resourceProfile);

                for (int resourceIndex = 0; resourceIndex < resourceTypeIdList.Count; ++resourceIndex) {
                    int resourceTypeId = resourceTypeIdList[resourceIndex];
                    ResourceEntryView resourceEntry = ResourceEntryView.Create(resourceTypeId);
                    resourceEntry.Name = GetOwnedById(resourceTypeNames, resourceTypeId).Data.Value;

                    ResourceCategoryView resourceCategory = ResourceCategoryView.CreateFromEntry(resourceEntry);
                    resourceProfile.AddCategory(resourceCategory);
                }
            }

            IList<int> provinceIdList = provincesData.Ids.IdList;
            PropertyScriptableUnmanaged<Position>.IdTuple[] provincePositions = provincesData.GetPropertyData<Position>();
            PropertyScriptableUnmanaged<ProvinceOwnerId>.IdTuple[] provinceOwnerIds = provincesData.GetPropertyData<ProvinceOwnerId>();
            PropertyScriptableManaged<ProvinceInitialUnits>.IdTuple[] provinceInitialUnits = provincesData.GetPropertyData<ProvinceInitialUnits>();
            PropertyScriptableManaged<ProvinceInitialBuildings>.IdTuple[] provinceInitialBuildings = provincesData.GetPropertyData<ProvinceInitialBuildings>();
            PropertyScriptableUnmanaged<ProvinceName>.IdTuple[] provinceNames = provincesData.GetPropertyData<ProvinceName>();
            PropertyScriptableManaged<StartingResourceProduction>.IdTuple[] startingResourceProduction = provincesData.GetPropertyData<StartingResourceProduction>();
            PropertyScriptableUnmanaged<ProvincePoiTag>.IdTuple[] provincePoiTags = provincesData.GetPropertyData<ProvincePoiTag>();

            // compose needed balancing data
            Dictionary<long, Position> provincePositionsDict = new();
            Dictionary<long, ProvinceInitialUnits> initialUnitsDict = new();
            Dictionary<long, ProvinceInitialBuildings> initialBuildingsDict = new();
            foreach (PropertyScriptableUnmanaged<Position>.IdTuple provincePositionConfig in provincePositions) {
                provincePositionsDict[provincePositionConfig.Id] = provincePositionConfig.Data;
            }
            foreach (PropertyScriptableManaged<ProvinceInitialUnits>.IdTuple initialUnitsConfig in provinceInitialUnits) {
                initialUnitsDict[initialUnitsConfig.Id] = initialUnitsConfig.Data;
            }
            foreach (PropertyScriptableManaged<ProvinceInitialBuildings>.IdTuple initialBuildingsConfig in provinceInitialBuildings) {
                initialBuildingsDict[initialBuildingsConfig.Id] = initialBuildingsConfig.Data;
            }

            for (int i = 0; i < provinceIdList.Count; ++i) {
                int locationId = provinceIdList[i];

                float2 position = provincePositionsDict[locationId].Value;

                bool createInitialArmy = initialUnitsDict.ContainsKey(locationId);
                bool createInitialBuildings = initialBuildingsDict.ContainsKey(locationId);

                long playerId = GetOwnedById(provinceOwnerIds, locationId).Data.Value;

                // create province
                LocationView location = LocationView.CreateProvince(locationId);
                location.Position = position;
                location.Name = GetOwnedById(provinceNames, locationId).Data.Value.ToString();
                location.OwnerId = playerId;

                // Set POI data if this province is marked as point of interest
                bool hasPoi = provincePoiTags.Any(tuple => tuple.Id == locationId);
                if (hasPoi) {
                    location.PointOfInterest = true;
                }

                if (TryGetOwnedById(startingResourceProduction, locationId, out PropertyScriptableManaged<StartingResourceProduction>.IdTuple? startingResourceProductionEntry)) {
                    ResourceUnitsPerDay[] resourceProductions = startingResourceProductionEntry.Data.Value;
                    foreach (ResourceUnitsPerDay production in resourceProductions) {
                        location.SetResourceProduction(production.Id, production.Units);
                    }
                }

                mapState.AddLocation(location);

                if (createInitialArmy) {
                    // create starting army object per province
                    ArmyView army = ArmyView.Create();
                    army.Position = position;
                    army.OwnerId = playerId;

                    // create starting units
                    IdValueTuple[] initialUnits = initialUnitsDict[locationId].Value;
                    int totalUnitsCount = 0;
                    foreach (IdValueTuple initialUnitsEntry in initialUnits) {
                        long unitTypeId = initialUnitsEntry.Id;
                        int count = Mathf.FloorToInt(initialUnitsEntry.Amount);

                        UnitView unit = UnitView.Create(unitTypeId);
                        unit.Size = count;

                        army.AddUnit(unit);
                        totalUnitsCount += count;
                    }

                    army.Size = totalUnitsCount;

                    armyState.AddArmy(army);
                }

                if (createInitialBuildings) {
                    // create initial buildings
                    IdValueTuple[] initialBuildings = initialBuildingsDict[locationId].Value;
                    foreach (Properties.Common.IdValueTuple initialBuildingsEntry in initialBuildings) {
                        long buildingTypeId = initialBuildingsEntry.Id;
                        int count = Mathf.FloorToInt(initialBuildingsEntry.Amount);

                        // interpret the count as true/false
                        if (count > 0) {
                            ModdableUpgradeView upgrade = ModdableUpgradeView.Create(buildingTypeId);
                            location.SetModdableUpgrade(upgrade);
                        }
                    }
                }
            }

            return gameState.Object;
        }

        /// <summary>
        /// IdTuples are not sorted by id, so we need to find the correct one.
        /// </summary>
        /// <param name="tuple">entries with id</param>
        /// <param name="id">id of item to find</param>
        /// <typeparam name="T">Type of data</typeparam>
        /// <returns>Found entry</returns>
        /// <exception cref="Exception">In case of not finding data gives this exception</exception>
        private static PropertyScriptable<T>.IdTuple GetOwnedById<T>(PropertyScriptable<T>.IdTuple[] tuple, int id) {
            foreach (PropertyScriptable<T>.IdTuple data in tuple) {
                if (data.Id == id) {
                    return data;
                }
            }
            throw new Exception($"No data for id. {id}");
        }

        private static bool TryGetOwnedById<T>(
            PropertyScriptable<T>.IdTuple[] tuple,
            int id,
            [NotNullWhen(true)] out PropertyScriptable<T>.IdTuple? result
        ) {
            foreach (PropertyScriptable<T>.IdTuple data in tuple) {
                if (data.Id == id) {
                    result = data;
                    return true;
                }
            }

            result = null;
            return false;
        }
    }
}
