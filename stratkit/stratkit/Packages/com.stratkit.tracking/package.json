{"name": "com.stratkit.tracking", "version": "1.5.0", "displayName": "Stratkit Tracking Core", "unity": "2022.3", "unityRelease": "0f1", "description": "Core package to include tracking components required to perform tracking on different games and companies.", "publishConfig": {"registry": "https://npm.pkg.github.com/@bytro"}, "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.worldreference": "2.0.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.ext.nunit": "1.0.6"}, "author": {"name": "Bytro", "email": "<EMAIL>", "url": "https://www.bytro.com"}, "samples": [{"displayName": "Tracking Demo", "description": "Demo of how to create a tracking event and how a tracking handler would read it", "path": "Samples~/"}]}