﻿using Stratkit.Tracking.Core.Sender;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Stratkit.Tracking.Core.Tests.Editor {
    /// <summary>
    /// Mocked implementation of <see cref="ATrackingSender{T}"/>
    /// </summary>
    public sealed class TestTrackingSender : ATrackingSender<int> {
        public bool ShouldSucceedNextSend;
        public List<int> SentEventsInOrder = new();
        public int SendEventAsyncHitCount;
        public int LastAwaitDueToFailureMs;
        public int LastMessageDropCount;
        public int AwaitSendMs;
        private int _awaitedTime;

        private TaskCompletionSource<bool> _awaiterByTime = new();
        private TaskCompletionSource<bool> _awaiterOnError = new();

        public TestTrackingSenderPersistence Persistence {
            get { return (TestTrackingSenderPersistence)_persistence; }
            set { _persistence = value; }
        }

        public TestActionInEditor ActionInEditor {
            get {
                return _actionInEditor == ExpectedAction.SendToServer
                    ? TestActionInEditor.SendToServer
                    : TestActionInEditor.DoNotSendToServer;
            }
            set {
                _actionInEditor = value == TestActionInEditor.SendToServer
                    ? ExpectedAction.SendToServer
                    : ExpectedAction.DoNothing;
            }
        }

        public int MaxQueueCapacity {
            get { return _maxQueueCapacity; }
            set { _maxQueueCapacity = value; }
        }

        public int MaxSendTryCount {
            get { return _maxSendTryCount; }
            set { _maxSendTryCount = value; }
        }

        public int RetryDelayMs {
            get { return _retryDelayMs; }
            set { _retryDelayMs = value; }
        }

        public float RetryDelayMultiplier {
            get { return _retryDelayMultiplier; }
            set { _retryDelayMultiplier = value; }
        }

        public bool ShouldDropFailedMessages {
            get { return _shouldDropFailedMessages; }
            set { _shouldDropFailedMessages = value; }
        }

        public bool ShouldPersistQueue {
            get { return _shouldPersistQueue; }
            set { _shouldPersistQueue = value; }
        }

        public void ResolveAwaiterOnError() {
            TaskCompletionSource<bool> awaiterOnError = _awaiterOnError;
            _awaiterOnError = new TaskCompletionSource<bool>();
            awaiterOnError.SetResult(true);
        }

        public void AdvanceTimeMs(int msToAdvance) {
            _awaitedTime += msToAdvance;
            while (_awaitedTime >= AwaitSendMs) {
                _awaitedTime -= AwaitSendMs;
                _awaiterByTime.TrySetResult(true);
            }
        }

        protected override async Task<bool> SendEventAsync(int serializedEvent) {
            SendEventAsyncHitCount++;
            if (AwaitSendMs > 0) {
                await _awaiterByTime.Task;
                _awaiterByTime = new TaskCompletionSource<bool>();
            }

            if (ShouldSucceedNextSend) {
                SentEventsInOrder.Add(serializedEvent);
            }

            return ShouldSucceedNextSend;
        }

        protected override List<string> SerializedEventsToStrings(Queue<int> serializedEventQueue) {
            List<string> list = new();
            foreach (int i in serializedEventQueue) {
                list.Add(i.ToString());
            }

            return list;
        }

        protected override List<int> StringsToSerializedEvents(IList<string> persistedEventQueue) {
            List<int> list = new();
            foreach (string s in persistedEventQueue) {
                list.Add(int.Parse(s));
            }

            return list;
        }

        protected override Task AwaitDueToFailure(int awaitMs) {
            LastAwaitDueToFailureMs = awaitMs;
            return _awaiterOnError.Task;
        }

        protected override bool HasInternet() {
            return true;
        }

        protected override void OnMessagesDropped(int messageDropCount, string reason) {
            LastMessageDropCount = messageDropCount;
        }

        public enum TestActionInEditor {
            DoNotSendToServer = 0,
            SendToServer = 1,
        }
    }
}
