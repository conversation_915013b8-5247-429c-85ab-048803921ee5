﻿using NUnit.Framework;
using System.Collections;
using UnityEditor;
using UnityEngine;
using UnityEngine.TestTools;

namespace Stratkit.Tracking.Core.Tests.Editor {
    public sealed class ATrackingSenderTests {
        private const string TestPath = "Packages/com.stratkit.tracking/Tests/Editor/";
        private const string TestTrackingSenderPath = nameof(TestTrackingSender) + ".asset";
        private TestTrackingSender? _testTrackingSenderEditor;
        private TestTrackingSender _senderInstance = null!; // For convenience we ensure C# that this instance will not be null, because we're ensuring it on Setup

        [SetUp]
        public void Setup() {
            if (_testTrackingSenderEditor == null) {
                _testTrackingSenderEditor = AssetDatabase.LoadAssetAtPath<TestTrackingSender>($"{TestPath}{TestTrackingSenderPath}");
            }
            Assert.NotNull(_testTrackingSenderEditor, $"{nameof(TestTrackingSender)} was not found, did you change the path of this package?");
            _senderInstance = Object.Instantiate(_testTrackingSenderEditor);
            Assert.NotNull(_senderInstance, $"{nameof(TestTrackingSender)} was not instantiated");
            _senderInstance.Persistence = Object.Instantiate(_testTrackingSenderEditor.Persistence); // Copy the persistence as well
        }

        [TearDown]
        public void TearDown() {
            if (_senderInstance != null) {
                Object.DestroyImmediate(_senderInstance);
                _senderInstance = null!;
            }
        }

        [UnityTest]
        public IEnumerator LogInEditorModeShouldNotSendMessages() {
            _senderInstance.ActionInEditor = TestTrackingSender.TestActionInEditor.DoNotSendToServer;
            _senderInstance.ShouldSucceedNextSend = true;
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount, "Hit count was not initialized as zero");
            _senderInstance.SendTrackingEventToServer(23);
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount, nameof(_senderInstance.SendTrackingEventToServer) + " did attempt to send to server even when action was set to not do that");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendInEditorModeShouldSendMessages() {
            _senderInstance.ActionInEditor = TestTrackingSender.TestActionInEditor.SendToServer;
            _senderInstance.ShouldSucceedNextSend = true;
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount, "Hit count was not initialized as zero");
            const int SentMessage = 23;
            _senderInstance.SendTrackingEventToServer(SentMessage);
            Assert.AreEqual(1, _senderInstance.SendEventAsyncHitCount, nameof(_senderInstance.SendTrackingEventToServer) + " did not attempt to send to server even when action was set to send");
            Assert.AreEqual(1, _senderInstance.SentEventsInOrder.Count, "Expected exactly one message to have been sent");
            Assert.AreEqual(SentMessage, _senderInstance.SentEventsInOrder[0], "Sent message was not the expected one");
            yield break;
        }

        [UnityTest]
        public IEnumerator PersistenceShouldLoadOnlyOnFirstEventTracked() {
            TestTrackingSenderPersistence persistence = _senderInstance.Persistence;
            _senderInstance.ShouldSucceedNextSend = true;
            Assert.AreEqual(0, persistence.LoadEventsHitCount);
            yield return null;
            Assert.AreEqual(0, persistence.LoadEventsHitCount);
            _senderInstance.SendTrackingEventToServer(1);
            Assert.AreEqual(1, persistence.LoadEventsHitCount, "Loading should have happened right before sending the events");
            _senderInstance.SendTrackingEventToServer(2);
            Assert.AreEqual(1, persistence.LoadEventsHitCount, "Loading should have not changed after the first count");
        }

        [UnityTest]
        public IEnumerator PersistenceShouldSendBeforeNewEvents() {
            TestTrackingSenderPersistence persistence = _senderInstance.Persistence;
            persistence.PersistedEvents.Add("1");
            persistence.PersistedEvents.Add("2");
            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.SendTrackingEventToServer(3);
            Assert.AreEqual(3, _senderInstance.SentEventsInOrder.Count, "Expected exactly 3 messages to have been sent");
            Assert.AreEqual(1, _senderInstance.SentEventsInOrder[0], "Sent message was not the first persisted one");
            Assert.AreEqual(2, _senderInstance.SentEventsInOrder[1], "Sent message was not the second persisted one");
            Assert.AreEqual(3, _senderInstance.SentEventsInOrder[2], "Sent message was not the new one");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldSucceedWhenPersistenceIsInactive() {
            _senderInstance.ShouldPersistQueue = false;
            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.SendTrackingEventToServer(42);
            Assert.AreEqual(1, _senderInstance.SentEventsInOrder.Count, "Expected exactly one message to have been sent");
            Assert.AreEqual(42, _senderInstance.SentEventsInOrder[0], "Sent message was not the expected one");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldSucceedWhenPersistenceReturnsFalse() {
            TestTrackingSenderPersistence persistence = _senderInstance.Persistence;
            persistence.ShouldPersistNextEvents = false;
            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.SendTrackingEventToServer(42);
            Assert.AreEqual(1, _senderInstance.SentEventsInOrder.Count, "Expected exactly one message to have been sent");
            Assert.AreEqual(42, _senderInstance.SentEventsInOrder[0], "Sent message was not the expected one");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldDropMessagesWhenFailConfiguredTimes() {
            _senderInstance.ShouldDropFailedMessages = true;
            _senderInstance.MaxSendTryCount = 3;
            _senderInstance.ShouldSucceedNextSend = false;
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount);
            _senderInstance.SendTrackingEventToServer(1);
            Assert.AreEqual(1, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(0, _senderInstance.LastMessageDropCount, "No message should've been dropped");

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(2, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(0, _senderInstance.LastMessageDropCount, "No message should've been dropped");

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(3, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(1, _senderInstance.LastMessageDropCount, "Message should've been dropped");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldAwaitMessagesAfterDroppingWhenFailConfiguredTimes() {
            _senderInstance.ShouldDropFailedMessages = true;
            _senderInstance.MaxSendTryCount = 2;
            _senderInstance.ShouldSucceedNextSend = false;
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount);
            _senderInstance.SendTrackingEventToServer(1);
            _senderInstance.SendTrackingEventToServer(2);
            Assert.AreEqual(1, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(0, _senderInstance.LastMessageDropCount, "No message should've been dropped");

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(2, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(1, _senderInstance.LastMessageDropCount, "First failed message should've been dropped");
            _senderInstance.LastMessageDropCount = 0;

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(3, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(0, _senderInstance.LastMessageDropCount, "Second failed message should've not been dropped");

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(4, _senderInstance.SendEventAsyncHitCount);
            Assert.AreEqual(1, _senderInstance.LastMessageDropCount, "Second failed message should've been dropped");
            yield break;
        }

        [UnityTest]
        public IEnumerator AwaitMsShouldIncreaseByRetryCount() {
            _senderInstance.ShouldDropFailedMessages = false;
            _senderInstance.MaxSendTryCount = 2;
            _senderInstance.ShouldSucceedNextSend = false;
            Assert.AreEqual(0, _senderInstance.SendEventAsyncHitCount);
            _senderInstance.SendTrackingEventToServer(1);
            Assert.AreEqual(_senderInstance.RetryDelayMs * _senderInstance.RetryDelayMultiplier * 1, _senderInstance.LastAwaitDueToFailureMs);

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(_senderInstance.RetryDelayMs * _senderInstance.RetryDelayMultiplier * 2, _senderInstance.LastAwaitDueToFailureMs);

            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(_senderInstance.RetryDelayMs * _senderInstance.RetryDelayMultiplier * 2, _senderInstance.LastAwaitDueToFailureMs, $"Maximum try count of {_senderInstance.MaxSendTryCount} was reached, the time should not increase after that");
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldSendQueuedMessagesInOrder() {
            _senderInstance.MaxQueueCapacity = 10;
            _senderInstance.ShouldSucceedNextSend = false;
            const int MessageCount = 5;
            for (int i = 1; i <= MessageCount; i++) {
                _senderInstance.SendTrackingEventToServer(i);
            }

            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(MessageCount, _senderInstance.SentEventsInOrder.Count);
            for (int index = 0; index < _senderInstance.SentEventsInOrder.Count; index++) {
                int sentEvent = _senderInstance.SentEventsInOrder[index];
                Assert.AreEqual(index + 1, sentEvent, $"Sent message at index {index} was not the expected one");
            }
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldQueueMessagesWhenAwaiting() {
            _senderInstance.MaxQueueCapacity = 10;
            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.AwaitSendMs = 100;
            _senderInstance.ShouldPersistQueue = false;
            const int MessageCount = 5;
            for (int i = 1; i <= MessageCount; i++) {
                _senderInstance.SendTrackingEventToServer(i);
            }

            Assert.AreEqual(0, _senderInstance.SentEventsInOrder.Count, "Nothing should've been sent yet");
            for (int index = 0; index < MessageCount; index++) {
                _senderInstance.AdvanceTimeMs(_senderInstance.AwaitSendMs);
                Assert.AreEqual(index + 1, _senderInstance.SentEventsInOrder.Count);
                int sentEvent = _senderInstance.SentEventsInOrder[index];
                Assert.AreEqual(index + 1, sentEvent, $"Sent message at index {index} was not the expected one");
            }
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldDropMessagesWhenQueueGetsFull() {
            const int MaxQueueCapacity = 10;
            const int ExcessMessages = 5;
            _senderInstance.MaxQueueCapacity = MaxQueueCapacity;
            _senderInstance.ShouldSucceedNextSend = false;
            for (int i = 1; i <= _senderInstance.MaxQueueCapacity + ExcessMessages; i++) {
                _senderInstance.SendTrackingEventToServer(i);
            }

            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.ResolveAwaiterOnError();
            Assert.AreEqual(ExcessMessages, _senderInstance.LastMessageDropCount, $"{ExcessMessages} messages should've been dropped from the queue before sending");
            for (int index = 0; index < _senderInstance.SentEventsInOrder.Count; index++) {
                int sentEvent = _senderInstance.SentEventsInOrder[index];
                Assert.AreEqual(index + ExcessMessages + 1, sentEvent, $"Sent message at index {index} was not the expected one");
            }
            yield break;
        }

        [UnityTest]
        public IEnumerator SendTrackingEventToServerShouldDropMessagesWhenLoadedFromPersistence() {
            TestTrackingSenderPersistence persistence = _senderInstance.Persistence;
            const int MaxQueueCapacity = 10;
            const int ExcessMessages = 5;
            _senderInstance.MaxQueueCapacity = MaxQueueCapacity;
            for (int i = 1; i <= MaxQueueCapacity + ExcessMessages; i++) {
                persistence.PersistedEvents.Add(i.ToString());
            }
            _senderInstance.ShouldSucceedNextSend = true;
            _senderInstance.SendTrackingEventToServer(MaxQueueCapacity + ExcessMessages + 1);
            Assert.AreEqual(ExcessMessages + 1, _senderInstance.LastMessageDropCount, $"{ExcessMessages + 1} messages should've been dropped from the queue before sending");
            for (int index = 0; index < _senderInstance.SentEventsInOrder.Count; index++) {
                int sentEvent = _senderInstance.SentEventsInOrder[index];
                Assert.AreEqual(index + ExcessMessages + 2, sentEvent, $"Sent message at index {index} was not the expected one");
            }
            yield break;
        }
    }
}
