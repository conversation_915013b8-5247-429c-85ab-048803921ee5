# com.stratkit.tracking

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 0.1.0
 - Initial version

## Version 1.0.0
 - Use `EntityAPI` to cache entity queries.

## Version 1.0.1
 - MapPerformanceTrackerSystem using WorldReferenceUtils.GetPersistentWorld

## Version 1.1.0
  - MapPerformanceTrackerSystem NOT using WorldReferenceUtils.GetPersistentWorld, in favor of getting the MatchCountStatComponent directly from [package.json](package.json)the map world.

## Version 1.1.1
 - Disable automatic reference to assembly.

## Version 1.2.0
 - Move performance tracking into separate module.

## Version 1.3.0
 - FunnelTrackingSteps now as extension method for a nicer syntax.

## Version 1.4.0
 - Removed `Entities.ForEach` usages.
 - Converted `TrackingConsumerSystem` to `ISystem`.

## Version 1.4.1
 - Fixes `MinuteTrackingSystem` structural changes errors.

## Version 1.4.2
 - Minor code clean up.

## Version 1.5.0
 - Add more tests for tracking sender to ensure tracking events do get awaited and sent in order.
