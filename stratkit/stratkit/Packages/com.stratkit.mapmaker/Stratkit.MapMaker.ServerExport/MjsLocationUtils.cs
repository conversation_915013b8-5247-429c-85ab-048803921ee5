using Stillfront.Logging;
using Stratkit.Data.Provinces;
using Stratkit.Data.Resources;
using Stratkit.MapMaker.ServerExport.Data;
using Stratkit.Properties.Common;
using Stratkit.Properties.Loader;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Unity.Mathematics;

using Province = Stratkit.MapMaker.Editor.Province;
using Seapoint = Stratkit.MapMaker.Editor.Seapoint;

namespace Stratkit.MapMaker.ServerExport {
    /// <summary>
    /// MJS stands for Map Json Schema
    /// </summary>
    public static class MjsLocationUtils {
        public static MjsLocation[] GetMjsLocations(
            PropertiesCollectionScriptable provincesData,
            ContentItemNameScriptable terrainNames,
            in Province[] provinces,
            in float2[] provinceBorderPositions,
            in Seapoint[] seapoints,
            in float2 mapSize,
            int noProductionTypeId
        ) {
            IList<int> provinceIdList = provincesData.Ids.IdList;
            PropertyScriptableManaged<ProvinceInitialUnits>.IdTuple[] provinceInitialUnits = provincesData.GetPropertyDataManaged<ProvinceInitialUnits>();
            PropertyScriptableManaged<ProvinceInitialBuildings>.IdTuple[] provinceInitialBuildings = provincesData.GetPropertyDataManaged<ProvinceInitialBuildings>();
            PropertyScriptableUnmanaged<ProvinceName>.IdTuple[] provinceNames = provincesData.GetPropertyData<ProvinceName>();
            PropertyScriptableUnmanaged<ProvinceOwnerId>.IdTuple[] provinceOwnerIds = provincesData.GetPropertyData<ProvinceOwnerId>();
            PropertyScriptableUnmanaged<ProvincePopulation>.IdTuple[] provincePopulations = provincesData.GetPropertyData<ProvincePopulation>();
            PropertyScriptableUnmanaged<ProvinceTerrain>.IdTuple[] provinceTerrains = provincesData.GetPropertyData<ProvinceTerrain>();
            PropertyScriptableManaged<StartingResourceProduction>.IdTuple[] provinceResourceProduction = provincesData.GetPropertyDataManaged<StartingResourceProduction>();
            PropertyScriptableUnmanaged<ProvinceVictoryPoints>.IdTuple[] victoryPoints = provincesData.GetPropertyData<ProvinceVictoryPoints>();
            PropertyScriptableManaged<ContentItemName>.IdTuple[] terrainNamesValues = terrainNames.Values;
            PropertyScriptableUnmanaged<ProvinceInitialUpgradeGrounds>.IdTuple[] initialUpgradeGrounds = provincesData.GetPropertyData<ProvinceInitialUpgradeGrounds>();
            PropertyScriptableUnmanaged<ProvinceMaxUpgradeGrounds>.IdTuple[] maxUpgradeGrounds = provincesData.GetPropertyData<ProvinceMaxUpgradeGrounds>();
            PropertyScriptableUnmanaged<ProvincePoiTag>.IdTuple[] provincePoiTags = provincesData.GetPropertyData<ProvincePoiTag>();

            Dictionary<int, Province> provinceLookup = provinces.ToDictionary(x => x.Id);

            List<MjsLocation> mjsLocations = new(provinceIdList.Count);

            for (int i = 0; i < provinceIdList.Count; ++i) {
                int id = provinceIdList[i];
                long ownerId = provinceOwnerIds.Single(x => x.Id == id).Data.Value;

                // this gets the server compatible serialization name for the terrain type
                int terrainId = provinceTerrains.Single(x => x.Id == id).Data.Value;
                PropertyScriptableManaged<ContentItemName>.IdTuple? terrainName = terrainNamesValues.SingleOrDefault(x => x.Id == terrainId);
                string terrainTypeName = "UNKNOWN";
                if (terrainName != null) {
                    terrainTypeName = terrainName.Data.Value;
                } else {
                    Log.Error($"Cant find terrain id '{terrainId}' on {terrainNames.name}", terrainNames);
                }

                if (!provinceLookup.TryGetValue(id, out Province province)) {
                    continue;
                }

                float2 capitalPosition = MjsUtils.ConvertCoordinate(province.Position, mapSize);

                int productionTypeId = noProductionTypeId;
                PropertyScriptableManaged<StartingResourceProduction>.IdTuple? productionType = provinceResourceProduction.SingleOrDefault(x => x.Id == id);
                if (productionType is { Data: { Value: { Length: > 0 } } }) {
                    productionTypeId = productionType.Data.Value[0].Id;
                }

                long[] neighbourIds = province.NeighbourIds.Select(x => (long)x).OrderBy(x => x).ToArray();
                mjsLocations.Add(new MjsLocation {
                    Id = id,
                    LocationType = MjsLocationType.Province, // We don't support NeutralProvince yet
                    Borders = GetBorderPolygons(province, provinceBorderPositions, mapSize),
                    Capital = new double[] { capitalPosition.x, capitalPosition.y },
                    ConfiguredProductions = GetConfiguredProductions(provinceResourceProduction, id),
                    CoreIds = new[] { ownerId },
                    Hostility = null, // feature for some game, can stay null
                    InitialProvinceUnits = GetInitialUnits(provinceInitialUnits, id),
                    InitialProvinceUpgrades = GetInitialProvinceUpgrades(provinceInitialBuildings, id),
                    Name = provinceNames.First(x => x.Id == id).Data.Value.ToString(),
                    NeighbourIds = neighbourIds.Length <= 0 ? null : neighbourIds,
                    OwnerId = ownerId,
                    PointOfInterest = GetPointOfInterest(provincePoiTags, id),
                    Population = provincePopulations.Single(x => x.Id == id).Data.Value,
                    InitialUpgradeGrounds = GetInitialUpgradeGrounds(initialUpgradeGrounds, id),
                    MaxUpgradeGrounds = GetMaxUpgradeGrounds(maxUpgradeGrounds, id),
                    ProductionType = productionTypeId,
                    Regions = null, // feature we may not need yet
                    TerrainMarkerPositions = null, // another feature we may not need yet
                    TerrainType = terrainTypeName,
                    VictoryPoints = victoryPoints.First(x => x.Id == id).Data.Value,
                });
            }

            foreach (Seapoint seapoint in seapoints) {
                float2 position = MjsUtils.ConvertCoordinate(seapoint.Position, mapSize);

                mjsLocations.Add(new MjsLocation {
                    Id = seapoint.Id,
                    LocationType = MjsLocationType.SeaPoint,
                    Capital = new double[] { position.x, position.y },
                    Name = string.Empty, // TODO: sea point names
                    NeighbourIds = seapoint.NeighbourIds.Select(x => (long)x).OrderBy(x => x).ToArray(),
                });
            }

            // Sort locations by id
            mjsLocations.Sort((a, b) => (int)(a.Id - b.Id)!);

            return mjsLocations.ToArray();
        }

        private static long? GetInitialUpgradeGrounds(PropertyScriptableUnmanaged<ProvinceInitialUpgradeGrounds>.IdTuple[] initialUpgradeGrounds, int id) {
            if (initialUpgradeGrounds.Length <= 0) {
                return null;
            }

            long? initialUpgrade = initialUpgradeGrounds.First(x => x.Id == id).Data.Value;
            return initialUpgrade == 0 ? null : initialUpgrade;
        }

        private static long? GetMaxUpgradeGrounds(PropertyScriptableUnmanaged<ProvinceMaxUpgradeGrounds>.IdTuple[] maxUpgradeGrounds, int id) {
            if (maxUpgradeGrounds.Length <= 0) {
                return null;
            }

            long? maxUpgrade = maxUpgradeGrounds.First(x => x.Id == id).Data.Value;
            return maxUpgrade == 0 ? null : maxUpgrade;
        }

        private static double[][] GetBorderPolygons(in Province province, float2[] borderPositions, float2 mapSize) {
            double[][] borders = new double[province.Borders.Length][];

            for (int borderIndex = 0; borderIndex < province.Borders.Length; ++borderIndex) {
                ReadOnlySpan<float2> inBorder = province.BorderPositions(borderPositions, borderIndex);
                double[] outBorder = new double[inBorder.Length * 2];

                int outIndex = 0;
                for (int pointIndex = 0; pointIndex < inBorder.Length; ++pointIndex) {
                    float2 point = inBorder[pointIndex];
                    float2 converted = MjsUtils.ConvertCoordinate(point, mapSize);
                    outBorder[outIndex++] = converted.x;
                    outBorder[outIndex++] = converted.y;
                }

                if (!IsClockwiseInOriginTopLeft(outBorder)) {
                    outBorder = ReversePoints(outBorder);
                }

                borders[borderIndex] = outBorder;
            }

            return borders;
        }

        /// <summary>
        /// Checks if border is clockwise for server coordinate system (origin top-left)
        /// </summary>
        /// <param name="outBorder">List od points</param>
        /// <returns>true if clockwise</returns>
        /// <exception cref="ArgumentException">throws then array doesn't have an even number of elements</exception>
        private static bool IsClockwiseInOriginTopLeft(double[] outBorder) {
            if (outBorder.Length % 2 != 0) {
                throw new ArgumentException("The array must have an even number of elements (x, y pairs).");
            }

            double area = 0.0;

            for (int i = 0; i < outBorder.Length; i += 2) {
                double currentX = outBorder[i];
                double currentY = outBorder[i + 1];

                double nextX = outBorder[(i + 2) % outBorder.Length];
                double nextY = outBorder[(i + 3) % outBorder.Length];

                area += (nextX - currentX) * (currentY + nextY);
            }

            return area < 0;
        }

        private static double[] ReversePoints(double[] points) {
            if (points.Length % 2 != 0) {
                throw new ArgumentException("The array must have an even number of elements (x, y pairs).");
            }

            double[] reversed = new double[points.Length];

            for (int i = 0; i < points.Length; i += 2) {
                reversed[points.Length - i - 2] = points[i];
                reversed[points.Length - i - 1] = points[i + 1];
            }

            return reversed;
        }

        private static Dictionary<string, double> GetConfiguredProductions(
            PropertyScriptableManaged<StartingResourceProduction>.IdTuple[] provinceResourceProductions,
            int locationId
        ) {
            PropertyScriptableManaged<StartingResourceProduction>.IdTuple? resourceProductions = provinceResourceProductions
                .SingleOrDefault(tuple => tuple.Id == locationId);
            if (resourceProductions == null) {
                return new Dictionary<string, double>();
            }
            return resourceProductions.Data.Value
                .ToDictionary(tuple => tuple.Id.ToString(CultureInfo.InvariantCulture), tuple => (double)tuple.Units);
        }

        private static Dictionary<string, long>? GetInitialUnits(
            PropertyScriptableManaged<ProvinceInitialUnits>.IdTuple[] provinceInitialUnits,
            int locationId
        ) {
            PropertyScriptableManaged<ProvinceInitialUnits>.IdTuple? initialUnits = provinceInitialUnits
                .SingleOrDefault(tuple => tuple.Id == locationId);
            if (initialUnits == null || initialUnits.Data.Value.Length <= 0) {
                return null;
            }

            Dictionary<string, long> dictionary = initialUnits.Data.Value
                .Where(tuple => tuple.Amount > 0f)
                .ToDictionary(tuple => tuple.Id.ToString(CultureInfo.InvariantCulture), tuple => (long)tuple.Amount);
            return dictionary.Count <= 0 ? null : dictionary;
        }

        private static Dictionary<string, long>? GetInitialProvinceUpgrades(
            PropertyScriptableManaged<ProvinceInitialBuildings>.IdTuple[] initialBuildingsLevels,
            int locationId
        ) {
            PropertyScriptableManaged<ProvinceInitialBuildings>.IdTuple? initialBuildings = initialBuildingsLevels
                .SingleOrDefault(tuple => tuple.Id == locationId);
            if (initialBuildings == null || initialBuildings.Data.Value.Length <= 0) {
                return null;
            }

            Dictionary<string, long> dictionary = initialBuildings.Data.Value
                .Where(tuple => tuple.Amount > 0f)
                .ToDictionary(tuple => tuple.Id.ToString(CultureInfo.InvariantCulture), tuple => (long)tuple.Amount);
            return dictionary.Count <= 0 ? null : dictionary;
        }

        private static bool? GetPointOfInterest(
            PropertyScriptableUnmanaged<ProvincePoiTag>.IdTuple[] provincePoiTags,
            int locationId
        ) {
            // Check if this province has a POI tag
            bool hasPoi = provincePoiTags.Any(tuple => tuple.Id == locationId);
            return hasPoi ? true : null; // Return true if POI exists, null otherwise (to minimize JSON size)
        }
    }
}
