﻿using Stillfront.Logging;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace Stratkit.AddressablesUtils {
    /// <summary>
    /// Representation of an asset that can be disposed.
    /// Every instance of DisposableAsset is registered in <see cref="AddressablesCacheManager"/> and causes
    /// asset reference count increased by 1, even if during loading. When the asset is disposed, the reference count
    /// is decreased, if goes to 0 the is released.
    /// </summary>
    public abstract class DisposableAsset : IDisposable {
        public AssetReference AssetReference {
            get {
                if (HasBeenDisposed) {
                    throw new ObjectDisposedException(GetType().Name);
                }

                return _assetReference;
            }
        }

        public bool HasBeenDisposed { get; private set; }

        protected readonly Func<AsyncOperationHandle> HandleProvider;
        private readonly AssetReference _assetReference;
        private readonly IAddressablesCacheManager _cacheManager;
        private readonly Func<AssetReference, bool> _onDispose;

        protected DisposableAsset(
            IAddressablesCacheManager cacheManager,
            AssetReference assetReference,
            Func<AsyncOperationHandle> handleProvider,
            Func<AssetReference, bool> onDispose
        ) {
            _cacheManager = cacheManager;
            _assetReference = assetReference;
            HandleProvider = handleProvider;
            _onDispose = onDispose;
        }

#if UNITY_EDITOR
        public virtual UnityEngine.Object GetEditorAsset() => _assetReference.editorAsset;
#endif

        public void Dispose() {
            if (HasBeenDisposed) {
                return;
            }

            HasBeenDisposed = true;

            // if cache manager has been destroyed then do nothing
            if (!_cacheManager.IsValid) {
                return;
            }

            _onDispose(_assetReference);
        }

        public bool IsAssetLoaded(out AddressablesCacheStatus status) {
            if (HasBeenDisposed) {
                throw new ObjectDisposedException(GetType().Name);
            }

            AsyncOperationHandle handle = HandleProvider();
            switch (handle.Status) {
                case AsyncOperationStatus.Succeeded:
                    status = AddressablesCacheStatus.Loaded;
                    return true;
                case AsyncOperationStatus.Failed:
                    status = AddressablesCacheStatus.Failed;
                    break;
                default:
                    status = AddressablesCacheStatus.Loading;
                    break;
            }

            return false;
        }
    }

    public sealed class DisposableAsset<TObject> : DisposableAsset where TObject : UnityEngine.Object {
        public DisposableAsset(
            IAddressablesCacheManager cacheManager,
            AssetReference assetReference,
            Func<AsyncOperationHandle> handleProvider,
            Func<AssetReference, bool> onDispose
        ) : base(cacheManager, assetReference, handleProvider, onDispose) { }

#if UNITY_EDITOR
        public new TObject GetEditorAsset() => (TObject)base.GetEditorAsset();
#endif

        // it may be invoked many times by the owner, doesn't cause reference incrementing
        public async Task<TObject> GetAssetAsync() {
            AsyncOperationHandle handle = await WaitForAssetAsync();
            return handle.Convert<TObject>().Result;
        }

        public async Task<AsyncOperationHandle> WaitForAssetAsync() {
            if (HasBeenDisposed) {
                throw new ObjectDisposedException(nameof(DisposableAsset<TObject>));
            }

            AsyncOperationHandle handle = HandleProvider();

            try {
                await handle.Task;
                bool isValid = handle.IsValid();
                bool isFailed = isValid && handle.Status == AsyncOperationStatus.Failed;
                if (!isValid || isFailed) {
                    if (HasBeenDisposed) {
                        throw new ObjectDisposedException(
                            $"Failed to load asset (HasBeenDisposed) isValid: {isValid}, isFailed: {isFailed}"
                        );
                    }

                    throw new FailedToLoadAddressableException(
                        AssetReference,
                        $"isValid: {isValid}, isFailed: {isFailed}"
                    );
                }
            } catch (Exception) {
                try {
                    // Release the asset if it fails
                    Dispose();
                } catch (Exception e2) {
                    Log.Exception(
                        e2, "Failed to release asset on exception"
                    );
                }

                throw;
            }

            return handle;
        }

        public bool TryGetLoadedAsset([NotNullWhen(true)] out TObject? asset) {
            if (HasBeenDisposed) {
                throw new ObjectDisposedException(nameof(DisposableAsset<TObject>));
            }

            AsyncOperationHandle handle = HandleProvider();
            if (handle.Status == AsyncOperationStatus.Succeeded) {
                asset = handle.Convert<TObject>().Result;
                return true;
            }

            asset = null;
            return false;
        }
    }
}
