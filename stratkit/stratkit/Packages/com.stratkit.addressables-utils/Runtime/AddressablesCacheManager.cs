using Sirenix.OdinInspector;
using Stillfront.Logging;
using System;
using System.Collections.Generic;
using Unity.Entities;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using Object = UnityEngine.Object;

namespace Stratkit.AddressablesUtils {
    /// <summary>
    /// Manager that helps to centralise the loading of addressable assets.
    /// </summary>
    public sealed class AddressablesCacheManager : MonoBehaviour, IComponentData, IAddressablesCacheManager {
        /// <summary>
        /// Check if the Object was destroyed.
        /// </summary>
        public bool IsValid => this != null;

        /// <summary>
        /// The list of references by GUID so we can release
        /// </summary>
        [ShowInInspector, DisableInEditorMode]
        private Dictionary<AssetReference, AsyncOperationHandle> _cachedHandles = new();

        /// <summary>
        /// The total reference count for each asset
        /// </summary>
        [ShowInInspector, DisableInEditorMode]
        private Dictionary<AssetReference, int> _assetReferenceCount = new();

        private readonly AssetsDisposer _internalDisposer = new();

        /// <summary>
        /// Returns a disposable holder for an addressable asset managed by the provided IAssetsDisposer (the owner of the assets).
        /// </summary>
        /// <remarks>
        /// If the disposer already has a registered reference to the asset, the method simply returns that reference without incrementing the reference count.
        /// This allows you to attach a disposer to, for example, a dialog, and safely request assets using `GetDisposableAsset` each time the dialog is opened and closed.
        /// This is done without having to worry about reference counting, regardless of whether the dialog is destroyed each time or not.
        /// If the asset isn't already loaded or its loading failed, it triggers a new load operation and updates the cache and reference count accordingly.
        /// </remarks>
        /// <typeparam name="T">The type of the asset to be loaded.</typeparam>
        /// <param name="assetToLoad">A reference to the asset to be loaded.</param>
        /// <param name="disposer">The IAssetsDisposer which manages the lifecycle of the asset.</param>
        /// <returns>A DisposableAsset holder that encapsulates the requested asset.</returns>
        public DisposableAsset<T> GetDisposableAsset<T>(AssetReference assetToLoad, IAssetsDisposer disposer) where T : Object {
            // If the owner (disposer) already has a reference to the asset, return it and don't increment reference count
            if (disposer.TryGetDisposableAsset(assetToLoad, out DisposableAsset<T>? disposableAsset)) {
                return disposableAsset;
            }

            // Trigger asset loading in case when it is not registered or failed
            if (!_cachedHandles.TryGetValue(assetToLoad, out AsyncOperationHandle cachedHandle)
                || !cachedHandle.IsValid()
                || cachedHandle.Status == AsyncOperationStatus.Failed) {
                AsyncOperationHandle currentOperationHandle = assetToLoad.OperationHandle;
                if (currentOperationHandle.IsValid()) {
                    // The asset is already loaded, but was cached in another cache manager (e.g. when the asset is loaded by the persistent world but used in the map world). We return a valid reference to the asset but we do not track it here, assuming that the other side will last longer.
                    // https://bytrolabs.atlassian.net/browse/WHS-6080 A proper solution must involve some sort of global labelling on each asset, so that cache managers do keep track and remove their own references but only unload an asset when the last label on the asset (which would correspond to this cache manager) was removed. See: https://bytrolabs.atlassian.net/browse/WHS-6158
                    Log.Warning($"Asset {assetToLoad} is already loaded. Returning a reference but not caching on this cache manager. This may lead to errors when asset is disposed by the original cache manager.", this);
                    return new DisposableAsset<T>(
                        new DummyAddressablesCacheManager(),
                        assetToLoad,
                        () => currentOperationHandle,
                        _ => false
                    );
                }

                cachedHandle = assetToLoad.LoadAssetAsync<T>();
                _cachedHandles[assetToLoad] = cachedHandle;
            }

            // Increase reference counter because we create new holder here
            _assetReferenceCount.TryGetValue(assetToLoad, out int count);
            _assetReferenceCount[assetToLoad] = count + 1;

            // Create disposable asset
            disposableAsset = new DisposableAsset<T>(
                this,
                assetToLoad,
                () => _cachedHandles[assetToLoad],
                ReleaseDisposableAsset
            );

            // Register in the owner (disposer)
            disposer.Register(disposableAsset);

            return disposableAsset;
        }

        public DisposableAsset<T> GetDisposableAsset<T>(AssetReferenceT<T> assetToLoad, IAssetsDisposer disposer) where T : Object {
            return GetDisposableAsset<T>(assetToLoad as AssetReference, disposer);
        }

        public DisposableAsset<Sprite> GetDisposableAsset(AssetReferenceSprite assetToLoad, IAssetsDisposer disposer) {
            return GetDisposableAsset<Sprite>(assetToLoad as AssetReference, disposer);
        }

        public DisposableAsset<Sprite> GetDisposableAsset(AssetReferenceAtlasedSprite assetToLoad, IAssetsDisposer disposer) {
            return GetDisposableAsset<Sprite>(assetToLoad, disposer);
        }

        private bool ReleaseDisposableAsset(AssetReference assetReference) {
            // check it the asset is registered
            if (!_cachedHandles.TryGetValue(assetReference, out AsyncOperationHandle cachedHandle)) {
                return false;
            }

            // try decrease the counter
            int count = _assetReferenceCount[assetReference];
            if (count <= 0) {
                Log.Error($"Attempt to release an asset to many times {assetReference}", this);
                return false;
            }

            // Postpone not done task
            if (count == 1 && !cachedHandle.IsDone) {
                PostponeDisposingNotLoadedAsset(assetReference);
                count = _assetReferenceCount[assetReference];
                count--;
                _assetReferenceCount[assetReference] = count;
                return true;
            }

            count--;
            _assetReferenceCount[assetReference] = count;

            // release the asset if it is not used anymore
            if (count <= 0) {
                _assetReferenceCount.Remove(assetReference);
                _cachedHandles.Remove(assetReference);

                if (cachedHandle.IsValid()) {
                    cachedHandle.Task.Dispose();
                    assetReference.ReleaseAsset();
                }
            }

            return true;
        }

        private async void PostponeDisposingNotLoadedAsset(AssetReference assetReference) {
            try {
                DisposableAsset<Object> disposableAsset = GetDisposableAsset<Object>(assetReference, _internalDisposer);
                await disposableAsset.WaitForAssetAsync();
                _internalDisposer.Unregister(disposableAsset);
                disposableAsset.Dispose();
            } catch (Exception e) {
                Log.Exception(e, this);
            }
        }

        /// <summary>
        /// Bulk release all the assets still in cache that have been loaded by the manager.
        /// </summary>
        private void ReleaseAndClearAssets() {
            foreach (AsyncOperationHandle handle in _cachedHandles.Values) {
                if (!handle.IsValid() || handle.Status != AsyncOperationStatus.Succeeded) {
                    continue;
                }
                handle.Task.Dispose();
                Addressables.Release(handle);
            }

            _assetReferenceCount.Clear();
            _cachedHandles.Clear();
        }

        private void OnDestroy() {
            ReleaseAndClearAssets();
        }

        private sealed class DummyAddressablesCacheManager : IAddressablesCacheManager {
            public bool IsValid => false;

            public DisposableAsset<T> GetDisposableAsset<T>(AssetReference assetToLoad, IAssetsDisposer disposer) where T : Object {
                throw new NotSupportedException("Dummy Addressables Cache Manager does not support asset loading.");
            }

            public DisposableAsset<T> GetDisposableAsset<T>(AssetReferenceT<T> assetToLoad, IAssetsDisposer disposer) where T : Object {
                throw new NotSupportedException("Dummy Addressables Cache Manager does not support asset loading.");
            }
        }
    }
}
