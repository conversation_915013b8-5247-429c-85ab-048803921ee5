# com.stratkit.addressables-utils

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 0.1.0
 - Initial version

## Version 0.1.1
 - Reset embedded prefab transform to (0,0,0)

## Version 0.1.2
 - Get rid of managed components usages with ECB

## Version 0.2.0
- Added support for AssetRefenreceAtlasedSprite

## Version 0.2.1
- Add extra check

## Version 0.2.2
 - Disable automatic reference to assembly.

## Version 0.2.3
 - Fix asmdef asset name not matching the assembly name.

## Version 0.3.0
 - Added `EditorAddressablesCacheManager` to load addressable assets in the editor.

## Version 0.3.1
 - Fix compilation errors during builds.

## Version 0.4.0
 - Apply auto-refactors for FE-Content-Item

## Version 0.4.1
 - [WHS-6080](https://bytrolabs.atlassian.net/browse/WHS-6080) Fix cache manager taking ownership of assets which were not loaded by it. This is a temporary fix, right now the first cache manager to load an asset will take ownership of it, which is not ideal. A solution has been requested [here](https://bytrolabs.atlassian.net/browse/WHS-6158).
 - Add context to all `AddressablesCacheManager` logs.
