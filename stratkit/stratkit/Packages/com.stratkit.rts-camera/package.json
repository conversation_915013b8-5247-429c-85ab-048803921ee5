{"name": "com.stratkit.rts-camera", "version": "2.5.1", "displayName": "Stratkit Rts Camera", "unity": "2022.3", "unityRelease": "0f1", "description": "Gives an ability to pan and zoom the map.", "publishConfig": {"registry": "https://npm.pkg.github.com/@bytro"}, "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.sectionstreaming": "5.8.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.user-input-actions": "1.9.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2"}, "author": {"name": "Bytro", "email": "<EMAIL>", "url": "https://www.bytro.com"}, "samples": [{"displayName": "Example Map", "description": "This sample contains a scene with a part of the world map taken from Google Maps with details from north of Germany through Hamburg to park \"Planten un Blomen\".", "path": "Samples~"}]}