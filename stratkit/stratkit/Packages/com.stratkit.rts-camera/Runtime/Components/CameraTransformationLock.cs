﻿using Unity.Entities;

namespace Stratkit.RtsCamera.Components {
    /// <summary>
    /// Component that indicates that there is a lock on the camera, and <see cref="CameraTransformationSystem"/> should not apply any transformations to the camera.
    /// This is used to prevent camera movement when the camera is being focused on a specific point or when the camera is being controlled by a tween.
    /// </summary>
    public struct CameraTransformationLock : IComponentData, IEnableableComponent {
    }
}
