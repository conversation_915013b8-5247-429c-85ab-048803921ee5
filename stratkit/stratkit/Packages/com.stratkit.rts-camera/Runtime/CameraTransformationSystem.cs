using Stratkit.Mathematics;
using Stratkit.RtsCamera.Components;
using Stratkit.RtsCamera.Config;
using Stratkit.RtsCamera.Internal;
using Stratkit.RtsCamera.Internal.Tween;
using Stratkit.RtsCamera.Tween;
using Stratkit.SectionStreaming.Components;
using Stratkit.UserInputActions;
using Stratkit.UserInputActions.Components;
using Stratkit.UserInputActions.Data;
using Stratkit.UserInputActions.Enum;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace Stratkit.RtsCamera {
    /// <summary>
    /// This system takes output of <see cref="UserActionInWorldDetectionSystem"/>
    /// </summary>
    [UpdateAfter(typeof(UserActionInWorldDetectionSystem))]
    public sealed partial class CameraTransformationSystem : SystemBase {
        /// <summary>
        /// Max correction offset that can be applied to the camera within one frame can not be bigger than
        /// half of the current camera's deviation in worlds coords. In other case the camera would be moved
        /// too far and may fall into the constraint on the other side of the map what in consequence will apply
        /// big deviation correction in opposite direction and repeat the whole scenario infinitely.
        /// </summary>
        private const float MaxCorrectionRatioInOneFrame = 0.5f;

        private EntityCommandBuffer _tmpEcb;
        private EntityQuery _cameraLockQuery;

        private sealed class StateComponent : IComponentData {
            public bool Initialized;
        }

        private sealed class InertiaData {
            public Vector3 Speed;
            public float Duration;

            public Vector2 GestureStartPosition;
            public Vector2 GestureStopPosition;
            public float GestureDuration;
        }

        protected override void OnCreate() {
            _cameraLockQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<CameraTransformationLock>()
                .Build(this);

            RequireForUpdate<CameraConfig>();
            RequireForUpdate<CameraContainer>();
            RequireForUpdate<MapConstraintsComponent>();
            RequireForUpdate<DeviceAPIComponent>();

            Entity entity = EntityManager.CreateEntity(
                stackalloc ComponentType[] {
                    ComponentType.ReadWrite<CameraAddPositionTweenComponent>(),
                    ComponentType.ReadWrite<CameraVelocityTweenComponent>()
                }
            );

            EntityManager.SetComponentData(entity, new CameraAddPositionTweenComponent());
            EntityManager.SetComponentEnabled<CameraAddPositionTweenComponent>(entity, false);

            EntityManager.SetComponentData(entity, new CameraVelocityTweenComponent());
            EntityManager.SetComponentEnabled<CameraVelocityTweenComponent>(entity, false);

            EntityManager.AddComponent<StateComponent>(SystemHandle);
            EntityManager.SetComponentData(SystemHandle, new StateComponent());

            EntityManager.CreateSingleton<CameraPositionComponent>();
            EntityManager.CreateSingleton<CameraStartActionComponent>();
            // CameraHeightComponent is created on the first update,
            // so system depending i.e., on Tilt can receive the correct data
        }

        private void TryInit(Camera camera, CameraConfig configRef) {
            StateComponent state = EntityManager.GetComponentData<StateComponent>(SystemHandle);
            if (state.Initialized) {
                return;
            }

            // set initial camera rotation and position
            Transform transform = camera.transform;
            Vector3 rotationEuler = transform.rotation.eulerAngles;
            rotationEuler.y = 0f;
            rotationEuler.z = 0f;
            transform.rotation = Quaternion.Euler(rotationEuler);
            float farthestDistance = configRef.Current.Zoom.FarthestDistance;
            Vector3 cameraPosition = transform.position;
            if (cameraPosition.y > farthestDistance) {
                cameraPosition.y = farthestDistance;
                camera.transform.position = cameraPosition;
            }

            state.Initialized = true;
        }

        protected override void OnUpdate() {
            if (!_cameraLockQuery.IsEmpty) {
                return;
            }

            CameraContainer cameraContainer = SystemAPI.ManagedAPI.GetSingleton<CameraContainer>();
            Camera camera = cameraContainer.Camera;
            if (camera == null) {
                return;
            }

            _tmpEcb = new EntityCommandBuffer(Allocator.Temp);

            CameraConfig configRef = SystemAPI.ManagedAPI.GetSingleton<CameraConfig>();
            TryInit(camera, configRef);

            Transform transform = camera.transform;
            ConfigData config = configRef.Current;
            MapConstraintsComponent mapConstraints = SystemAPI.GetSingleton<MapConstraintsComponent>();
            DeviceAPIComponent api = SystemAPI.ManagedAPI.GetSingleton<DeviceAPIComponent>();
            ITimeProvider time = api.Time;
            IScreenProvider screen = api.Screen;

            DeviationData deviation;

            ulong frameIndex = SystemAPI.GetSingleton<FrameIndexComponent>().Value;
            bool hasInfiniteScrolling = SystemAPI.HasSingleton<InfiniteScrollingConfig>();
            if (hasInfiniteScrolling) {
                InfiniteScrollingConfig scrollingConfig = SystemAPI.GetSingleton<InfiniteScrollingConfig>();
                Vector3 leftBottom = CameraHelpers.GetPointOnPlane(camera, new float2(0f, 0f));
                Vector3 rightBottom = CameraHelpers.GetPointOnPlane(camera, new float2(screen.Width, 0f));
                Vector3 springCorrection = Vector3.zero;
                if (rightBottom.x > 2 * scrollingConfig.MapWidth) {
                    springCorrection = -scrollingConfig.MapWidth * Vector3.right;
                } else if (leftBottom.x < -scrollingConfig.MapWidth) {
                    springCorrection = +scrollingConfig.MapWidth * Vector3.right;
                }

                deviation = new DeviationData {
                    DampingRate = Vector3.one,
                    SpringCorrection = springCorrection,
                };
            } else {
                Bounds2 bounds = mapConstraints.Value.GetBoundsForHeight(camera.transform.position.y);
                deviation = CalculateDeviationCorrection(camera, config.Constraints, bounds, time.DeltaTime, screen);
            }

            // Schedule zooming tween
            ScrollActionComponent scrollAction = SystemAPI.GetSingleton<ScrollActionComponent>();
            PerformScrollAction(transform, scrollAction, frameIndex, config.Zoom);

            PositionActionComponent positionAction = SystemAPI.GetSingleton<PositionActionComponent>();

            // Perform translation
            bool positionChanged = PerformPositionAction(
                transform,
                positionAction,
                frameIndex,
                deviation,
                config,
                time,
                screen
            );

            // Tween
            {
                float dt = time.DeltaTime;

                foreach ((CameraAddPositionTweenComponent tween, Entity entity) in SystemAPI
                    .Query<CameraAddPositionTweenComponent>()
                    .WithEntityAccess()) { // there is only 1
                    UpdateCameraActionSingletonFrameIndexAtEnd();
                    if (tween.Step(dt)) {
                        EntityManager.SetComponentEnabled<CameraAddPositionTweenComponent>(entity, false);
                    }
                }

                foreach ((CameraVelocityTweenComponent tween, Entity entity) in SystemAPI
                    .Query<CameraVelocityTweenComponent>()
                    .WithEntityAccess()) { // there is only 1
                    UpdateCameraActionSingletonFrameIndexAtEnd();
                    if (tween.StepWithDeviationData(dt, deviation)) {
                        EntityManager.SetComponentEnabled<CameraVelocityTweenComponent>(entity, false);
                    }
                }
            }

            // Deviation correction:
            if (!positionChanged) {
                Vector3 localPosition = transform.localPosition;
                localPosition += deviation.SpringCorrection;
                transform.localPosition = localPosition;
            }

            // Apply tilt
            if (config.Zoom.ApplyTiltCurve) {
                ApplyTilt(transform, config.Zoom);
            }

            // Apply Clip Planes
            if (config.DynamicallySetClipPlanes) {
                Vector3 cameraPos = camera.transform.position;
                Vector3 topPointOnGround = CameraHelpers.GetPointOnPlane(
                    camera,
                    new Vector2(screen.Width * 0.5f, screen.Height)
                );
                Vector3 bottomPointOnGround =
                    CameraHelpers.GetPointOnPlane(camera, new Vector2(screen.Width * 0.5f, 0f));

                float maxVisibleDistance = (topPointOnGround - cameraPos).magnitude * 2f;
                float minVisibleDistance = math.max(0.01f, (bottomPointOnGround - cameraPos).magnitude * 0.5f);

                if (config.DynamicallySetNearClipPlaneOnOverlayCameras) {
                    camera.nearClipPlane = minVisibleDistance;
                }

                if (config.DynamicallySetFarClipPlaneOnOverlayCameras) {
                    camera.farClipPlane = maxVisibleDistance;
                }

                if (config.DynamicallySetClipPlanesOnOverlayCameras) {
                    List<Camera> overlayCameras = cameraContainer.OverlayCameras;
                    if (overlayCameras != null!) {
                        foreach (Camera overlayCamera in overlayCameras) {
                            if (config.DynamicallySetNearClipPlaneOnOverlayCameras) {
                                overlayCamera.nearClipPlane = minVisibleDistance;
                            }

                            if (config.DynamicallySetFarClipPlaneOnOverlayCameras) {
                                overlayCamera.farClipPlane = maxVisibleDistance;
                            }
                        }
                    }
                }
            }

            UpdateCameraHeightProperties(transform);
            UpdateCameraPositionProperties(transform);

            _tmpEcb.Playback(EntityManager);
            _tmpEcb.Dispose();
        }

        private void ApplyTilt(Transform transform, ZoomConfig zoomConfig) {
            float distance = transform.localPosition.y;
            float tilt = zoomConfig.TiltCurve.Evaluate(distance);
            Vector3 euler = transform.localRotation.eulerAngles;
            euler.x = tilt;
            transform.localRotation = Quaternion.Euler(euler);
        }

        private void PerformScrollAction(
            Transform transform,
            ScrollActionComponent scrollAction,
            ulong frameIndex,
            ZoomConfig zoomConfig
        ) {
            if (scrollAction.FrameIndex == frameIndex) {
                ScheduleZoomTween(transform, scrollAction, zoomConfig);
            }
        }

        private void ScheduleZoomTween(Transform transform, ScrollActionComponent scrollAction, ZoomConfig zoomConfig) {
            float deltaScale = scrollAction.Value > 0f ? 1f / zoomConfig.ZoomMultiplier : zoomConfig.ZoomMultiplier;
            float3 actionCenter = scrollAction.ActionPosition;
            Vector3 localPosition = transform.localPosition;
            Vector3 newPosition = CalculateNewZoomPosition(
                localPosition,
                actionCenter,
                localPosition.y * deltaScale,
                zoomConfig
            );
            SetCameraActionSingleton(
                localPosition,
                CameraActionTypes.Zoom
            ); // important to do before ScheduleCameraPositionTween so Zoom does not get overwritten
            ScheduleCameraPositionTween(newPosition, transform, zoomConfig.ZoomDuration);
            StopCameraVelocityTween();
        }

        private bool PerformPositionAction(
            Transform transform,
            PositionActionComponent positionAction,
            ulong frameIndex,
            DeviationData deviation,
            ConfigData config,
            ITimeProvider time,
            IScreenProvider screen
        ) {
            PositionActionData firstData = positionAction.First;
            PositionActionData secondData = positionAction.Second;
            bool firstHasContact = firstData.HasContact;
            bool secondHasContact = secondData.HasContact;
            bool firstIsBlocked = firstData.InputActionState == InputActionState.Blocked;
            bool secondIsBlocked = secondData.InputActionState == InputActionState.Blocked;
            bool firstValid = firstData.FrameIndex == frameIndex
                && firstData.InputActionState != InputActionState.None
                && !firstIsBlocked;
            bool secondValid = secondData.FrameIndex == frameIndex
                && secondData.InputActionState != InputActionState.None
                && !secondIsBlocked;

            // Double finger drag:
            if (firstHasContact
                && secondHasContact
                && (firstValid || secondValid)
                && !(firstIsBlocked || secondIsBlocked)) {
                Vector3 localPosition = transform.localPosition;
                Vector3 newLocalPosition = localPosition;

                // Translation
                if (config.EnablePanningWhileZooming) {
                    float3 deltaCurrent = Vector3.zero;
                    if (firstValid) {
                        deltaCurrent += firstData.Recent.Position - firstData.Current.Position;
                    }

                    if (secondValid) {
                        deltaCurrent += secondData.Recent.Position - secondData.Current.Position;
                    }

                    SetCameraActionSingleton(newLocalPosition, CameraActionTypes.Pan);
                    newLocalPosition += (Vector3)deltaCurrent;
                }

                // Zoom:
                {
                    float heightAtPinchStart = positionAction.PinchStartData.CameraHeight;
                    float pinchStartDistance = math.length(
                        firstData.WhenPinchStart.ScreenPosition - secondData.WhenPinchStart.ScreenPosition
                    );
                    float currentDistance =
                        math.length(firstData.Current.ScreenPosition - secondData.Current.ScreenPosition);
                    float scale;

                    if (config.Zoom.RelativePinching) {
                        scale = pinchStartDistance / currentDistance;
                        if (scale > 1f) {
                            scale = 1f + ((scale - 1f) * config.Zoom.ZoomMultiplier);
                        } else if (scale < 1f) {
                            scale = Mathf.Pow(scale, config.Zoom.ZoomMultiplier);
                        }
                    } else {
                        scale = (pinchStartDistance - currentDistance) / screen.ShorterSide;
                        if (scale > 0f) {
                            scale = 1f + (scale * config.Zoom.ZoomMultiplier);
                        } else {
                            scale /= screen.Ratio;
                            scale += 1f;
                            scale = Mathf.Pow(scale, config.Zoom.ZoomMultiplier);
                        }
                    }

                    float3 actionCenter = 0.5f * (firstData.Current.Position + secondData.Current.Position);
                    SetCameraActionSingleton(newLocalPosition, CameraActionTypes.Zoom);
                    newLocalPosition = CalculateNewZoomPosition(
                        newLocalPosition,
                        actionCenter,
                        heightAtPinchStart * scale,
                        config.Zoom
                    );
                }

                // Deviation correction:
                {
                    Vector3 deltaCurrent = newLocalPosition - localPosition;
                    deltaCurrent = deviation.ApplyDampingOnUserInput(deltaCurrent);
                    Vector3 position = transform.localPosition;
                    position += deltaCurrent;
                    transform.localPosition = position;
                }

                StopCameraVelocityTween();
                StopCameraPositionTween();
            } else if (firstValid) {
                PerformSinglePositionOrReleaseAction(
                    transform,
                    firstData,
                    secondData,
                    deviation,
                    config,
                    time.RealTime,
                    screen
                );
            } else if (secondValid) {
                PerformSinglePositionOrReleaseAction(
                    transform,
                    secondData,
                    firstData,
                    deviation,
                    config,
                    time.RealTime,
                    screen
                );
            }

            return firstHasContact || secondHasContact;
        }

        private static bool HadTouchRecently(PositionActionData touchData, float realTime, ConfigData config) {
            return realTime - touchData.Current.Time < config.MinTouchDurationAfterPinchToStartPan;
        }

        /// <summary>
        /// Resolves which action should be performed according to an active action data and an inactive one (e.g. a user finishing a pinch-zoom action)
        /// </summary>
        /// <param name="transform"><see cref="Transform"/> of the camera</param>
        /// <param name="actionData"><see cref="PositionActionData"/> main action data that is valid this frame</param>
        /// <param name="otherActionData"><see cref="PositionActionData"/> secondary action data which is not valid this frame (e.g. a finger lifting up)</param>
        /// <param name="deviation"><see cref="DeviationData"/> calculated for this frame</param>
        /// <param name="config"><see cref="ConfigData"/> of the camera</param>
        /// <param name="realTime"><see cref="float"/> with the current real time passed</param>
        /// <param name="screen"><see cref="IScreenProvider"/> with screen information</param>
        private void PerformSinglePositionOrReleaseAction(
            Transform transform,
            PositionActionData actionData,
            PositionActionData otherActionData,
            DeviationData deviation,
            ConfigData config,
            float realTime,
            IScreenProvider screen
        ) {
            if (actionData.HasContact) {
                if (config.EnablePanningWhileZooming || !HadTouchRecently(otherActionData, realTime, config)) {
                    // Update camera position only if touch gesture lasts at least a while
                    if (realTime - actionData.Start.Time > config.MinTouchDurationToStartPan) {
                        Vector3 deltaCurrent = actionData.Recent.Position - actionData.Current.Position;
                        deltaCurrent = deviation.ApplyDampingOnUserInput(deltaCurrent);
                        Vector3 position = transform.localPosition;
                        position += deltaCurrent;
                        transform.localPosition = position;
                        SetCameraActionSingleton(position, CameraActionTypes.Pan);
                    }
                }

                StopCameraVelocityTween();
                StopCameraPositionTween();
            } else {
                InertiaData? inertia1 = null;
                InertiaData? inertia2 = null;

                if (!otherActionData.HasContact
                    && actionData.Current.Time - otherActionData.Current.Time
                    < config.MinTouchDurationAfterPinchToStartPan
                    && otherActionData.Current.Time
                    > actionData.Start
                        .Time // The otherActionData was present within the same period as the actionData, to avoid interpreting both actions as a pinch
                ) {
                    inertia1 = CalculateInertia(actionData, config.Inertia, realTime, screen);
                    inertia2 = CalculateInertia(otherActionData, config.Inertia, realTime, screen);
                } else if (!otherActionData.HasContact || config.EnablePanningWhileZooming) {
                    inertia1 = CalculateInertia(actionData, config.Inertia, realTime, screen);
                }

                if (inertia1 != null) {
                    // double swipe (or double release)
                    if (inertia2 != null) {
                        float avgDuration = 0.5f * (inertia1.Duration + inertia2.Duration);

                        bool tweenPosition = config.Inertia.EnablePositionTweenAfterPinch;
                        if (tweenPosition) {
                            ScheduleCameraVelocityTween(
                                0.5f * (inertia1.Speed + inertia2.Speed),
                                transform,
                                avgDuration
                            );
                        }

                        float2 position1 = inertia1.GestureStartPosition;
                        float2 position2 = inertia2.GestureStartPosition;
                        float2 targetPosition1 = inertia1.GestureStopPosition;
                        float2 targetPosition2 = inertia2.GestureStopPosition;
                        float distance = math.distance(position1, position2);
                        float targetDistance = math.distance(targetPosition1, targetPosition2);
                        float pinchDistance = targetDistance - distance;
                        float gestureDuration = (inertia1.GestureDuration + inertia2.GestureDuration) / 2;
                        float force = pinchDistance / gestureDuration;
                        float actionDistance = force * avgDuration;
                        if (config.Zoom.RelativePinching) {
                            actionDistance /= Vector2.Distance(
                                inertia1.GestureStartPosition,
                                inertia2.GestureStartPosition
                            );
                        } else {
                            actionDistance /= screen.ShorterSide;
                        }

                        float deltaScale = actionDistance > 0f ? 1f / actionDistance : -actionDistance;
                        float3 actionCenter = (actionData.Start.Position + otherActionData.Start.Position) * 0.5f;
                        Vector3 localPosition = transform.localPosition;
                        Vector3 newPosition = CalculateNewZoomPosition(
                            localPosition,
                            actionCenter,
                            localPosition.y * deltaScale,
                            config.Zoom
                        );
                        ScheduleCameraPositionTween(newPosition, transform, avgDuration, tweenPosition);
                    } else {
                        // single swipe or release
                        ScheduleCameraVelocityTween(inertia1.Speed, transform, inertia1.Duration);
                    }
                }
            }
        }

        private InertiaData? CalculateInertia(
            PositionActionData actionData,
            InertiaConfig inertiaConfig,
            float realTime,
            IScreenProvider screen
        ) {
            float shorterScreenSide = screen.ShorterSide;
            Vector3 deltaAverage = actionData.Current.Position - actionData.Previous.Position;

            InertiaData output = new();

            // Check it was a swipe gesture:
            double gestureDuration = actionData.Current.Time - actionData.Start.Time;
            if (gestureDuration <= float.Epsilon) {
                return null;
            }

            Vector2 pixelDelta;
            float deltaTime;
            float deltaLength;

            // swipe gesture: use data of whole swipe
            if (gestureDuration < inertiaConfig.SwipeGestureDuration) {
                pixelDelta = actionData.Current.ScreenPosition - actionData.Start.ScreenPosition;
                deltaTime = (float)gestureDuration;
                deltaLength = math.length(actionData.Current.Position - actionData.Start.Position);

                output.GestureStartPosition = actionData.Start.ScreenPosition;
                output.GestureStopPosition = actionData.Current.ScreenPosition;
            } else {
                // throw/release gesture: use data from last 2 input actions
                pixelDelta = actionData.Current.ScreenPosition - actionData.Previous.ScreenPosition;
                deltaTime = (float)(actionData.Current.Time - actionData.Previous.Time);
                if (deltaTime <= float.Epsilon) {
                    return null;
                }

                deltaLength = deltaAverage.magnitude;

                output.GestureStartPosition = actionData.Previous.ScreenPosition;
                output.GestureStopPosition = actionData.Current.ScreenPosition;
            }

            output.GestureDuration = deltaTime;

            Vector3 direction = deltaAverage.normalized;
            float speedValue = deltaLength / deltaTime;

            // Schedule translation tween
            Vector2 screenDelta = pixelDelta / shorterScreenSide;
            Vector2 screenSpeed = -screenDelta / deltaTime;
            float screenSpeedValue = screenSpeed.magnitude;
            if (screenSpeedValue >= inertiaConfig.MinScreenSpeed) {
                float rate = screenSpeedValue / inertiaConfig.MaxScreenSpeed;
                Vector3 speed = -inertiaConfig.SpeedMultiplier * speedValue * direction;

                if (rate > 1f) {
                    speed /= rate;
                    rate = 1f;
                }

                float duration = math.lerp(inertiaConfig.MinDuration, inertiaConfig.MaxDuration, rate);
                output.Speed = speed;
                output.Duration = duration;
                return output;
            }

            return null;
        }

        private void StopCameraVelocityTween() {
            foreach ((CameraVelocityTweenComponent velocityTween, Entity entity) in
                SystemAPI
                    .Query<CameraVelocityTweenComponent>()
                    .WithOptions(EntityQueryOptions.IgnoreComponentEnabledState)
                    .WithEntityAccess()) { // there is only 1
                velocityTween.Data = default;
                velocityTween.From = Vector3.zero;
                velocityTween.To = Vector3.zero;
                EntityManager.SetComponentEnabled<CameraVelocityTweenComponent>(entity, false);
            }
        }

        private void ScheduleCameraVelocityTween(float3 velocity, Transform transform, float duration) {
            foreach ((CameraVelocityTweenComponent velocityTween, Entity entity) in
                SystemAPI
                    .Query<CameraVelocityTweenComponent>()
                    .WithOptions(EntityQueryOptions.IgnoreComponentEnabledState)
                    .WithEntityAccess()) { // there is only 1
                velocityTween.Data = new CameraFiniteTweenData(duration, transform);
                velocityTween.From = velocity;
                velocityTween.To = Vector3.zero;
                EntityManager.SetComponentEnabled<CameraVelocityTweenComponent>(entity, true);
                SetCameraActionSingleton(transform.localPosition, CameraActionTypes.Pan);
            }
        }

        private void StopCameraPositionTween() {
            foreach ((CameraAddPositionTweenComponent positionTween, Entity entity) in
                SystemAPI
                    .Query<CameraAddPositionTweenComponent>()
                    .WithOptions(EntityQueryOptions.IgnoreComponentEnabledState)
                    .WithEntityAccess()) { // there is only 1
                positionTween.Data = default;
                positionTween.From = Vector3.zero;
                positionTween.To = Vector3.zero;
                EntityManager.SetComponentEnabled<CameraAddPositionTweenComponent>(entity, false);
            }
        }

        private void ScheduleCameraPositionTween(
            Vector3 targetPosition,
            Transform transform,
            float duration,
            bool onlyY = false
        ) {
            foreach ((CameraAddPositionTweenComponent positionTween, Entity entity) in
                SystemAPI
                    .Query<CameraAddPositionTweenComponent>()
                    .WithOptions(EntityQueryOptions.IgnoreComponentEnabledState)
                    .WithEntityAccess()) { // there is only 1
                positionTween.Data = new CameraFiniteTweenData(duration, transform);
                positionTween.From = transform.localPosition;
                positionTween.To = targetPosition;
                positionTween.AxisMask = onlyY ? Vector3.up : Vector3.one;
                EntityManager.SetComponentEnabled<CameraAddPositionTweenComponent>(entity, true);
            }

            SetCameraActionSingleton(transform.localPosition, CameraActionTypes.Pan);
        }

        /// <summary>
        /// Calculates new camera position after zoom in or out, takes into consideration:
        /// - actionCenter (place where user hold the mouse cursor or where was center of touches)
        /// - tilt configuration - adjusts offset to keep global actionCenter in the same position on the screen
        /// Returns new position
        /// </summary>
        private Vector3 CalculateNewZoomPosition(
            float3 localPosition,
            float3 actionCenter,
            float scaledDistance,
            ZoomConfig zoomConfig
        ) {
            float distance = localPosition.y;
            float newDistance = math.clamp(scaledDistance, zoomConfig.ClosestDistance, zoomConfig.FarthestDistance);
            float realDeltaScale = newDistance / distance;
            float3 directionToActionCenter = actionCenter - localPosition;
            float3 newPosition = actionCenter - (directionToActionCenter * realDeltaScale);

            // Apply tilt offset
            if (zoomConfig.ApplyTiltCurve) {
                float tilt1 = zoomConfig.TiltCurve.Evaluate(distance);
                float destTilt = zoomConfig.TiltCurve.Evaluate(newDistance);
                float tiltDiff = destTilt - tilt1;
                if (Mathf.Abs(tiltDiff) > float.Epsilon) {
                    Vector3 newDirectionInvalid =
                        Quaternion.AngleAxis(tiltDiff, Vector3.right) * directionToActionCenter;
                    Ray newRayInvalid = new(newPosition, newDirectionInvalid);
                    float3 newActionCenterInvalid = MathHelpers.GetPointOnPlane(newRayInvalid);
                    float3 offset = actionCenter - newActionCenterInvalid;
                    newPosition += offset;
                }
            }

            return newPosition;
        }

        private DeviationData CalculateDeviationCorrection(
            Camera camera,
            ConstraintsConfig constraintsConfig,
            Bounds2 mapConstraints,
            float dt,
            IScreenProvider screen
        ) {
            float width = screen.Width;
            float height = screen.Height;

            float3 leftBottom = CameraHelpers.GetPointOnPlane(camera, new float2(0f, 0f));
            float3 rightBottom = CameraHelpers.GetPointOnPlane(camera, new float2(width, 0f));
            float3 top = CameraHelpers.GetPointOnPlane(camera, new float2(width * 0.5f, height));

            float3 min = mapConstraints.Min.ConvertToSpace3D();
            float3 max = mapConstraints.Max.ConvertToSpace3D();

            float3 worldDeviation = float3.zero;
            float2 screenDeviation = float2.zero;

            if (leftBottom.x < min.x) {
                worldDeviation.x += min.x - leftBottom.x;
                screenDeviation.x += camera.WorldToScreenPoint(new float3(min.x, 0f, leftBottom.z)).x;
            }

            if (rightBottom.x > max.x) {
                worldDeviation.x += max.x - rightBottom.x;
                screenDeviation.x -= width - camera.WorldToScreenPoint(new float3(max.x, 0f, rightBottom.z)).x;
            }

            if (constraintsConfig.EnableConstraintsZ) {
                if (rightBottom.z < min.z) {
                    worldDeviation.z += min.z - rightBottom.z;
                    screenDeviation.y += camera.WorldToScreenPoint(new float3(rightBottom.x, 0f, min.z)).y;
                }

                if (top.z > max.z) {
                    worldDeviation.z += max.z - top.z;
                    screenDeviation.y -= height - camera.WorldToScreenPoint(new float3(rightBottom.x, 0f, max.z)).y;
                }
            }

            float shortestSide = Mathf.Min(width, height);
            float2 dampingRate = screenDeviation / (shortestSide * constraintsConfig.MaxDeviation * 2f);
            dampingRate.x = 1f - math.abs(math.clamp(dampingRate.x, -1f, 1f));
            dampingRate.y = 1f - math.abs(math.clamp(dampingRate.y, -1f, 1f));

            float multiplier = math.min(constraintsConfig.SpringRate * dt, MaxCorrectionRatioInOneFrame);
            float3 deviationCorrection = worldDeviation * multiplier;

            return new DeviationData {
                SpringCorrection = deviationCorrection,
                DampingRate = dampingRate.ConvertToSpace3D(1f),
            };
        }

        private void UpdateCameraHeightProperties(Transform cameraTransform) {
            float cameraHeight = cameraTransform.position.y;

            Entity singletonEntity;
            CameraHeightComponent properties;

            if (!SystemAPI.HasSingleton<CameraHeightComponent>())
            {
                singletonEntity = EntityManager.CreateSingleton<CameraHeightComponent>();
                properties = new CameraHeightComponent
                {
                    Height = cameraHeight,
                    Tilt = cameraTransform.rotation.eulerAngles.x
                };

                properties.Publish(_tmpEcb, singletonEntity);
                EntityManager.SetComponentData(singletonEntity, properties);
                return;
            }

            singletonEntity = SystemAPI.GetSingletonEntity<CameraHeightComponent>();
            properties = EntityManager.GetComponentData<CameraHeightComponent>(singletonEntity);
            if (!(math.abs(properties.Height - cameraHeight) > 0.0001f)) {
                return;
            }

            // Use EntityManager SetComponentData so SetChangedVersionFilter knows about the changes
            properties.Height = cameraHeight;
            properties.Tilt = cameraTransform.rotation.eulerAngles.x;
            properties.Publish(_tmpEcb, singletonEntity);
            EntityManager.SetComponentData(singletonEntity, properties);
        }

        private void UpdateCameraPositionProperties(Transform cameraTransform) {
            Vector3 cameraPosition = cameraTransform.position;
            float cameraX = cameraPosition.x;
            float cameraZ = cameraPosition.z;

            CameraPositionComponent properties = SystemAPI.GetSingleton<CameraPositionComponent>();
            if (!(math.abs(properties.PosX - cameraX) > 0.0001f) && !(math.abs(properties.PosZ - cameraZ) > 0.0001f)) {
                return;
            }

            Entity singletonEntity = SystemAPI.GetSingletonEntity<CameraPositionComponent>();

            // Use EntityManager SetComponentData so SetChangedVersionFilter knows about the changes
            properties.PosX = cameraX;
            properties.PosZ = cameraZ;
            properties.Publish(_tmpEcb, singletonEntity);
            EntityManager.SetComponentData(singletonEntity, properties);
        }

        /// <summary>
        /// Update the CameraStartActionComponent to the latest change info happened to the camera
        /// </summary>
        /// <param name="startPosition">the position of the camera before transformation. Ignored if there is an action since last frame already</param>
        /// <param name="actionType">the action that causes the transformation. Ignored if there is an action since last frame already</param>
        private void SetCameraActionSingleton(Vector3 startPosition, CameraActionTypes actionType) {
            ulong frame = SystemAPI.GetSingleton<FrameIndexComponent>().Value;
            CameraStartActionComponent previousAction = SystemAPI.GetSingleton<CameraStartActionComponent>();
            bool updatePreviousAction = frame - previousAction.FrameIndexAtEnd <= 1;
            if (updatePreviousAction) {
                UpdateCameraActionSingletonFrameIndexAtEnd();
                return;
            }

            SystemAPI.SetSingleton(
                new CameraStartActionComponent {
                    ActionType = actionType,
                    StartPosition = startPosition,
                    FrameIndexAtStart = frame,
                    FrameIndexAtEnd = frame,
                }
            );
        }

        private void UpdateCameraActionSingletonFrameIndexAtEnd() {
            CameraStartActionComponent component = SystemAPI.GetSingleton<CameraStartActionComponent>();
            SystemAPI.SetSingleton(
                new CameraStartActionComponent {
                    ActionType = component.ActionType,
                    StartPosition = component.StartPosition,
                    FrameIndexAtStart = component.FrameIndexAtStart,
                    FrameIndexAtEnd = SystemAPI.GetSingleton<FrameIndexComponent>().Value,
                }
            );
        }
    }
}
