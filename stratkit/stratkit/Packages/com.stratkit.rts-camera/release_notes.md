# com.stratkit.rts-camera

_When adding new versions, add it at the end of the file for simplicity_
_Tag with [P10-XXXX] when the task has a ticket related. E.g. - [P10-1234](https://bytrolabs.atlassian.net/browse/P10-1234) Fix NRE on startup_

## Version 1.0.0
 - Initial version

## Version 1.0.1
 - Add Missing RequireForUpdate on System

## Version 1.0.2
 - Fix for `ClickableArchetype.Types` type change.
 - Fix unit tests.
## Version 1.1.0
 - Added CameraPositionComponent (with CameraPositionComponentHasChanged) to be able to track change of camera position

## Version 1.2.0
 - Reference province holder through singleton.

## Version 1.3.0
 - Remove Stratkit.MapBalancingData dependency.
 - Move `MapConstraints` into `RtsCamera` package.

## Version 1.3.1
 - Disable automatic reference to assembly.

## Version 1.4.0
 - Use baked map size for camera constraints.

## Version 1.5.0
 - Update for `Stratkit.Mathematics.Bounds2`.

## Version 1.6.0
 - Update for components moved to Stratkit.SectionStreaming.

## Version 1.7.0
 - Update references to renamed Map module.

## Version 2.0.0
 - Add support for Infinite Scrolling

## Version 2.0.1
 - Fix bug that camera snap too early for Infinite Scrolling when scrolling to the right

## Version 2.0.2
 - Add reference to `Unity.Burst` in package assembly.

## Version 2.1.0
 - Prevent camera from moving when input was over UI.

## Version 2.2.0
 - [WHS-3575](https://bytrolabs.atlassian.net/browse/WHS-3575) Avoid unintended menu opening.

## Version 2.3.0
 - Add zoom adaptive map bounds.
 - Deprecated previous fixed `CameraBoundOnAxis` approach.

## Version 2.3.1
 - [WHS-3895](https://bytrolabs.atlassian.net/browse/WHS-3895) Fix moving to zoom 0 when holding UI.

## Version 2.4.0
 - It is now possible to separately disable/enable near and far dynamic clipping planes when DynamicallySetClipPlanes is enabled

## Version 2.4.1
 - [WHS-5435](https://bytrolabs.atlassian.net/browse/WHS-5435) Fix of: Textures and UI elements disappear when tapping the army number panel while the map is panning in after the loading screen.

## Version 2.5.0
 - `CameraHeightComponent` is now created at the first update of camera height

## Version 2.5.1
 - Added `CameraLock` component to prevent camera jumping during tweening.
