%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-459461733949389357
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: kaelacsBane_MTL
  m_Shader: {fileID: -6465566751694194690, guid: 36fb471bcdf20874aa92c279ba65667b, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 580dbccb18a984df0b5593f6578e0515, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture1:
        m_Texture: {fileID: 2800000, guid: d40568991b2f04829af97d6e7819df9a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture10:
        m_Texture: {fileID: 2800000, guid: 580dbccb18a984df0b5593f6578e0515, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture11:
        m_Texture: {fileID: 2800000, guid: 580dbccb18a984df0b5593f6578e0515, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2:
        m_Texture: {fileID: 2800000, guid: d8934588ddac94332966c424719a1cea, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture3:
        m_Texture: {fileID: 2800000, guid: 99e4ca27efdf04d1182f8268e6e0e411, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture4:
        m_Texture: {fileID: 2800000, guid: c6864dc5eb8424495aa8a6df569c4a02, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture5:
        m_Texture: {fileID: 2800000, guid: 9435bea2cb36c46bc963d196fd68fc9c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture6:
        m_Texture: {fileID: 2800000, guid: c86bc194e9a651f4682320a4e1a9ea9d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture7:
        m_Texture: {fileID: 2800000, guid: 9e882c4bc02134c8c93dcc3250450b87, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture8:
        m_Texture: {fileID: 2800000, guid: 6ecb201ac284e4a968dea61aff7bb949, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture9:
        m_Texture: {fileID: 2800000, guid: 580dbccb18a984df0b5593f6578e0515, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _Cell_Density: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _Global_Tile: 4.7
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _Texture10RemapMax: 1
    - _Texture10RemapMin: 0
    - _Texture11RemapMax: 1
    - _Texture11RemapMin: 0
    - _Texture12RemapMax: 1
    - _Texture12RemapMin: 0
    - _Texture1RemapMax: 1
    - _Texture1RemapMin: 0
    - _Texture2RemapMax: 1
    - _Texture2RemapMin: 0
    - _Texture3RemapMax: 0.492
    - _Texture3RemapMin: 0.113
    - _Texture4RemapMax: 0.72
    - _Texture4RemapMin: 0.33
    - _Texture5RemapMax: 1
    - _Texture5RemapMin: 0
    - _Texture6RemapMax: 1
    - _Texture6RemapMin: 0
    - _Texture7RemapMax: 1
    - _Texture7RemapMin: 0
    - _Texture8RemapMax: 0.474
    - _Texture8RemapMin: 0
    - _Texture9RemapMax: 1
    - _Texture9RemapMin: 0
    - _Uv1_Tile: 1
    - _Uv2_Tile: 1
    - _Uv3_Tile: 1
    - _Uv4_Tile: 1
    - _Uv_10_Tile: 1
    - _Uv_11_Tile: 1
    - _Uv_12_Tile: 1
    - _Uv_1_Tile: 0.1
    - _Uv_2_Tile: 0.7
    - _Uv_3_Tile: 1
    - _Uv_4_Tile: 0.5
    - _Uv_5_Tile: 1
    - _Uv_6_Tile: 1
    - _Uv_7_Tile: 0.36
    - _Uv_8_Tile: 0.2
    - _Uv_9_Tile: 1
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
