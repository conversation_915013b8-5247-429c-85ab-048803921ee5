#if ENABLE_JAHRO
using JahroConsole;
#endif
using System;
using System.Threading.Tasks;
using Sirenix.OdinInspector;
using Stillfront.AddressablesExtensions;
using Stillfront.Logging;
using Stillfront.Validators;
using Stratkit.AppVersionSplit;
using Stratkit.Core;
using Stratkit.Entities;
using Stratkit.FirebaseIntegration;
using Stratkit.GameJoining.States;
using Stratkit.MapsCatalog;
using Stratkit.Performance.Core;
using Stratkit.Properties.Loader;
using Stratkit.ServiceInjector;
using Stratkit.Tracking.Bytro;
using Stratkit.Tracking.Core.Events;
using Stratkit.Tracking.Core.ModuleExtensions;
using Stratkit.Ui.Common.DataContainers;
using Unity.Entities;
using UnityEngine;
using UnityEngine.AddressableAssets;
using WHS.AppFlow;
using WHS.HelpshiftX;
using WHS.UI;
using WHS.UI.Lobby;
using WHS.UI.Matchmaking;
using FirebaseInitializer = WHS.Firebase.FirebaseInitializer;

namespace WHS.Bootstrap {
    public sealed class BootstrapManager : MonoBehaviour {
        private const string PersistentWorldName = "PersistentWorld";
        private const string MapWorldName = "MapWorld";

        [SerializeField, NotNull]
        private PerformanceSettingsConfigScriptable _performanceSettings = null!;

        [SerializeField]
        private bool _preventSleep = true;

        [SerializeField]
        private AssetReference _mainMenuSceneReference = null!;

        [SerializeField]
        private AssetReference _hubSceneReference = null!;

        [SerializeField]
        private ComponentReference<LobbyScreen> _lobbyScreen = null!;

        [SerializeField, NotNull]
        private BytroInitialTracker _initialTracker = null!;

        [SerializeField, NotNull]
        private AppVersionData _appVersionData = null!;

        [FoldoutGroup("Persistent World Bootstrap", true)]
        [SerializeField]
        private AModuleScriptable[] PreBalancingLoadingPersistentModules = null!;

        [FoldoutGroup("Persistent World Bootstrap")]
        [SerializeField, Searchable]
        [AssetSelector(Paths = "Assets/whs/Bootstrap/Modules/Persistent|Assets/whs/Bootstrap/Modules/Shared")]
        private AModuleScriptable[] PersistentModules = Array.Empty<AModuleScriptable>();

        [FoldoutGroup("Persistent World Bootstrap")]
        [SerializeField, Searchable]
        private ScriptableConfigSingleton[] PersistentModuleConfigs = Array.Empty<ScriptableConfigSingleton>();

        [SerializeField]
        [FoldoutGroup("Persistent World Bootstrap")]
        [InfoBox("Automatically inserted into the list of persistent config modules")]
        private AssetReferenceT<ContentItemsCatalogScriptable> _persistentContentItemsVersionsReference = new(string.Empty);

        [FoldoutGroup("Map World Bootstrap (non-persistent)", true)]
        [SerializeField]
        private AModuleScriptable[] PreBalancingLoadingMapModules = null!;

        [FoldoutGroup("Map World Bootstrap (non-persistent)", true)]
        [SerializeField, Searchable]
        [AssetSelector(Paths = "Assets/whs/Bootstrap/Modules/Map|Assets/whs/Bootstrap/Modules/Shared")]
        private AModuleScriptable[] MapWorldModules = Array.Empty<AModuleScriptable>();

        [FoldoutGroup("Map World Bootstrap (non-persistent)")]
        [SerializeField, Searchable]
        public ScriptableConfigSingleton[] MapModuleConfigs = Array.Empty<ScriptableConfigSingleton>();

        [FoldoutGroup("UI elements")]
        [SerializeField, NotNull]
        private LoginRegisterState.UIData _loggingUiData = null!;

        [FoldoutGroup("UI elements")]
        [SerializeField, NotNull]
        private ErrorPopupData _errorPopupData = null!;

        [FoldoutGroup("Game config")]
        [SerializeField]
        private AssetReferenceT<MapsCatalogScriptable> _maps = new(string.Empty);

        [FoldoutGroup("Game config")]
        [SerializeField, NotNull]
        private LoginRegisterState.Config _loginRegisterConfig = null!;

        [SerializeField, NotNull]
        private HelpshiftXInstallHelper _helpshiftPrefab = null!;

        [SerializeField, NotNull]
        private AssetReferenceGameObject _musicController = null!;

        [SerializeField, NotNull]
        [InfoBox("This is used to stop waiting for notifications to initialize as it could take a long time on iOS"
            + "\nWe also don't want a high number here as the loading speed is VERY IMPORTANT for the game.")]
        private int _notificationsInitializationTimeoutMilliseconds = 1000;

        private World? _persistentWorld;
        private PersistentWorldBootstrap? _persistentWorldBootstrap;
        private EntityQueryCache _persistentEntityQueryCache;
        private string _pushNotificationsToken = string.Empty;
        private ContentItemsCatalog? _contentItemsCatalog;

        private async void Start() {
            DontDestroyOnLoad(this);
            try {
                PerformanceSettingsUtils.ApplySettings(_performanceSettings.LoadingSettings);
                LoadingScreenProgressAdapter loadingScreenProgressAdapter = new(LoadingScreenType.BootstrapGame);
                loadingScreenProgressAdapter.Show();
                loadingScreenProgressAdapter.SetProgress(0.1f, "Jahro and ATT");
#if ENABLE_JAHRO
                await Jahro.InitializeIfNeededAsync();
#endif
                Track(InitialFunnelTrackingSteps.GameInit);
#if UNITY_IOS && !UNITY_EDITOR
                Track(FunnelTrackingSteps.WithStartSuffix(InitialFunnelTrackingSteps.AppTrackingTransparency));
                await InitializeAttAsync();
                Track(FunnelTrackingSteps.WithEndSuffix(InitialFunnelTrackingSteps.AppTrackingTransparency));
#endif
                loadingScreenProgressAdapter.SetProgress(0.2f, "Firebase");
                Track(InitialFunnelTrackingSteps.Firebase.WithStartSuffix());
                await InitializeFirebaseAsync();
                Track(InitialFunnelTrackingSteps.Firebase.WithEndSuffix());

                loadingScreenProgressAdapter.SetProgress(0.3f, "Notifications");
                Track(InitialFunnelTrackingSteps.Notifications.WithStartSuffix());
                await InitializeNotificationsAsync();
                Track(InitialFunnelTrackingSteps.Notifications.WithEndSuffix());

                loadingScreenProgressAdapter.SetProgress(0.4f, "Persistent world");
                Track(InitialFunnelTrackingSteps.AppConfig.WithStartSuffix());
                ConfigureAppSettings();
                Track(InitialFunnelTrackingSteps.AppConfig.WithEndSuffix());

                Track(InitialFunnelTrackingSteps.PersistentEcsWorld.WithStartSuffix());
                await BootstrapPersistentWorldAsync(loadingScreenProgressAdapter, 0.5f, 0.7f);
                Track(InitialFunnelTrackingSteps.PersistentEcsWorld.WithEndSuffix());

                if (_persistentWorld != null) {
                    PerformanceSettingsUtils.CreatePerformanceSettingsEntityOnWorld(_persistentWorld, _performanceSettings);
                }

                await InitializeMusicControllerAsync();

                loadingScreenProgressAdapter.SetProgress(0.8f, "Helpshift");
                Track(InitialFunnelTrackingSteps.HelpshiftX.WithStartSuffix());
                InitializeHelpshift();
                Track(InitialFunnelTrackingSteps.HelpshiftX.WithEndSuffix());

                loadingScreenProgressAdapter.SetProgress(0.85f, "Load maps catalog");
                Track(InitialFunnelTrackingSteps.AvailableMapsLoad.WithStartSuffix());
                MapsCatalogScriptable maps = await LoadAvailableMapsAsync();
                if (_persistentWorld != null) {
                    Entity mapsEntity = _persistentWorld.EntityManager.CreateSingleton<MapsCatalogScriptable>();
                    _persistentEntityQueryCache = EntityAPI.GetEntityQueryCache(_persistentWorld.EntityManager);

                    _persistentWorld.EntityManager.SetComponentData(mapsEntity, maps);
                    _persistentWorld.EntityManager.CreateSingleton(_errorPopupData);
                }

                Track(InitialFunnelTrackingSteps.AvailableMapsLoad.WithEndSuffix());

                loadingScreenProgressAdapter.SetProgress(0.9f, "Start App Flow FSM");
                if (_persistentWorld != null) {
                    Track(InitialFunnelTrackingSteps.AppFlowFsm.WithStartSuffix());
                    AppFlowFsm.Create(
                        _persistentWorld,
                        _persistentEntityQueryCache,
                        MapWorldName,
                        PreBalancingLoadingMapModules,
                        MapWorldModules,
                        MapModuleConfigs,
                        _mainMenuSceneReference,
                        _hubSceneReference,
                        OpenMatchmakingDialogAsync,
                        maps.AvailableMaps,
                        _contentItemsCatalog!,
                        _loggingUiData,
                        _loginRegisterConfig,
                        _appVersionData,
                        _pushNotificationsToken
                    );
                    Track(InitialFunnelTrackingSteps.AppFlowFsm.WithEndSuffix());
                }
            } catch (Exception e) {
                // TODO: show error popup feedback to the user
                Log.Exception(e);
            }
        }

#if UNITY_IOS && !UNITY_EDITOR
        private async Task InitializeAttAsync() {
            Version currentVersion = new(UnityEngine.iOS.Device.systemVersion);
            Version ios14 = new("14.5");
            if (currentVersion < ios14) {
                return;
            }

            Unity.Advertisement.IosSupport.ATTrackingStatusBinding.AuthorizationTrackingStatus status =
                Unity.Advertisement.IosSupport.ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
            Log.Debug($"ATT AuthorizationTrackingStatus = {status}", this);
            if (status != Unity.Advertisement.IosSupport.ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED) {
                return;
            }

            Log.Debug("ATT RequestAuthorizationTracking", this);
            Unity.Advertisement.IosSupport.ATTrackingStatusBinding.RequestAuthorizationTracking();

            const float AttTimeout = 5; // seconds
            float startTime = Time.time;
            while (true) {
                status = Unity.Advertisement.IosSupport.ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
                if (status != Unity.Advertisement.IosSupport.ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED) {
                    Log.Debug($"ATT given AuthorizationTrackingStatus = {status}", this);
                    break;
                }
                if (Time.time - startTime > AttTimeout) {
                    Log.Warning("ATT RequestAuthorizationTracking timeout", this);
                    break;
                }
                await Task.Yield();
            }
        }
#endif

        private async Task InitializeMusicControllerAsync() {
            GameObject? musicController = await _musicController.InstantiateAsync().Task;
            DontDestroyOnLoad(musicController);
        }

        private void InitializeHelpshift() {
            HelpshiftXInstallHelper instance = Instantiate(_helpshiftPrefab);
            DontDestroyOnLoad(instance.gameObject);
        }

        private async Task OpenMatchmakingDialogAsync() {
            if (_persistentWorld == null) {
                return;
            }

            try {
                await LobbyScreenHelper.OpenLobbyScreenAsync(
                    _persistentWorld,
                    _persistentEntityQueryCache,
                    _lobbyScreen
                );
            } catch (Exception e) {
                Log.Exception(e, this);
            }
        }

        private async Task InitializeNotificationsAsync() {
            try {
                NotificationsInitializer notificationsInitializer = new();
                await notificationsInitializer.InitAsync(token => { _pushNotificationsToken = token; }, _notificationsInitializationTimeoutMilliseconds);
            } catch (Exception e) {
                Log.Error("Failed to initialize Firebase notifications", this);
                Log.Exception(e, this);
            }
        }

        private static async Task InitializeFirebaseAsync() {
            FirebaseInitializer firebaseInitializer = new();
            await firebaseInitializer.InitAsync();
        }

        private void ConfigureAppSettings() {
            Screen.sleepTimeout = _preventSleep ? SleepTimeout.NeverSleep : SleepTimeout.SystemSetting;
        }

        private async Task<MapsCatalogScriptable> LoadAvailableMapsAsync() {
            return await _maps.LoadAssetAsync().Task;
        }

        private async Task BootstrapPersistentWorldAsync(
            LoadingScreenProgressAdapter loadingScreenProgressAdapter,
            float minLoadingStep,
            float maxLoadingStep
        ) {
            ContentItemsCatalogScriptable? persistentContentItemsVersions = await _persistentContentItemsVersionsReference.LoadAssetAsync().Task;
            loadingScreenProgressAdapter.SetProgress(ConvertProgress(0f), "Init persistent world");
            _persistentWorld = DefaultWorldInitialization.Initialize(PersistentWorldName);
            World.DefaultGameObjectInjectionWorld = _persistentWorld;

            _contentItemsCatalog = new ContentItemsCatalog(persistentContentItemsVersions);
            _persistentWorld.EntityManager.CreateSingleton(_contentItemsCatalog);

            int persistentModulesLength = PersistentModules.Length;
            IModule[] persistentModules = new IModule[persistentModulesLength];
            Array.Copy(PersistentModules, persistentModules, persistentModulesLength);

            int persistentConfigsLength = PersistentModuleConfigs.Length;
            IConfigSingleton[] persistentConfigs = new IConfigSingleton[persistentConfigsLength + 1];
            Array.Copy(PersistentModuleConfigs, persistentConfigs, persistentConfigsLength);
            persistentConfigs[persistentConfigsLength] = persistentContentItemsVersions;

            _persistentWorldBootstrap = new PersistentWorldBootstrap(
                _persistentWorld,
                persistentModules,
                persistentConfigs,
                _contentItemsCatalog
            );

            loadingScreenProgressAdapter.SetProgress(ConvertProgress(0.2f), "Load pre balancing modules");
            await ModulesSetupHelper.AddSystemsToWorldAndInitModulesAsync(_persistentWorld,
                PreBalancingLoadingPersistentModules);

            loadingScreenProgressAdapter.SetProgress(ConvertProgress(0.25f), "Load balancing");
            await _persistentWorldBootstrap.LoadBalancingDataAsync();
            await _persistentWorldBootstrap.InitAsync((stage, module) => Log.Debug($"Loading Module {module} {stage}"));
            loadingScreenProgressAdapter.SetProgress(ConvertProgress(0.50f), "User consent");
            _persistentWorldBootstrap.AquireUserConsent();
            loadingScreenProgressAdapter.SetProgress(ConvertProgress(0.75f), "Init persistent bootstrap");

            return;

            float ConvertProgress(float progress0To1) => (maxLoadingStep - minLoadingStep) * progress0To1 + minLoadingStep;
        }

        private void Track(string step) {
            _initialTracker.Track(
                new UberFunnelEvent {
                    Step = step,
                },
                _persistentWorld?.EntityManager
            );
        }
    }
}
