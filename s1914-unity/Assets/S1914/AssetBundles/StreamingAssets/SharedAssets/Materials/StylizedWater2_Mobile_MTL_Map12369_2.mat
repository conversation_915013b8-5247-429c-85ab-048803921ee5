%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: StylizedWater2_Mobile_MTL_Map12369_2
  m_Shader: {fileID: -6465566751694194690, guid: d7b0192b9bf19c949900035fa781fdc4,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ENVIRONMENTREFLECTIONS_OFF
  - _NORMALMAP
  - _RECEIVE_SHADOWS_OFF
  - _SMOOTH_INTERSECTION
  - _UNLIT
  m_InvalidKeywords:
  - ADVANCED_LIGHTING
  - _CROSSPAN_INTERSECTION
  - _DEPTHEXP_ON
  - _DEPTH_TEX
  - _RECEIVEDYNAMICEFFECTS_ON
  - _SIMPLE_LIGHTING
  - _WORLDSPACEUV_ON
  - _ZCLIP_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: b84e00389862c2f419c1f9d9180aa1fa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMapLarge:
        m_Texture: {fileID: 2800000, guid: b84e00389862c2f419c1f9d9180aa1fa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMapSlope:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CausticsTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DepthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FoamTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FoamTexDynamic:
        m_Texture: {fileID: 2800000, guid: 2e10c404ec8e1ff41bff06b82e5569df, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IntersectionNoise:
        m_Texture: {fileID: 2800000, guid: 79e9371a7786f4596b7fecccd406bfc9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normals:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _PlanarReflection:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _PlanarReflectionLeft:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shadermap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_1942CF3A: 1
    - Vector1_BE75C478: 32
    - Vector1_E23F9E57: 0.5
    - Vector1_E796673B: 10
    - _ADVANCED_LIGHTING: 1
    - _AdvancedLighting: 1
    - _Advanced_Lighting: 1
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AnimationSpeed: 1
    - _Blend: 0
    - _BumpScale: 1
    - _CROSSPAN_INTERSECTIONOn: 1
    - _CausticsBrightness: 4.83
    - _CausticsChromance: 1
    - _CausticsDistortion: 0.15
    - _CausticsOn: 0
    - _CausticsSpeed: 0.1
    - _CausticsTiling: 0.5
    - _ColorAbsorption: 0
    - _CrossPan_IntersectionOn: 0
    - _Cull: 0
    - _Cutoff: 0.5
    - _Depth: 5.01
    - _DepthExp: 1
    - _DepthHorizontal: 0.42
    - _DepthMode: 0
    - _DepthTexture: 1
    - _DepthVertical: 0.83
    - _DisableDepthTexture: 0
    - _DistanceNormalsOn: 0
    - _DistanceNormalsTiling: 0.1
    - _DstBlend: 0
    - _EdgeFade: 0
    - _EnvironmentReflections: 1
    - _EnvironmentReflectionsOn: 0
    - _FlatShadingOn: 0
    - _FlowSpeed: 1
    - _FoamBaseAmount: 1
    - _FoamClipping: 0.98
    - _FoamDistortion: 0.1
    - _FoamOn: 0
    - _FoamOpacity: 0.253
    - _FoamSize: 0.01
    - _FoamSpeed: 0.030000014
    - _FoamSpeedDynamic: 0.1
    - _FoamSubSpeed: -0.25
    - _FoamSubSpeedDynamic: -0.1
    - _FoamSubTiling: 0.5
    - _FoamSubTilingDynamic: 2
    - _FoamTiling: 0.049999908
    - _FoamTilingDynamic: 0.1
    - _FoamWaveAmount: 0.83
    - _FoamWaveMask: 0
    - _FoamWaveMaskExp: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _HorizonDistance: 0.7
    - _IntersectionClipping: 0.083
    - _IntersectionDistortion: 0.215
    - _IntersectionFalloff: 15.8
    - _IntersectionLength: 0.01
    - _IntersectionRippleDist: 10
    - _IntersectionRippleStrength: 0.038
    - _IntersectionSize: 0.944
    - _IntersectionSource: 0
    - _IntersectionSpeed: 0.005
    - _IntersectionStyle: 2
    - _IntersectionTiling: 0.01
    - _IntersectionWaveDist: 33.28
    - _LightingMode: 0
    - _LightingOn: 0
    - _Metallic: 0
    - _Metallicness: 0.1
    - _NORMALMAPOn: 1
    - _NormalMap: 1
    - _NormalMapOn: 1
    - _NormalSpeed: 1
    - _NormalStrength: 0.631
    - _NormalSubSpeed: -2
    - _NormalSubTiling: 0.2
    - _NormalTiling: 0.29999992
    - _OcclusionStrength: 1
    - _PlanarReflectionsEnabled: 0
    - _PlanarReflectionsParams: 0
    - _PointSpotLightReflectionDistortion: 0.5
    - _PointSpotLightReflectionExp: 64
    - _PointSpotLightReflectionSize: 0
    - _PointSpotLightReflectionStrength: 10
    - _QueueOffset: 0
    - _ReceiveDynamicEffects: 1
    - _ReceiveShadows: 0
    - _ReflectionBlur: 0
    - _ReflectionDistortion: 0.086
    - _ReflectionFresnel: 5
    - _ReflectionFresnelMode: 0
    - _ReflectionLighting: 0
    - _ReflectionStrength: 0.785
    - _Reflectivity: 1
    - _RefractionAmount: 0.05
    - _RefractionChromaticAberration: 1
    - _RefractionOn: 0
    - _RefractionStrength: 0.267
    - _RimRize: 1
    - _RimTiling: 0.025
    - _RiverModeOn: 0
    - _SHARP_INERSECTIONOn: 1
    - _ShadingMode: 0
    - _ShadowStrength: 1
    - _ShoreLineLength: 0
    - _ShoreLineWaveDistance: 23
    - _ShoreLineWaveStr: 1
    - _SimpleLighting: 1
    - _SlopeAngleFalloff: 25
    - _SlopeAngleThreshold: 15
    - _SlopeFoam: 1
    - _SlopeSpeed: 4
    - _SlopeStretching: 0.5
    - _SlopeThreshold: 0.25
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SparkleIntensity: 0
    - _SparkleSize: 0
    - _SpecularHighlights: 1
    - _SpecularReflectionsOn: 1
    - _Speed: 0.5
    - _SrcBlend: 1
    - _SunReflectionDistortion: 0.78
    - _SunReflectionPerturbance: 0.548
    - _SunReflectionSize: 0.916
    - _SunReflectionStrength: 0.1
    - _Surface: 0
    - _TEXTURE_INTERSECTIONOn: 0
    - _TessMax: 15
    - _TessMin: 0
    - _TessValue: 16
    - _Texture_IntersectionOn: 1
    - _Tiling: 0.5
    - _Translucency: 0
    - _TranslucencyCurvatureMask: 0
    - _TranslucencyExp: 4
    - _TranslucencyOn: 0
    - _TranslucencyReflectionMask: 0
    - _TranslucencyStrength: 1
    - _TranslucencyStrengthDirect: 0.05
    - _UnderwaterRefractionOffset: 0.2
    - _UnderwaterSurfaceSmoothness: 0.8
    - _VertexColorDepth: 0
    - _VertexColorFoam: 0
    - _VertexColorWaveFlattening: 0
    - _WaveCount: 1
    - _WaveDistance: 0.763
    - _WaveHeight: 0
    - _WaveNormalStr: 0.291
    - _WaveSpeed: 1
    - _WaveSteepness: 0
    - _WaveTint: -0.1
    - _WavesOn: 0
    - _WorkflowMode: 1
    - _WorldSpaceUV: 1
    - _ZClip: 1
    - _ZWrite: 0
    m_Colors:
    - _BaseColor: {r: 0.07450981, g: 0.34509805, b: 0.38679248, a: 1}
    - _Color: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _DepthMapBounds: {r: -402.3, g: -459.43, b: 0.0012693577, a: 0}
    - _Direction: {r: 1, g: 1, b: 0, a: 0}
    - _DistanceNormalsFadeDist: {r: 300, g: 0.25, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FoamColor: {r: 1, g: 1, b: 1, a: 0.09803922}
    - _FoamTiling: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _HorizonColor: {r: 0.00000007410327, g: 0.00001330085, b: 0.000023268429, a: 1}
    - _IntersectionColor: {r: 3.031433, g: 3.031433, b: 3.031433, a: 1}
    - _NormalTiling: {r: 0.07, g: 0.07, b: 0, a: 0}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShallowColor: {r: 0.019607844, g: 0.10980392, b: 0.16470589, a: 1}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _WaterColor: {r: 0.21176466, g: 0.6745098, b: 1, a: 1}
    - _WaterShallowColor: {r: 0, g: 0.9394503, b: 1, a: 1}
    - _WaveDirection: {r: 1, g: 1, b: 1, a: 1}
    - _WaveFadeDistance: {r: 150, g: 300, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &6498140463000427223
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 1
