%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ca35e0ea8d3646f4aff2549977659f31, type: 3}
  m_Name: CrossroadsOfHistoryPlayerRelationshipsScriptable
  m_EditorClassIdentifier: 
  Values:
  - Id: 1
    Data:
      Relationships: []
  - Id: 2
    Data:
      Relationships: []
  - Id: 3
    Data:
      Relationships: []
  - Id: 4
    Data:
      Relationships: []
  - Id: 5
    Data:
      Relationships: []
  - Id: 6
    Data:
      Relationships: []
  - Id: 7
    Data:
      Relationships: []
  - Id: 8
    Data:
      Relationships: []
  - Id: 9
    Data:
      Relationships: []
  - Id: 10
    Data:
      Relationships: []
  - Id: 11
    Data:
      Relationships: []
  - Id: 12
    Data:
      Relationships: []
  - Id: 13
    Data:
      Relationships: []
  - Id: 14
    Data:
      Relationships: []
  - Id: 15
    Data:
      Relationships: []
  - Id: 16
    Data:
      Relationships: []
  - Id: 17
    Data:
      Relationships: []
  - Id: 18
    Data:
      Relationships: []
  - Id: 19
    Data:
      Relationships: []
  - Id: 20
    Data:
      Relationships: []
  - Id: 21
    Data:
      Relationships: []
  - Id: 22
    Data:
      Relationships: []
  - Id: 23
    Data:
      Relationships: []
  - Id: 24
    Data:
      Relationships: []
  - Id: 25
    Data:
      Relationships: []
  - Id: 26
    Data:
      Relationships: []
  - Id: 27
    Data:
      Relationships: []
  - Id: 28
    Data:
      Relationships: []
  - Id: 29
    Data:
      Relationships: []
  - Id: 30
    Data:
      Relationships: []
  - Id: 31
    Data:
      Relationships: []
  - Id: 32
    Data:
      Relationships: []
  - Id: 33
    Data:
      Relationships: []
  - Id: 34
    Data:
      Relationships: []
  - Id: 35
    Data:
      Relationships: []
  - Id: 36
    Data:
      Relationships: []
  - Id: 37
    Data:
      Relationships: []
  - Id: 38
    Data:
      Relationships: []
  - Id: 39
    Data:
      Relationships: []
  - Id: 40
    Data:
      Relationships: []
  - Id: 41
    Data:
      Relationships: []
  - Id: 42
    Data:
      Relationships: []
  - Id: 43
    Data:
      Relationships: []
  - Id: 44
    Data:
      Relationships: []
  - Id: 45
    Data:
      Relationships: []
