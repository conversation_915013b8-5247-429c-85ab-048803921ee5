%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 95f111f5f9acd47119aacc5c48ce2108, type: 3}
  m_Name: _SwiftStrikeBalancingDataImporter
  m_EditorClassIdentifier: 
  _csvImportFile: {fileID: 4900000, guid: 3c27750042fa34d139de84d814bc5794, type: 3}
  _skipImportOnValidation: 1
  Culture: en-US
  _reminderDialogMessage: 
  _target: {fileID: 11400000, guid: 6b4640db18ad1465381d137e622779f2, type: 2}
  _importers:
  - _target: {fileID: 11400000, guid: 12776a24e02bc4e56903b332177ecdb3, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016642
    Properties:
    - FieldName: Productions
      ColumnNameRegularExpression:
      - '\d+: initial .*'
      Deserializer: {fileID: 11400000, guid: 4495cd133df704826ade3172ad7e57ea, type: 2}
  - _target: {fileID: 11400000, guid: 3e20711075c6c4f13a2edec78fd232b3, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016645
    Properties:
    - FieldName: Priority
      ColumnNameRegularExpression:
      - tutorialPrio
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  - _target: {fileID: 11400000, guid: 8d84c00d099a24f9d9e0aa6bf74790a3, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016648
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - capitalId
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  - _target: {fileID: 11400000, guid: ac9a972899a0b41709f09bbb87079c76, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016656
    Properties: []
  - _target: {fileID: 11400000, guid: cb49451a7128e418bbc8596efbc59533, type: 2}
    ShouldCreateComponent:
      rid: 2914009399884513281
    Properties:
    - FieldName: Relationships
      ColumnNameRegularExpression:
      - initialRelations$
      Deserializer: {fileID: 11400000, guid: 5b80061fc0eff49b2aeba3f7badf3eaf, type: 2}
  - _target: {fileID: 11400000, guid: 6576c7d0f2b6340669e533c7f8d656ab, type: 2}
    ShouldCreateComponent:
      rid: 5976901895355629568
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - nationDifficulty
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  - _target: {fileID: 11400000, guid: 7bb40edf31d0e4c3eb1fb311bafca696, type: 2}
    ShouldCreateComponent:
      rid: 4264081218386788355
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - ^countryKey$
      Deserializer: {fileID: 11400000, guid: a7f288431e1cd6a4a9c8d4f6dfd78d0e, type: 2}
  - _target: {fileID: 11400000, guid: b94666b3ca6a84b4b843abe263baf2a6, type: 2}
    ShouldCreateComponent:
      rid: 1575986436218290178
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - ^flag$
      Deserializer: {fileID: 11400000, guid: 3373c97cae099b8499c0d3fead7b0598, type: 2}
  _onPostImport:
    m_PersistentCalls:
      m_Calls: []
  _enableValidation: 0
  references:
    version: 2
    RefIds:
    - rid: 1575986436218290178
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009309724016642
      type: {class: OnlyNonEmptyValuesAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - '\d+: initial .*'
    - rid: 2914009309724016645
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009309724016648
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009309724016656
      type: {class: OnlyNonEmptyNonZeroValuesAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - aiPlayer
    - rid: 2914009399884513281
      type: {class: OnlyNonEmptyValuesAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - initialRelations$
    - rid: 4264081218386788355
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 5976901895355629568
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
