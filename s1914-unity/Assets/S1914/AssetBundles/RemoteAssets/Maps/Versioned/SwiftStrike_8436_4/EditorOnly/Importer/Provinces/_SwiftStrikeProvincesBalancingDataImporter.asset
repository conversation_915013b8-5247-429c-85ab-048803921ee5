%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 95f111f5f9acd47119aacc5c48ce2108, type: 3}
  m_Name: _SwiftStrikeProvincesBalancingDataImporter
  m_EditorClassIdentifier: 
  _csvImportFile: {fileID: 4900000, guid: f603061217201453087d9c2d8e36c6f5, type: 3}
  _skipImportOnValidation: 1
  Culture: en-US
  _reminderDialogMessage: 
  _target: {fileID: 11400000, guid: 5ab2b427fb79c461ea12f85ba35d415d, type: 2}
  _importers:
  - _target: {fileID: 11400000, guid: 930211c0d3e834056bc629814b8abe07, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016649
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - country$
      Deserializer: {fileID: 11400000, guid: d6d40a5289ea74081ab99483b9f5191b, type: 2}
  - _target: {fileID: 11400000, guid: c7516eb7132884f4e95fb989c74e2fca, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016651
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - .*\(\d+\).*
      Deserializer: {fileID: 11400000, guid: 5c41c74ebff8d4145a32d42977c246bf, type: 2}
  - _target: {fileID: 11400000, guid: 1b402eeacc2544903a1236ecacb1977f, type: 2}
    ShouldCreateComponent:
      rid: 2914009309724016653
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - \d+:.*
      Deserializer: {fileID: 11400000, guid: 4495cd133df704826ade3172ad7e57ea, type: 2}
  - _target: {fileID: 11400000, guid: 8eca33e878e0744fb91d38ec8285ad21, type: 2}
    ShouldCreateComponent:
      rid: 2914009331867844609
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - country$
      Deserializer: {fileID: 11400000, guid: d6d40a5289ea74081ab99483b9f5191b, type: 2}
  - _target: {fileID: 11400000, guid: 03b3857e9d1b74fe3aa62008ea1f2cee, type: 2}
    ShouldCreateComponent:
      rid: 2914009335105323008
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - victoryPoints
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  - _target: {fileID: 11400000, guid: 1c76b703a2e5a497cb8f132f6e0be0e9, type: 2}
    ShouldCreateComponent:
      rid: 2914009407360073728
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - resource|production|money|manpower
      Deserializer: {fileID: 11400000, guid: e51e86e6ed9f14cbc978ecfc1120719c, type: 2}
  - _target: {fileID: 11400000, guid: 3bb445b5da95a48ceab0801a257d838e, type: 2}
    ShouldCreateComponent:
      rid: 4264081218386788360
    Properties:
    - FieldName: CityAreaPresets
      ColumnNameRegularExpression:
      - ^cityPreset$
      Deserializer: {fileID: 11400000, guid: 048f4cd00582a744bbb30b11a3615897, type: 2}
  - _target: {fileID: 11400000, guid: ddd251adfb01447e38507661da9f59e3, type: 2}
    ShouldCreateComponent:
      rid: 4264081218386788356
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - ^provinceKey$
      Deserializer: {fileID: 11400000, guid: a7f288431e1cd6a4a9c8d4f6dfd78d0e, type: 2}
  - _target: {fileID: 11400000, guid: 71e7aa1896fbd4079930da8180151e40, type: 2}
    ShouldCreateComponent:
      rid: 6434528212840349696
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - initialUpgradeGrounds
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  - _target: {fileID: 11400000, guid: b1aef18ba62d24ad1b72e0d7b669e332, type: 2}
    ShouldCreateComponent:
      rid: 6434528212840349697
    Properties:
    - FieldName: Value
      ColumnNameRegularExpression:
      - maxUpgradeGrounds
      Deserializer: {fileID: 11400000, guid: 029a1561a1ff04c5d9be9bccdb3c6e2c, type: 2}
  _onPostImport:
    m_PersistentCalls:
      m_Calls: []
  _enableValidation: 0
  references:
    version: 2
    RefIds:
    - rid: 2914009309724016649
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009309724016651
      type: {class: OnlyNonEmptyValuesAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - .*\(\d+\).*
    - rid: 2914009309724016653
      type: {class: OnlyNonEmptyValuesAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - \d+:.*
    - rid: 2914009331867844609
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009335105323008
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 2914009407360073728
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 4264081218386788356
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 4264081218386788360
      type: {class: OnlyNonEmptyStringsAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data:
        _headersRegex:
        - ^cityPreset$
    - rid: 6434528212840349696
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
    - rid: 6434528212840349697
      type: {class: AlwaysAllowComponentCreation, ns: Stratkit.Properties.Loader,
        asm: Stratkit.Csv.Editor}
      data: 
