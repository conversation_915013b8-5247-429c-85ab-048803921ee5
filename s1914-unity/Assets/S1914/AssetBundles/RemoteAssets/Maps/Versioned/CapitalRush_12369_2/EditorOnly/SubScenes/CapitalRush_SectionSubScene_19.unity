%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!43 &6127480
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &50544785
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.8000488}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &164303870
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 164303874}
  - component: {fileID: 164303873}
  - component: {fileID: 164303872}
  - component: {fileID: 164303871}
  m_Layer: 0
  m_Name: SectionMeta_134447538
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &164303871
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 164303870}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &164303872
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 164303870}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 2
--- !u!114 &164303873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 164303870}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134447538
--- !u!4 &164303874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 164303870}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &178374592
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &187466130
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &215403610
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.8000488}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &241982453
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &293870337
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &411288739
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &417848313
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &422478898
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &467887920
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &489942920
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &496689454
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &506634996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 506635000}
  - component: {fileID: 506634999}
  - component: {fileID: 506634998}
  - component: {fileID: 506634997}
  m_Layer: 0
  m_Name: SectionMeta_134381860
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &506634997
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506634996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &506634998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506634996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 4
--- !u!114 &506634999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506634996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134381860
--- !u!4 &506635000
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506634996}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &513513182
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &519055662
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519055666}
  - component: {fileID: 519055665}
  - component: {fileID: 519055664}
  - component: {fileID: 519055663}
  m_Layer: 0
  m_Name: SectionMeta_134513217
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &519055663
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519055662}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &519055664
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519055662}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 1
--- !u!114 &519055665
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519055662}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134513217
--- !u!4 &519055666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519055662}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &530199466
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &541715775
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &565589188
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &583939991
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 583939995}
  - component: {fileID: 583939994}
  - component: {fileID: 583939993}
  - component: {fileID: 583939992}
  m_Layer: 0
  m_Name: SectionMeta_134316184
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &583939992
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 583939991}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &583939993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 583939991}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 8
--- !u!114 &583939994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 583939991}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134316184
--- !u!4 &583939995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 583939991}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &653696209
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &719472078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 719472080}
  - component: {fileID: 719472079}
  m_Layer: 0
  m_Name: RoadRendererAuthor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &719472079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 719472078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac059752b3a0463ea041cd84d57ab5d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  All:
  - B:
      MeshList:
      - {fileID: 1097599817}
      - {fileID: 50544785}
      - {fileID: 1473712819}
      - {fileID: 1279794932}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134644289
    O:
    - E: 0
      A: 0
      P:
        x: 5400
        y: 0
        z: 944
      L: -1
    - E: 1
      A: 0
      P:
        x: 5445
        y: 0
        z: 1034
      L: -1
    - E: 2
      A: 0
      P:
        x: 5481
        y: 0
        z: 961
      L: -1
    - E: 3
      A: 0
      P:
        x: 5517
        y: 0
        z: 926
      L: -1
    S:
    - P:
        x: 5358.6455
        y: 995.7293
        z: 5443.3545
        w: 1033.2706
      M: 1
      O: 0
      E:
        x: 1
        y: 6.432961e-10
        z: 1
        w: 6.432961e-10
      L: -1
    - P:
        x: 5400.9985
        y: 942.5023
        z: 5435.0015
        w: 891.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
    - P:
        x: 5400.805
        y: 945.61
        z: 5444.195
        w: 1032.39
      M: 1
      O: 0
      E:
        x: 1
        y: 4.2235607e-10
        z: 1
        w: 4.2235607e-10
      L: -1
    - P:
        x: 5401.7617
        y: 944.36975
        z: 5479.2383
        w: 960.63025
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 7.5292117e-10
        z: 0.99999994
        w: 7.5292117e-10
      L: -1
    - P:
        x: 5445.796
        y: 1032.3856
        z: 5480.204
        w: 962.6144
      M: 1
      O: 0
      E:
        x: 0.99999976
        y: -0.0000000015323657
        z: 0.99999976
        w: -0.0000000015323657
      L: -1
    - P:
        x: 5446.6694
        y: 1034.6729
        z: 5510.3306
        w: 1060.3271
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000017368366
        z: 1.0000001
        w: 0.0000000017368366
      L: -1
    - P:
        x: 5436.9634
        y: 891.5203
        z: 5480.0366
        w: 959.4797
      M: 1
      O: 0
      E:
        x: 1
        y: 8.334005e-10
        z: 1
        w: 8.334005e-10
      L: -1
    - P:
        x: 5481.151
        y: 959.20636
        z: 5489.849
        w: 855.79364
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
    - P:
        x: 5481.533
        y: 962.7193
        z: 5511.467
        w: 1059.2808
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 6.08017e-10
        z: 0.99999994
        w: 6.08017e-10
      L: -1
    - P:
        x: 5482.2905
        y: 959.74524
        z: 5515.7095
        w: 927.25476
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0.000000002557611
        z: 0.99999994
        w: 0.000000002557611
      L: -1
  - B:
      MeshList:
      - {fileID: 513513182}
      - {fileID: 1917110416}
      - {fileID: 1221229007}
      - {fileID: 1175618147}
      - {fileID: 241982453}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134578753
    O:
    - E: 0
      A: 0
      P:
        x: 5404
        y: 0
        z: 809
      L: -1
    - E: 1
      A: 0
      P:
        x: 5436
        y: 0
        z: 890
      L: -1
    - E: 2
      A: 0
      P:
        x: 5440
        y: 0
        z: 755
      L: -1
    - E: 3
      A: 0
      P:
        x: 5490
        y: 0
        z: 854
      L: -1
    - E: 4
      A: 0
      P:
        x: 5494
        y: 0
        z: 769
      L: -1
    S:
    - P:
        x: 5404.661
        y: 810.6741
        z: 5435.339
        w: 888.3259
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -1.5616464e-10
        z: 0.99999994
        w: -1.5616464e-10
      L: -1
    - P:
        x: 5404.9985
        y: 807.5023
        z: 5439.0015
        w: 756.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
    - P:
        x: 5405.5947
        y: 809.83453
        z: 5488.4053
        w: 853.16547
      M: 1
      O: 0
      E:
        x: 1
        y: 3.1887082e-10
        z: 1
        w: 3.1887082e-10
      L: -1
    - P:
        x: 5437.4976
        y: 889.0015
        z: 5488.5024
        w: 854.9985
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0.0000000038893653
        z: 1.0000002
        w: 0.0000000038893653
      L: -1
    - P:
        x: 5440.8115
        y: 756.6067
        z: 5489.1885
        w: 852.3933
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -2.4300686e-10
        z: 0.9999999
        w: -2.4300686e-10
      L: -1
    - P:
        x: 5441.742
        y: 755.4517
        z: 5492.258
        w: 768.5483
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0
        z: 1.0000001
        w: 0
      L: -1
    - P:
        x: 5490.0845
        y: 852.20197
        z: 5493.9155
        w: 770.79803
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -9.142487e-11
        z: 0.99999964
        w: -9.142487e-11
      L: -1
    - P:
        x: 5490.632
        y: 855.68536
        z: 5516.368
        w: 924.31464
      M: 1
      O: 0
      E:
        x: 1
        y: -4.066011e-10
        z: 1
        w: -4.066011e-10
      L: -1
  - B:
      MeshList:
      - {fileID: 215403610}
      - {fileID: 1236860363}
      - {fileID: 1375946421}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134709825
    O:
    - E: 0
      A: 0
      P:
        x: 5440
        y: 0
        z: 1169
      L: -1
    - E: 1
      A: 0
      P:
        x: 5476
        y: 0
        z: 1115
      L: -1
    - E: 2
      A: 0
      P:
        x: 5512
        y: 0
        z: 1061
      L: -1
    S:
    - P:
        x: 5404.9985
        y: 1221.5023
        z: 5439.0015
        w: 1170.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
    - P:
        x: 5395.805
        y: 1080.61
        z: 5439.195
        w: 1167.39
      M: 1
      O: 0
      E:
        x: 1
        y: 4.2235607e-10
        z: 1
        w: 4.2235607e-10
      L: -1
    - P:
        x: 5396.338
        y: 1077.7959
        z: 5443.662
        w: 1035.2041
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
    - P:
        x: 5396.645
        y: 1079.7311
        z: 5474.355
        w: 1114.2689
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000021027184
        z: 1.0000001
        w: 0.0000000021027184
      L: -1
    - P:
        x: 5440.9985
        y: 1167.5023
        z: 5475.0015
        w: 1116.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
    - P:
        x: 5441.026
        y: 1170.4791
        z: 5478.676
        w: 1224.7654
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 5.638872e-10
        z: 1.0000001
        w: -0.0000003163407
      L: -1
    - P:
        x: 5445.6436
        y: 1035.681
        z: 5475.3564
        w: 1113.319
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -1.12032814e-10
        z: 0.99999994
        w: -1.12032814e-10
      L: -1
    - P:
        x: 5476.1084
        y: 1116.7968
        z: 5482.517
        w: 1223
      M: 1
      O: 0
      E:
        x: 1
        y: 1.16391875e-10
        z: 1
        w: -0.00000015132174
      L: -1
    - P:
        x: 5476.9985
        y: 1113.5023
        z: 5511.0015
        w: 1062.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
  - B:
      MeshList:
      - {fileID: 1695344267}
      - {fileID: 1416772405}
      - {fileID: 1357474095}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134513217
    O:
    - E: 0
      A: 0
      P:
        x: 5445
        y: 0
        z: 620
      L: -1
    - E: 1
      A: 0
      P:
        x: 5476
        y: 0
        z: 701
      L: -1
    - E: 2
      A: 0
      P:
        x: 5512
        y: 0
        z: 611
      L: -1
    S:
    - P:
        x: 5358.6455
        y: 581.7293
        z: 5443.3545
        w: 619.2707
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000012865918
        z: 1.0000001
        w: 0.0000000012865918
      L: -1
    - P:
        x: 5395.805
        y: 666.61
        z: 5439.195
        w: 753.39
      M: 1
      O: 0
      E:
        x: 1
        y: 4.2235607e-10
        z: 1
        w: 4.2235607e-10
      L: -1
    - P:
        x: 5396.338
        y: 663.79584
        z: 5443.662
        w: 621.20416
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0.0000000018723538
        z: 0.99999994
        w: 0.0000000018723538
      L: -1
    - P:
        x: 5396.645
        y: 665.7311
        z: 5474.355
        w: 700.2689
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000021027184
        z: 1.0000001
        w: 0.0000000021027184
      L: -1
    - P:
        x: 5440.9985
        y: 753.5023
        z: 5475.0015
        w: 702.4977
      M: 1
      O: 0
      E:
        x: 0.9999997
        y: 0
        z: 0.9999997
        w: 0
      L: -1
    - P:
        x: 5445.6436
        y: 621.6811
        z: 5475.3564
        w: 699.3189
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -3.8091208e-10
        z: 0.99999994
        w: -3.8091208e-10
      L: -1
    - P:
        x: 5446.784
        y: 619.7604
        z: 5510.216
        w: 611.2396
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0.0000000037252104
        z: 1.0000002
        w: 0.0000000037252104
      L: -1
    - P:
        x: 5476.4604
        y: 702.74005
        z: 5493.5396
        w: 767.25995
      M: 1
      O: 0
      E:
        x: 1
        y: 8.3724255e-10
        z: 1
        w: 8.3724255e-10
      L: -1
    - P:
        x: 5476.6685
        y: 699.32874
        z: 5511.3315
        w: 612.67126
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
  - B:
      MeshList:
      - {fileID: 1365040283}
      - {fileID: 1125013544}
      - {fileID: 178374592}
      - {fileID: 1156098964}
      - {fileID: 866397326}
      - {fileID: 1873074244}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134513074
    O:
    - E: 0
      A: 0
      P:
        x: 5400
        y: 0
        z: 944
      L: -1
    - E: 1
      A: 0
      P:
        x: 5404
        y: 0
        z: 809
      L: -1
    - E: 2
      A: 0
      P:
        x: 5436
        y: 0
        z: 890
      L: -1
    - E: 3
      A: 0
      P:
        x: 5481
        y: 0
        z: 961
      L: -1
    - E: 4
      A: 0
      P:
        x: 5490
        y: 0
        z: 854
      L: -1
    - E: 5
      A: 0
      P:
        x: 5517
        y: 0
        z: 926
      L: -1
    S:
    - P:
        x: 5402.496
        y: 940.2558
        z: 5433.504
        w: 893.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5402.0127
        y: 948.0249
        z: 5442.9873
        w: 1029.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5404.404
        y: 944.9243
        z: 5476.596
        w: 960.0757
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000024240963
        z: 1.0000001
        w: 0.0000000024240963
      L: -1
    - P:
        x: 5405.6533
        y: 813.18524
        z: 5434.3467
        w: 885.81476
      M: 1
      O: 0
      E:
        x: 1
        y: -4.5318693e-10
        z: 1
        w: -4.5318693e-10
      L: -1
    - P:
        x: 5407.9873
        y: 811.0863
        z: 5486.0127
        w: 851.9137
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000016921302
        z: 1
        w: 0.0000000016921302
      L: -1
    - P:
        x: 5446.99
        y: 1029.9641
        z: 5479.01
        w: 965.0359
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000016466685
        z: 0.9999999
        w: -0.0000000016466685
      L: -1
    - P:
        x: 5438.409
        y: 893.8009
        z: 5478.591
        w: 957.1991
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0.000000002084515
        z: 0.99999994
        w: 0.000000002084515
      L: -1
    - P:
        x: 5439.744
        y: 887.50385
        z: 5486.256
        w: 856.49615
      M: 1
      O: 0
      E:
        x: 1
        y: -0.000000002132543
        z: 1
        w: -0.000000002132543
      L: -1
    - P:
        x: 5442.029
        y: 759.0168
        z: 5487.971
        w: 849.9832
      M: 1
      O: 0
      E:
        x: 1
        y: -0.0000000011697536
        z: 1
        w: -0.0000000011697536
      L: -1
    - P:
        x: 5481.377
        y: 956.5158
        z: 5489.623
        w: 858.4842
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: 0
        z: 0.9999999
        w: 0
      L: -1
    - P:
        x: 5484.2266
        y: 957.86316
        z: 5513.7734
        w: 929.13684
      M: 1
      O: 0
      E:
        x: 1.0000005
        y: 0
        z: 1.0000005
        w: 0
      L: -1
    - P:
        x: 5490.2114
        y: 849.505
        z: 5493.7886
        w: 773.495
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: 9.7912685e-11
        z: 0.99999964
        w: 9.7912685e-11
      L: -1
    - P:
        x: 5491.58
        y: 858.2135
        z: 5515.42
        w: 921.7865
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 2.7433805e-11
        z: 0.99999994
        w: 2.7433805e-11
      L: -1
  - B:
      MeshList:
      - {fileID: 530199466}
      - {fileID: 1072237124}
      - {fileID: 1529933195}
      - {fileID: 792456875}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134578610
    O:
    - E: 0
      A: 0
      P:
        x: 5440
        y: 0
        z: 1169
      L: -1
    - E: 1
      A: 0
      P:
        x: 5445
        y: 0
        z: 1034
      L: -1
    - E: 2
      A: 0
      P:
        x: 5476
        y: 0
        z: 1115
      L: -1
    - E: 3
      A: 0
      P:
        x: 5512
        y: 0
        z: 1061
      L: -1
    S:
    - P:
        x: 5361.1143
        y: 996.8233
        z: 5440.8857
        w: 1032.1768
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000027324487
        z: 1
        w: 0.0000000027324487
      L: -1
    - P:
        x: 5406.496
        y: 1219.2557
        z: 5437.504
        w: 1172.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5397.0127
        y: 1083.0249
        z: 5437.9873
        w: 1164.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5398.3447
        y: 1075.9896
        z: 5441.6553
        w: 1037.0104
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
    - P:
        x: 5399.1123
        y: 1080.8276
        z: 5471.8877
        w: 1113.1724
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000014968627
        z: 0.9999999
        w: -0.0000000014968627
      L: -1
    - P:
        x: 5442.496
        y: 1165.2557
        z: 5473.504
        w: 1118.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5442.5645
        y: 1172.6978
        z: 5478.676
        w: 1224.7654
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000014109935
        z: 1
        w: -0.000000082660705
      L: -1
    - P:
        x: 5446.6084
        y: 1038.2028
        z: 5474.3916
        w: 1110.7972
      M: 1
      O: 0
      E:
        x: 1
        y: 6.4700606e-10
        z: 1
        w: 6.4700606e-10
      L: -1
    - P:
        x: 5449.174
        y: 1035.682
        z: 5507.826
        w: 1059.318
      M: 1
      O: 0
      E:
        x: 1
        y: -9.425786e-10
        z: 1
        w: -9.425786e-10
      L: -1
    - P:
        x: 5476.271
        y: 1119.4918
        z: 5482.517
        w: 1223
      M: 1
      O: 0
      E:
        x: 1
        y: 6.413165e-11
        z: 1.0000001
        w: 0.00000013159843
      L: -1
    - P:
        x: 5478.496
        y: 1111.2557
        z: 5509.504
        w: 1064.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5482.3325
        y: 965.2982
        z: 5510.6675
        w: 1056.7018
      M: 1
      O: 0
      E:
        x: 1
        y: -6.812557e-11
        z: 1
        w: -6.812557e-11
      L: -1
  - B:
      MeshList:
      - {fileID: 1614286190}
      - {fileID: 1957822952}
      - {fileID: 1689393582}
      - {fileID: 1989622641}
      - {fileID: 1207594140}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134447538
    O:
    - E: 0
      A: 0
      P:
        x: 5440
        y: 0
        z: 755
      L: -1
    - E: 1
      A: 0
      P:
        x: 5445
        y: 0
        z: 620
      L: -1
    - E: 2
      A: 0
      P:
        x: 5476
        y: 0
        z: 701
      L: -1
    - E: 3
      A: 0
      P:
        x: 5494
        y: 0
        z: 769
      L: -1
    - E: 4
      A: 0
      P:
        x: 5512
        y: 0
        z: 611
      L: -1
    S:
    - P:
        x: 5361.1143
        y: 582.8233
        z: 5440.8857
        w: 618.1767
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 6.8311234e-10
        z: 0.99999994
        w: 6.8311234e-10
      L: -1
    - P:
        x: 5406.496
        y: 805.2558
        z: 5437.504
        w: 758.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5397.0127
        y: 669.0249
        z: 5437.9873
        w: 750.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5398.3447
        y: 661.9897
        z: 5441.6553
        w: 623.0103
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0.0000000020458688
        z: 1.0000002
        w: 0.0000000020458688
      L: -1
    - P:
        x: 5399.1123
        y: 666.82764
        z: 5471.8877
        w: 699.17236
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000014968627
        z: 0.9999999
        w: -0.0000000014968627
      L: -1
    - P:
        x: 5442.496
        y: 751.2558
        z: 5473.504
        w: 704.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5444.356
        y: 756.12933
        z: 5489.644
        w: 767.87067
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000012740021
        z: 1
        w: 0.0000000012740021
      L: -1
    - P:
        x: 5446.6084
        y: 624.2027
        z: 5474.3916
        w: 696.7973
      M: 1
      O: 0
      E:
        x: 1
        y: 0.000000001222121
        z: 1
        w: 0.000000001222121
      L: -1
    - P:
        x: 5449.46
        y: 619.4009
        z: 5507.54
        w: 611.5991
      M: 1
      O: 0
      E:
        x: 0.99999976
        y: -0.0000000040684562
        z: 0.99999976
        w: -0.0000000040684562
      L: -1
    - P:
        x: 5477.1514
        y: 705.35016
        z: 5492.8486
        w: 764.64984
      M: 1
      O: 0
      E:
        x: 1
        y: 6.832094e-10
        z: 1
        w: 6.832094e-10
      L: -1
    - P:
        x: 5477.6714
        y: 696.82184
        z: 5510.3286
        w: 615.17816
      M: 1
      O: 0
      E:
        x: 1.0000005
        y: 0.0000000013556857
        z: 1.0000005
        w: 0.0000000013556857
      L: -1
  - B:
      MeshList:
      - {fileID: 565589188}
      - {fileID: 1810840326}
      - {fileID: 293870337}
      - {fileID: 924460552}
      - {fileID: 496689454}
      - {fileID: 847844100}
      - {fileID: 417848313}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134447396
    O:
    - E: 0
      A: 0
      P:
        x: 5400
        y: 0
        z: 944
      L: -1
    - E: 1
      A: 0
      P:
        x: 5440
        y: 0
        z: 1169
      L: -1
    - E: 2
      A: 0
      P:
        x: 5445
        y: 0
        z: 1034
      L: -1
    - E: 3
      A: 0
      P:
        x: 5476
        y: 0
        z: 1115
      L: -1
    - E: 4
      A: 0
      P:
        x: 5481
        y: 0
        z: 961
      L: -1
    - E: 5
      A: 0
      P:
        x: 5512
        y: 0
        z: 1061
      L: -1
    - E: 6
      A: 0
      P:
        x: 5517
        y: 0
        z: 926
      L: -1
    S:
    - P:
        x: 5361.937
        y: 997.1879
        z: 5440.063
        w: 1031.812
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000027900016
        z: 0.9999999
        w: -0.0000000027900016
      L: -1
    - P:
        x: 5406.9956
        y: 1218.507
        z: 5437.0044
        w: 1173.493
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5397.415
        y: 1083.83
        z: 5437.585
        w: 1164.17
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: 1.244212e-10
        z: 0.9999999
        w: 1.244212e-10
      L: -1
    - P:
        x: 5399.0137
        y: 1075.3876
        z: 5440.9863
        w: 1037.6124
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0
        z: 1.0000001
        w: 0
      L: -1
    - P:
        x: 5399.9346
        y: 1081.1931
        z: 5471.0654
        w: 1112.8069
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 7.6573514e-10
        z: 0.99999994
        w: 7.6573514e-10
      L: -1
    - P:
        x: 5402.9956
        y: 939.50696
        z: 5433.0044
        w: 894.49304
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5402.415
        y: 948.8299
        z: 5442.585
        w: 1029.17
      M: 1
      O: 0
      E:
        x: 1
        y: -0.0000000012442111
        z: 1
        w: -0.0000000012442111
      L: -1
    - P:
        x: 5405.2847
        y: 945.1092
        z: 5475.7153
        w: 959.8908
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
    - P:
        x: 5442.9956
        y: 1164.507
        z: 5473.0044
        w: 1119.493
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5443.0776
        y: 1173.4373
        z: 5478.676
        w: 1224.7654
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -0.0000000017891577
        z: 0.99999994
        w: -0.0000000627398
      L: -1
    - P:
        x: 5446.93
        y: 1039.0432
        z: 5474.07
        w: 1109.9568
      M: 1
      O: 0
      E:
        x: 1
        y: -9.8124925e-11
        z: 1
        w: -9.8124925e-11
      L: -1
    - P:
        x: 5447.388
        y: 1029.1569
        z: 5478.612
        w: 965.8431
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -8.4432694e-10
        z: 0.99999994
        w: -8.4432694e-10
      L: -1
    - P:
        x: 5450.009
        y: 1036.0184
        z: 5506.991
        w: 1058.9816
      M: 1
      O: 0
      E:
        x: 0.9999998
        y: -0.0000000019404023
        z: 0.9999998
        w: -0.0000000019404023
      L: -1
    - P:
        x: 5438.8906
        y: 894.56104
        z: 5478.1094
        w: 956.43896
      M: 1
      O: 0
      E:
        x: 1
        y: -6.102054e-10
        z: 1
        w: -6.102054e-10
      L: -1
    - P:
        x: 5476.325
        y: 1120.3901
        z: 5482.517
        w: 1223
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: 1.0553327e-10
        z: 1.0000001
        w: 0.00000011969661
      L: -1
    - P:
        x: 5478.9956
        y: 1110.507
        z: 5509.0044
        w: 1065.493
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5481.4526
        y: 955.619
        z: 5489.5474
        w: 859.381
      M: 1
      O: 0
      E:
        x: 1
        y: -1.5429168e-10
        z: 1
        w: -1.5429168e-10
      L: -1
    - P:
        x: 5482.599
        y: 966.15784
        z: 5510.401
        w: 1055.8422
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 6.546406e-10
        z: 0.99999994
        w: 6.546406e-10
      L: -1
    - P:
        x: 5484.8716
        y: 957.2358
        z: 5513.1284
        w: 929.7642
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
  - B:
      MeshList:
      - {fileID: 1670985339}
      - {fileID: 1706643635}
      - {fileID: 1975250942}
      - {fileID: 1977732245}
      - {fileID: 411288739}
      - {fileID: 2009918425}
      - {fileID: 467887920}
      - {fileID: 422478898}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134381860
    O:
    - E: 0
      A: 0
      P:
        x: 5404
        y: 0
        z: 809
      L: -1
    - E: 1
      A: 0
      P:
        x: 5436
        y: 0
        z: 890
      L: -1
    - E: 2
      A: 0
      P:
        x: 5440
        y: 0
        z: 755
      L: -1
    - E: 3
      A: 0
      P:
        x: 5445
        y: 0
        z: 620
      L: -1
    - E: 4
      A: 0
      P:
        x: 5476
        y: 0
        z: 701
      L: -1
    - E: 5
      A: 0
      P:
        x: 5490
        y: 0
        z: 854
      L: -1
    - E: 6
      A: 0
      P:
        x: 5494
        y: 0
        z: 769
      L: -1
    - E: 7
      A: 0
      P:
        x: 5512
        y: 0
        z: 611
      L: -1
    S:
    - P:
        x: 5361.937
        y: 583.1879
        z: 5440.063
        w: 617.8121
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000013950003
        z: 1.0000001
        w: 0.0000000013950003
      L: -1
    - P:
        x: 5405.984
        y: 814.0223
        z: 5434.016
        w: 884.9777
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -4.3946388e-10
        z: 0.99999994
        w: -4.3946388e-10
      L: -1
    - P:
        x: 5406.9956
        y: 804.50696
        z: 5437.0044
        w: 759.49304
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5408.7847
        y: 811.50354
        z: 5485.2153
        w: 851.49646
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0
        z: 0.99999994
        w: 0
      L: -1
    - P:
        x: 5397.415
        y: 669.8299
        z: 5437.585
        w: 750.1701
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 2.0736839e-10
        z: 0.99999994
        w: 2.0736839e-10
      L: -1
    - P:
        x: 5399.0137
        y: 661.3876
        z: 5440.9863
        w: 623.6124
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0
        z: 1.0000001
        w: 0
      L: -1
    - P:
        x: 5399.9346
        y: 667.1932
        z: 5471.0654
        w: 698.8068
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000022972066
        z: 0.9999999
        w: -0.0000000022972066
      L: -1
    - P:
        x: 5440.493
        y: 887.00464
        z: 5485.507
        w: 856.99536
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0
        z: 0.99999994
        w: 0
      L: -1
    - P:
        x: 5442.9956
        y: 750.50696
        z: 5473.0044
        w: 705.49304
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0
        z: 1.0000002
        w: 0
      L: -1
    - P:
        x: 5442.4346
        y: 759.8201
        z: 5487.5654
        w: 849.1799
      M: 1
      O: 0
      E:
        x: 1
        y: 1.1163621e-10
        z: 1
        w: 1.1163621e-10
      L: -1
    - P:
        x: 5445.227
        y: 756.3552
        z: 5488.773
        w: 767.6448
      M: 1
      O: 0
      E:
        x: 0.9999998
        y: -0.0000000026499465
        z: 0.9999998
        w: -0.0000000026499465
      L: -1
    - P:
        x: 5446.93
        y: 625.0433
        z: 5474.07
        w: 695.9567
      M: 1
      O: 0
      E:
        x: 1
        y: 6.132817e-10
        z: 1
        w: 6.132817e-10
      L: -1
    - P:
        x: 5450.352
        y: 619.28107
        z: 5506.648
        w: 611.71893
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0.000000008394794
        z: 1.0000002
        w: 0.000000008394794
      L: -1
    - P:
        x: 5477.382
        y: 706.2202
        z: 5492.618
        w: 763.7798
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 1.7205583e-10
        z: 0.99999994
        w: 1.7205583e-10
      L: -1
    - P:
        x: 5478.0054
        y: 695.9862
        z: 5509.9946
        w: 616.0138
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 6.920068e-10
        z: 1.0000004
        w: 6.920068e-10
      L: -1
    - P:
        x: 5490.254
        y: 848.60596
        z: 5493.746
        w: 774.39404
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: 1.00285044e-10
        z: 0.9999999
        w: 1.00285044e-10
      L: -1
    - P:
        x: 5491.896
        y: 859.05615
        z: 5515.104
        w: 920.94385
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000010708723
        z: 1
        w: 0.0000000010708723
      L: -1
  - B:
      MeshList:
      - {fileID: 187466130}
      - {fileID: 1625396297}
      - {fileID: 489942920}
      - {fileID: 653696209}
      - {fileID: 6127480}
      - {fileID: 1863473072}
      - {fileID: 741534110}
      - {fileID: 2054921948}
      - {fileID: 541715775}
      - {fileID: 1696913648}
      - {fileID: 1470113786}
      - {fileID: 1129527157}
      - {fileID: 2066207210}
      - {fileID: 1802189741}
      - {fileID: 1226551194}
      - {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
      MaterialList:
      - {fileID: 2100000, guid: 94b8f85931600a4489aaedb3233117a8, type: 2}
      - {fileID: 2100000, guid: 2f997e464d822c94580467ade134e8aa, type: 2}
    I: 134316184
    O:
    - E: 0
      A: 0
      P:
        x: 5400
        y: 0
        z: 944
      L: -1
    - E: 1
      A: 0
      P:
        x: 5404
        y: 0
        z: 809
      L: -1
    - E: 2
      A: 0
      P:
        x: 5440
        y: 0
        z: 1169
      L: -1
    - E: 3
      A: 0
      P:
        x: 5445
        y: 0
        z: 1034
      L: -1
    - E: 4
      A: 0
      P:
        x: 5436
        y: 0
        z: 890
      L: -1
    - E: 5
      A: 0
      P:
        x: 5440
        y: 0
        z: 755
      L: -1
    - E: 6
      A: 0
      P:
        x: 5445
        y: 0
        z: 620
      L: -1
    - E: 7
      A: 0
      P:
        x: 5476
        y: 0
        z: 1115
      L: -1
    - E: 8
      A: 0
      P:
        x: 5476
        y: 0
        z: 701
      L: -1
    - E: 9
      A: 0
      P:
        x: 5481
        y: 0
        z: 961
      L: -1
    - E: 10
      A: 0
      P:
        x: 5490
        y: 0
        z: 854
      L: -1
    - E: 11
      A: 0
      P:
        x: 5494
        y: 0
        z: 769
      L: -1
    - E: 12
      A: 0
      P:
        x: 5512
        y: 0
        z: 1061
      L: -1
    - E: 13
      A: 0
      P:
        x: 5512
        y: 0
        z: 611
      L: -1
    - E: 14
      A: 0
      P:
        x: 5517
        y: 0
        z: 926
      L: -1
    S:
    - P:
        x: 5361.1143
        y: 582.8233
        z: 5440.8857
        w: 618.1767
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 6.8311234e-10
        z: 0.99999994
        w: 6.8311234e-10
      L: -1
    - P:
        x: 5361.1143
        y: 996.8233
        z: 5440.8857
        w: 1032.1768
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000027324487
        z: 1
        w: 0.0000000027324487
      L: -1
    - P:
        x: 5406.496
        y: 1219.2557
        z: 5437.504
        w: 1172.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5397.0127
        y: 1083.0249
        z: 5437.9873
        w: 1164.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5398.3447
        y: 1075.9896
        z: 5441.6553
        w: 1037.0104
      M: 1
      O: 0
      E:
        x: 1
        y: 0
        z: 1
        w: 0
      L: -1
    - P:
        x: 5399.1123
        y: 1080.8276
        z: 5471.8877
        w: 1113.1724
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000014968627
        z: 0.9999999
        w: -0.0000000014968627
      L: -1
    - P:
        x: 5402.496
        y: 940.2558
        z: 5433.504
        w: 893.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5402.0127
        y: 948.0249
        z: 5442.9873
        w: 1029.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5404.404
        y: 944.9243
        z: 5476.596
        w: 960.0757
      M: 1
      O: 0
      E:
        x: 1.0000001
        y: 0.0000000024240963
        z: 1.0000001
        w: 0.0000000024240963
      L: -1
    - P:
        x: 5405.6533
        y: 813.18524
        z: 5434.3467
        w: 885.81476
      M: 1
      O: 0
      E:
        x: 1
        y: -4.5318693e-10
        z: 1
        w: -4.5318693e-10
      L: -1
    - P:
        x: 5406.496
        y: 805.2558
        z: 5437.504
        w: 758.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5407.9873
        y: 811.0863
        z: 5486.0127
        w: 851.9137
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000016921302
        z: 1
        w: 0.0000000016921302
      L: -1
    - P:
        x: 5397.0127
        y: 669.0249
        z: 5437.9873
        w: 750.9751
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: -6.0988425e-10
        z: 0.99999994
        w: -6.0988425e-10
      L: -1
    - P:
        x: 5398.3447
        y: 661.9897
        z: 5441.6553
        w: 623.0103
      M: 1
      O: 0
      E:
        x: 1.0000002
        y: 0.0000000020458688
        z: 1.0000002
        w: 0.0000000020458688
      L: -1
    - P:
        x: 5399.1123
        y: 666.82764
        z: 5471.8877
        w: 699.17236
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000014968627
        z: 0.9999999
        w: -0.0000000014968627
      L: -1
    - P:
        x: 5442.496
        y: 1165.2557
        z: 5473.504
        w: 1118.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5442.5645
        y: 1172.6978
        z: 5478.676
        w: 1224.7654
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000014109935
        z: 1
        w: -0.000000082660705
      L: -1
    - P:
        x: 5446.6084
        y: 1038.2028
        z: 5474.3916
        w: 1110.7972
      M: 1
      O: 0
      E:
        x: 1
        y: 6.4700606e-10
        z: 1
        w: 6.4700606e-10
      L: -1
    - P:
        x: 5446.99
        y: 1029.9641
        z: 5479.01
        w: 965.0359
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: -0.0000000016466685
        z: 0.9999999
        w: -0.0000000016466685
      L: -1
    - P:
        x: 5449.174
        y: 1035.682
        z: 5507.826
        w: 1059.318
      M: 1
      O: 0
      E:
        x: 1
        y: -9.425786e-10
        z: 1
        w: -9.425786e-10
      L: -1
    - P:
        x: 5438.409
        y: 893.8009
        z: 5478.591
        w: 957.1991
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 0.000000002084515
        z: 0.99999994
        w: 0.000000002084515
      L: -1
    - P:
        x: 5439.744
        y: 887.50385
        z: 5486.256
        w: 856.49615
      M: 1
      O: 0
      E:
        x: 1
        y: -0.000000002132543
        z: 1
        w: -0.000000002132543
      L: -1
    - P:
        x: 5442.496
        y: 751.2558
        z: 5473.504
        w: 704.7442
      M: 1
      O: 0
      E:
        x: 1.0000004
        y: 0.0000000021325444
        z: 1.0000004
        w: 0.0000000021325444
      L: -1
    - P:
        x: 5442.029
        y: 759.0168
        z: 5487.971
        w: 849.9832
      M: 1
      O: 0
      E:
        x: 1
        y: -0.0000000011697536
        z: 1
        w: -0.0000000011697536
      L: -1
    - P:
        x: 5444.356
        y: 756.12933
        z: 5489.644
        w: 767.87067
      M: 1
      O: 0
      E:
        x: 1
        y: 0.0000000012740021
        z: 1
        w: 0.0000000012740021
      L: -1
    - P:
        x: 5446.6084
        y: 624.2027
        z: 5474.3916
        w: 696.7973
      M: 1
      O: 0
      E:
        x: 1
        y: 0.000000001222121
        z: 1
        w: 0.000000001222121
      L: -1
    - P:
        x: 5449.46
        y: 619.4009
        z: 5507.54
        w: 611.5991
      M: 1
      O: 0
      E:
        x: 0.99999976
        y: -0.0000000040684562
        z: 0.99999976
        w: -0.0000000040684562
      L: -1
    - P:
        x: 5476.271
        y: 1119.4918
        z: 5482.517
        w: 1223
      M: 1
      O: 0
      E:
        x: 1
        y: 6.413165e-11
        z: 1.0000001
        w: 0.00000013159843
      L: -1
    - P:
        x: 5478.496
        y: 1111.2557
        z: 5509.504
        w: 1064.7443
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: -0.0000000021325484
        z: 0.99999964
        w: -0.0000000021325484
      L: -1
    - P:
        x: 5477.1514
        y: 705.35016
        z: 5492.8486
        w: 764.64984
      M: 1
      O: 0
      E:
        x: 1
        y: 6.832094e-10
        z: 1
        w: 6.832094e-10
      L: -1
    - P:
        x: 5477.6714
        y: 696.82184
        z: 5510.3286
        w: 615.17816
      M: 1
      O: 0
      E:
        x: 1.0000005
        y: 0.0000000013556857
        z: 1.0000005
        w: 0.0000000013556857
      L: -1
    - P:
        x: 5481.377
        y: 956.5158
        z: 5489.623
        w: 858.4842
      M: 1
      O: 0
      E:
        x: 0.9999999
        y: 0
        z: 0.9999999
        w: 0
      L: -1
    - P:
        x: 5482.3325
        y: 965.2982
        z: 5510.6675
        w: 1056.7018
      M: 1
      O: 0
      E:
        x: 1
        y: -6.812557e-11
        z: 1
        w: -6.812557e-11
      L: -1
    - P:
        x: 5484.2266
        y: 957.86316
        z: 5513.7734
        w: 929.13684
      M: 1
      O: 0
      E:
        x: 1.0000005
        y: 0
        z: 1.0000005
        w: 0
      L: -1
    - P:
        x: 5490.2114
        y: 849.505
        z: 5493.7886
        w: 773.495
      M: 1
      O: 0
      E:
        x: 0.99999964
        y: 9.7912685e-11
        z: 0.99999964
        w: 9.7912685e-11
      L: -1
    - P:
        x: 5491.58
        y: 858.2135
        z: 5515.42
        w: 921.7865
      M: 1
      O: 0
      E:
        x: 0.99999994
        y: 2.7433805e-11
        z: 0.99999994
        w: 2.7433805e-11
      L: -1
  StraightMesh: {fileID: 4300000, guid: 028189ece19c140e597bfc438d353f1e, type: 2}
--- !u!4 &719472080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 719472078}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &741534110
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &792456875
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &847844100
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &866397326
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &893694737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 893694741}
  - component: {fileID: 893694740}
  - component: {fileID: 893694739}
  - component: {fileID: 893694738}
  m_Layer: 0
  m_Name: SectionMeta_134644289
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &893694738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 893694737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &893694739
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 893694737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 1
--- !u!114 &893694740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 893694737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134644289
--- !u!4 &893694741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 893694737}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &924460552
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1072237124
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1097599817
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1122768859
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1122768863}
  - component: {fileID: 1122768862}
  - component: {fileID: 1122768861}
  - component: {fileID: 1122768860}
  m_Layer: 0
  m_Name: SectionMeta_134578753
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1122768860
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122768859}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1122768861
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122768859}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 1
--- !u!114 &1122768862
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122768859}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134578753
--- !u!4 &1122768863
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122768859}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1125013544
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1129527157
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1156098964
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1175618147
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1207594140
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1221229007
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1226551194
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1236860363
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.8000488}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1279794932
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1333104109
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1333104113}
  - component: {fileID: 1333104112}
  - component: {fileID: 1333104111}
  - component: {fileID: 1333104110}
  m_Layer: 0
  m_Name: SectionMeta_134709825
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1333104110
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1333104109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1333104111
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1333104109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 1
--- !u!114 &1333104112
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1333104109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134709825
--- !u!4 &1333104113
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1333104109}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1357474095
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1365040283
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1371853668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1371853672}
  - component: {fileID: 1371853671}
  - component: {fileID: 1371853670}
  - component: {fileID: 1371853669}
  m_Layer: 0
  m_Name: SectionMeta_134513074
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1371853669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1371853668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1371853670
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1371853668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 2
--- !u!114 &1371853671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1371853668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134513074
--- !u!4 &1371853672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1371853668}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1375946421
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.8000488}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1416772405
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1470113786
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1473712819
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1516961759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1516961763}
  - component: {fileID: 1516961762}
  - component: {fileID: 1516961761}
  - component: {fileID: 1516961760}
  m_Layer: 0
  m_Name: SectionMeta_134578610
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1516961760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516961759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1516961761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516961759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 2
--- !u!114 &1516961762
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516961759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134578610
--- !u!4 &1516961763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516961759}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1529933195
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1614286190
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1625396297
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1670985339
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1689393582
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1695344267
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1696913648
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1706643635
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1776604137
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1776604141}
  - component: {fileID: 1776604140}
  - component: {fileID: 1776604139}
  - component: {fileID: 1776604138}
  m_Layer: 0
  m_Name: SectionMeta_134447396
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1776604138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776604137}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 054d1f64ae2e41abba98a63f7f0e8e4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1776604139
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776604137}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea79f38a8bf34417a7dc55be4fe7d6b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  value: 4
--- !u!114 &1776604140
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776604137}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c5b58f8d31375342a221ed113e71008, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SectionIndex: 134447396
--- !u!4 &1776604141
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776604137}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1802189741
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1810840326
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1863473072
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1873074244
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1917110416
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 33bf191833bf003c0000003c000000000000000033bf1918333f003c0000003c000000000000003c333f191833bf003c0000003c00000000003c0000333f1918333f003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 1.7998047, y: 0, z: 1.7999878}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1957822952
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1975250942
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1977732245
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &1989622641
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &2009918425
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 66c5191866c5003c0000003c000000000000000066c519186645003c0000003c000000000000003c6645191866c5003c0000003c00000000003c0000664519186645003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 5.3999023, y: 0, z: 5.4000244}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &2054921948
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!43 &2066207210
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 6
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 0
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 4
    m_Channels:
    - stream: 0
      offset: 0
      format: 1
      dimension: 4
    - stream: 0
      offset: 8
      format: 1
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 16
      format: 1
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 80
    _typelessdata: 80c4191880c4003c0000003c000000000000000080c419188044003c0000003c000000000000003c8044191880c4003c0000003c00000000003c0000804419188044003c0000003c00000000003c003c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0.002, z: 0}
    m_Extent: {x: 4.5, y: 0, z: 4.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 719472080}
  - {fileID: 893694741}
  - {fileID: 1122768863}
  - {fileID: 1333104113}
  - {fileID: 519055666}
  - {fileID: 1371853672}
  - {fileID: 1516961763}
  - {fileID: 164303874}
  - {fileID: 1776604141}
  - {fileID: 506635000}
  - {fileID: 583939995}
