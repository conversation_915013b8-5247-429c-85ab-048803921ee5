{"connections": [{"path": [9.0, 729.0, 14.0, 1031.0]}, {"path": [9.0, 729.0, 17.0, 473.0]}, {"path": [9.0, 729.0, 86.0, 862.0]}, {"path": [9.0, 729.0, 161.0, 752.0]}, {"path": [9.0, 729.0, 220.0, 607.0]}, {"path": [10.0, 1395.0, 14.0, 1557.0]}, {"path": [10.0, 1395.0, 15.0, 1279.0]}, {"path": [10.0, 1395.0, 58.0, 1461.0]}, {"path": [10.0, 1395.0, 96.0, 1342.0]}, {"path": [13.0, 27.0, 17.0, 473.0]}, {"path": [13.0, 27.0, 196.0, 278.0]}, {"path": [13.0, 27.0, 315.0, 24.0]}, {"path": [14.0, 1031.0, 16.0, 1176.0]}, {"path": [14.0, 1031.0, 86.0, 862.0]}, {"path": [14.0, 1031.0, 155.0, 1160.0]}, {"path": [14.0, 1031.0, 162.0, 1005.0]}, {"path": [14.0, 1557.0, 18.0, 1669.0]}, {"path": [14.0, 1557.0, 58.0, 1461.0]}, {"path": [14.0, 1557.0, 98.0, 1631.0]}, {"path": [14.0, 1557.0, 105.0, 1554.0]}, {"path": [15.0, 1279.0, 16.0, 1176.0]}, {"path": [15.0, 1279.0, 96.0, 1342.0]}, {"path": [15.0, 1279.0, 110.0, 1257.0]}, {"path": [16.0, 1176.0, 110.0, 1257.0]}, {"path": [16.0, 1176.0, 155.0, 1160.0]}, {"path": [17.0, 473.0, 196.0, 278.0]}, {"path": [17.0, 473.0, 220.0, 607.0]}, {"path": [18.0, 1669.0, 47.44, 1700.92]}, {"path": [18.0, 1669.0, 98.0, 1631.0]}, {"path": [47.44, 1700.92, 57.33333333333333, 1701.6666666666667]}, {"path": [57.33333333333333, 1701.6666666666667, 84.0, 1701.0]}, {"path": [58.0, 1461.0, 96.0, 1342.0]}, {"path": [58.0, 1461.0, 100.75901639344266, 1495.8508196721311]}, {"path": [58.0, 1461.0, 105.0, 1554.0]}, {"path": [58.0, 1461.0, 109.61702127659578, 1415.0]}, {"path": [73.0, 1783.0, 79.26213592232989, 1760.0388349514562]}, {"path": [73.0, 1783.0, 153.33333333333334, 1771.6666666666667]}, {"path": [79.26213592232989, 1760.0388349514562, 96.0, 1700.0]}, {"path": [84.0, 1701.0, 96.0, 1700.0]}, {"path": [86.0, 862.0, 161.0, 752.0]}, {"path": [86.0, 862.0, 162.0, 1005.0]}, {"path": [86.0, 862.0, 217.0, 893.0]}, {"path": [96.0, 1342.0, 110.0, 1257.0]}, {"path": [96.0, 1342.0, 112.3076923076923, 1401.4615384615386]}, {"path": [96.0, 1342.0, 150.84466019417493, 1353.7524271844657]}, {"path": [96.0, 1342.0, 169.0479896238651, 1315.520103761349]}, {"path": [96.0, 1700.0, 96.9443298969072, 1691.2247422680412]}, {"path": [96.0, 1700.0, 124.34831460674158, 1695.8426966292134]}, {"path": [96.9443298969072, 1691.2247422680412, 98.0, 1631.0]}, {"path": [98.0, 1631.0, 105.0, 1554.0]}, {"path": [98.0, 1631.0, 145.05882352941194, 1679.941176470588]}, {"path": [98.0, 1631.0, 203.0, 1603.0]}, {"path": [100.75901639344266, 1495.8508196721311, 108.0, 1503.0]}, {"path": [105.0, 1554.0, 110.0967741935483, 1508.032258064516]}, {"path": [105.0, 1554.0, 181.59322033898297, 1538.3898305084745]}, {"path": [105.0, 1554.0, 203.0, 1603.0]}, {"path": [108.0, 1503.0, 110.0967741935483, 1508.032258064516]}, {"path": [108.0, 1503.0, 128.0, 1442.0]}, {"path": [108.0, 1503.0, 154.0256410256411, 1515.2735042735042]}, {"path": [108.0, 1503.0, 177.8, 1464.6]}, {"path": [109.61702127659578, 1415.0, 116.0, 1409.0]}, {"path": [110.0, 1257.0, 155.0, 1160.0]}, {"path": [110.0, 1257.0, 169.8910891089109, 1307.089108910891]}, {"path": [110.0, 1257.0, 180.65599999999995, 1241.6399999999999]}, {"path": [112.3076923076923, 1401.4615384615386, 116.0, 1409.0]}, {"path": [116.0, 1409.0, 128.0, 1412.0]}, {"path": [116.0, 1409.0, 147.58333333333212, 1376.8611111111109]}, {"path": [124.34831460674158, 1695.8426966292134, 148.0, 1683.0]}, {"path": [128.0, 1412.0, 128.0, 1442.0]}, {"path": [128.0, 1412.0, 193.40462427745666, 1442.3699421965316]}, {"path": [145.05882352941194, 1679.941176470588, 148.0, 1683.0]}, {"path": [147.58333333333212, 1376.8611111111109, 166.0, 1357.0]}, {"path": [148.0, 1683.0, 155.32589285714272, 1758.0]}, {"path": [148.0, 1683.0, 160.15601023017916, 1666.8746803069055]}, {"path": [148.0, 1683.0, 189.9906976744187, 1694.0139534883717]}, {"path": [150.84466019417493, 1353.7524271844657, 166.0, 1357.0]}, {"path": [153.33333333333334, 1771.6666666666667, 155.32589285714272, 1758.0]}, {"path": [153.33333333333334, 1771.6666666666667, 189.33333333333334, 1767.6666666666667]}, {"path": [154.0256410256411, 1515.2735042735042, 213.0, 1531.0]}, {"path": [155.0, 1160.0, 162.0, 1005.0]}, {"path": [155.0, 1160.0, 187.96923076923076, 1213.753846153846]}, {"path": [155.0, 1160.0, 249.0, 1087.0]}, {"path": [155.0, 1160.0, 265.0, 1177.0]}, {"path": [160.15601023017916, 1666.8746803069055, 197.0, 1624.0]}, {"path": [161.0, 752.0, 217.0, 893.0]}, {"path": [161.0, 752.0, 220.0, 607.0]}, {"path": [161.0, 752.0, 262.0, 803.0]}, {"path": [162.0, 1005.0, 217.0, 893.0]}, {"path": [162.0, 1005.0, 249.0, 1087.0]}, {"path": [162.0, 1005.0, 294.0, 983.0]}, {"path": [166.0, 1357.0, 171.54945054945063, 1332.5824175824177]}, {"path": [166.0, 1357.0, 189.0, 1374.0]}, {"path": [166.0, 1357.0, 230.29126213592224, 1362.8446601941748]}, {"path": [169.0479896238651, 1315.520103761349, 176.0, 1313.0]}, {"path": [169.8910891089109, 1307.089108910891, 176.0, 1313.0]}, {"path": [171.54945054945063, 1332.5824175824177, 176.0, 1313.0]}, {"path": [176.0, 1313.0, 189.384236453202, 1273.8768472906404]}, {"path": [176.0, 1313.0, 251.06756756756755, 1339.0945945945946]}, {"path": [177.8, 1464.6, 197.0, 1451.0]}, {"path": [180.65599999999995, 1241.6399999999999, 202.0, 1237.0]}, {"path": [181.59322033898297, 1538.3898305084745, 213.0, 1531.0]}, {"path": [187.96923076923076, 1213.753846153846, 202.0, 1237.0]}, {"path": [189.0, 1374.0, 202.8223350253807, 1402.51269035533]}, {"path": [189.33333333333334, 1767.6666666666667, 203.33333333333331, 1753.0]}, {"path": [189.384236453202, 1273.8768472906404, 202.0, 1237.0]}, {"path": [189.9906976744187, 1694.0139534883717, 209.0, 1699.0]}, {"path": [193.40462427745666, 1442.3699421965316, 197.0, 1451.0]}, {"path": [196.0, 278.0, 220.0, 607.0]}, {"path": [196.0, 278.0, 311.0, 191.0]}, {"path": [196.0, 278.0, 315.0, 24.0]}, {"path": [196.0, 278.0, 363.0, 447.0]}, {"path": [197.0, 1451.0, 202.78947368421052, 1479.9473684210527]}, {"path": [197.0, 1451.0, 202.8223350253807, 1402.51269035533]}, {"path": [197.0, 1451.0, 232.81935023206006, 1412.9136022848982]}, {"path": [197.0, 1451.0, 259.0, 1470.0]}, {"path": [197.0, 1451.0, 290.0, 1437.0]}, {"path": [197.0, 1624.0, 198.14285714285748, 1615.1428571428569]}, {"path": [197.0, 1624.0, 206.62962962962962, 1683.0]}, {"path": [197.0, 1624.0, 229.0, 1652.0]}, {"path": [198.14285714285748, 1615.1428571428569, 203.0, 1603.0]}, {"path": [202.0, 1237.0, 240.0, 1237.0]}, {"path": [202.0, 1237.0, 241.99999999999977, 1201.5000000000002]}, {"path": [202.0, 1237.0, 254.0, 1290.0]}, {"path": [202.78947368421052, 1479.9473684210527, 213.0, 1531.0]}, {"path": [203.0, 1603.0, 205.84615384615387, 1598.2307692307693]}, {"path": [203.0, 1603.0, 287.0, 1621.0]}, {"path": [203.33333333333331, 1753.0, 209.0, 1699.0]}, {"path": [205.84615384615387, 1598.2307692307693, 230.0, 1580.3333333333333]}, {"path": [206.62962962962962, 1683.0, 209.0, 1699.0]}, {"path": [209.0, 1699.0, 238.0, 1752.0]}, {"path": [209.0, 1699.0, 249.0, 1693.0]}, {"path": [213.0, 1531.0, 225.91176470588238, 1540.8529411764705]}, {"path": [217.0, 893.0, 262.0, 803.0]}, {"path": [217.0, 893.0, 294.0, 983.0]}, {"path": [217.0, 893.0, 360.0, 863.0]}, {"path": [220.0, 607.0, 262.0, 803.0]}, {"path": [220.0, 607.0, 330.0, 669.0]}, {"path": [220.0, 607.0, 363.0, 447.0]}, {"path": [225.91176470588238, 1540.8529411764705, 262.0, 1580.0]}, {"path": [229.0, 1652.0, 267.63483146067415, 1669.9157303370787]}, {"path": [230.0, 1580.3333333333333, 262.0, 1580.0]}, {"path": [230.29126213592224, 1362.8446601941748, 276.0, 1367.0]}, {"path": [232.81935023206006, 1412.9136022848982, 276.0, 1367.0]}, {"path": [238.0, 1752.0, 310.0, 1772.0]}, {"path": [240.0, 1237.0, 318.71164813202233, 1265.8171870547753]}, {"path": [241.99999999999977, 1201.5000000000002, 265.0, 1177.0]}, {"path": [249.0, 1087.0, 265.0, 1177.0]}, {"path": [249.0, 1087.0, 294.0, 983.0]}, {"path": [249.0, 1087.0, 352.0, 1135.0]}, {"path": [249.0, 1087.0, 380.0, 1063.0]}, {"path": [249.0, 1693.0, 298.0, 1673.0]}, {"path": [251.06756756756755, 1339.0945945945946, 276.0, 1367.0]}, {"path": [254.0, 1290.0, 270.9127654324918, 1335.6796902359047]}, {"path": [259.0, 1470.0, 300.0, 1503.0]}, {"path": [262.0, 803.0, 314.0625000000001, 792.9374999999999]}, {"path": [262.0, 803.0, 330.0, 669.0]}, {"path": [262.0, 803.0, 360.0, 863.0]}, {"path": [262.0, 1580.0, 265.2710280373831, 1585.3644859813085]}, {"path": [262.0, 1580.0, 297.0, 1538.0]}, {"path": [262.0, 1580.0, 329.3333333333333, 1579.6666666666667]}, {"path": [265.0, 1177.0, 352.0, 1135.0]}, {"path": [265.0, 1177.0, 389.0, 1243.0]}, {"path": [265.2710280373831, 1585.3644859813085, 287.0, 1621.0]}, {"path": [267.63483146067415, 1669.9157303370787, 298.0, 1673.0]}, {"path": [270.9127654324918, 1335.6796902359047, 276.0, 1367.0]}, {"path": [276.0, 1367.0, 310.0066225165563, 1390.019867549669]}, {"path": [276.0, 1367.0, 313.15415019762804, 1347.6324110671937]}, {"path": [287.0, 1621.0, 296.6447638603704, 1666.5934291581111]}, {"path": [287.0, 1621.0, 379.0, 1633.0]}, {"path": [290.0, 1437.0, 341.0, 1411.0]}, {"path": [294.0, 983.0, 360.0, 863.0]}, {"path": [294.0, 983.0, 380.0, 1063.0]}, {"path": [294.0, 983.0, 403.0, 969.0]}, {"path": [296.6447638603704, 1666.5934291581111, 298.0, 1673.0]}, {"path": [297.0, 1538.0, 300.9230769230769, 1521.6153846153845]}, {"path": [298.0, 1673.0, 318.0, 1687.0]}, {"path": [300.0, 1503.0, 300.9230769230769, 1521.6153846153845]}, {"path": [300.0, 1503.0, 324.728128342246, 1514.9031016042782]}, {"path": [310.0, 1772.0, 350.0, 1769.0]}, {"path": [310.0066225165563, 1390.019867549669, 341.0, 1411.0]}, {"path": [311.0, 191.0, 315.0, 24.0]}, {"path": [311.0, 191.0, 350.24563017479295, 142.16099356025757]}, {"path": [311.0, 191.0, 363.0, 447.0]}, {"path": [311.0, 191.0, 467.0, 257.0]}, {"path": [313.15415019762804, 1347.6324110671937, 370.0, 1318.0]}, {"path": [314.0625000000001, 792.9374999999999, 381.0, 780.0]}, {"path": [315.0, 24.0, 361.2307692307692, 81.84615384615384]}, {"path": [315.0, 24.0, 496.0, 22.0]}, {"path": [318.0, 1687.0, 342.3424657534246, 1691.2465753424656]}, {"path": [318.71164813202233, 1265.8171870547753, 370.0, 1318.0]}, {"path": [324.728128342246, 1514.9031016042782, 363.0, 1524.0]}, {"path": [329.3333333333333, 1579.6666666666667, 355.37113402061857, 1604.8350515463917]}, {"path": [329.3333333333333, 1579.6666666666667, 386.54353562005275, 1566.1081794195252]}, {"path": [330.0, 669.0, 363.0, 447.0]}, {"path": [330.0, 669.0, 368.0487804878049, 713.4390243902438]}, {"path": [330.0, 669.0, 411.0, 546.0]}, {"path": [330.0, 669.0, 465.0, 628.0]}, {"path": [341.0, 1411.0, 371.49655172413793, 1478.0]}, {"path": [341.0, 1411.0, 378.97364186248825, 1368.135598212492]}, {"path": [341.0, 1411.0, 409.0, 1466.0]}, {"path": [341.0, 1411.0, 430.0, 1391.7166666666667]}, {"path": [342.3424657534246, 1691.2465753424656, 370.0, 1689.0]}, {"path": [350.0, 1769.0, 358.9384615384615, 1758.5076923076922]}, {"path": [350.24563017479295, 142.16099356025757, 361.0, 137.0]}, {"path": [352.0, 1135.0, 380.0, 1063.0]}, {"path": [352.0, 1135.0, 389.0, 1243.0]}, {"path": [352.0, 1135.0, 453.0, 1155.0]}, {"path": [355.37113402061857, 1604.8350515463917, 379.0, 1633.0]}, {"path": [358.9384615384615, 1758.5076923076922, 400.0, 1677.0]}, {"path": [360.0, 863.0, 379.37931034482756, 815.551724137931]}, {"path": [360.0, 863.0, 403.0, 969.0]}, {"path": [360.0, 863.0, 430.0, 900.0]}, {"path": [360.0, 863.0, 435.0, 840.0]}, {"path": [361.0, 137.0, 369.0, 131.0]}, {"path": [361.2307692307692, 81.84615384615384, 384.6666666666667, 111.66666666666666]}, {"path": [363.0, 447.0, 411.0, 546.0]}, {"path": [363.0, 447.0, 467.0, 257.0]}, {"path": [363.0, 447.0, 544.0, 496.0]}, {"path": [363.0, 447.0, 579.0, 348.0]}, {"path": [363.0, 1524.0, 407.0, 1556.0]}, {"path": [368.0487804878049, 713.4390243902438, 424.0, 722.0]}, {"path": [369.0, 131.0, 384.6666666666667, 111.66666666666666]}, {"path": [370.0, 1318.0, 378.97364186248825, 1368.135598212492]}, {"path": [370.0, 1318.0, 382.9754098360656, 1271.2704918032787]}, {"path": [370.0, 1318.0, 422.0, 1317.0]}, {"path": [370.0, 1318.0, 433.0, 1364.3846153846152]}, {"path": [370.0, 1689.0, 400.0, 1677.0]}, {"path": [371.49655172413793, 1478.0, 407.0, 1556.0]}, {"path": [379.0, 1633.0, 396.19672131147536, 1669.1639344262296]}, {"path": [379.0, 1633.0, 398.91666666666714, 1578.2291666666667]}, {"path": [379.0, 1633.0, 489.0, 1614.0]}, {"path": [379.37931034482756, 815.551724137931, 381.0, 780.0]}, {"path": [380.0, 1063.0, 403.0, 969.0]}, {"path": [380.0, 1063.0, 430.35363457760286, 1023.6326129666011]}, {"path": [380.0, 1063.0, 453.0, 1155.0]}, {"path": [380.0, 1063.0, 484.0, 1090.9402985074628]}, {"path": [381.0, 780.0, 395.1904761904762, 752.6326530612245]}, {"path": [381.0, 780.0, 398.92071611253203, 779.3017902813301]}, {"path": [382.9754098360656, 1271.2704918032787, 389.0, 1243.0]}, {"path": [384.6666666666667, 111.66666666666666, 418.6307692307692, 108.04615384615363]}, {"path": [386.54353562005275, 1566.1081794195252, 407.0, 1556.0]}, {"path": [389.0, 1243.0, 453.0, 1155.0]}, {"path": [389.0, 1243.0, 484.75609756097566, 1239.8048780487804]}, {"path": [395.1904761904762, 752.6326530612245, 424.0, 722.0]}, {"path": [396.19672131147536, 1669.1639344262296, 400.0, 1677.0]}, {"path": [398.91666666666714, 1578.2291666666667, 407.0, 1556.0]}, {"path": [398.92071611253203, 779.3017902813301, 458.0, 777.0]}, {"path": [400.0, 1677.0, 415.3333333333333, 1704.3333333333333]}, {"path": [400.0, 1677.0, 442.9622641509434, 1673.0943396226414]}, {"path": [403.0, 969.0, 429.98039215686276, 1012.0]}, {"path": [403.0, 969.0, 430.0, 900.0]}, {"path": [403.0, 969.0, 495.0, 983.0]}, {"path": [407.0, 1556.0, 421.6027397260275, 1566.3287671232874]}, {"path": [407.0, 1556.0, 435.19999999999993, 1513.6]}, {"path": [409.0, 1466.0, 453.0, 1495.0]}, {"path": [411.0, 546.0, 465.0, 628.0]}, {"path": [411.0, 546.0, 544.0, 496.0]}, {"path": [415.3333333333333, 1704.3333333333333, 421.3333333333333, 1755.0]}, {"path": [418.6307692307692, 108.04615384615363, 448.0, 105.0]}, {"path": [421.3333333333333, 1755.0, 459.4927007299269, 1775.2299270072995]}, {"path": [421.6027397260275, 1566.3287671232874, 489.0, 1614.0]}, {"path": [422.0, 1317.0, 462.2646264626462, 1298.6543654365437]}, {"path": [424.0, 722.0, 486.0, 713.0]}, {"path": [429.98039215686276, 1012.0, 435.0, 1020.0]}, {"path": [430.0, 900.0, 435.0, 840.0]}, {"path": [430.0, 900.0, 505.67567567567573, 889.0540540540544]}, {"path": [430.0, 1391.7166666666667, 461.0, 1385.0]}, {"path": [430.35363457760286, 1023.6326129666011, 435.0, 1020.0]}, {"path": [433.0, 1364.3846153846152, 461.0, 1385.0]}, {"path": [433.8588235294118, 823.635294117647, 435.0, 815.0]}, {"path": [433.8588235294118, 823.635294117647, 435.0, 840.0]}, {"path": [435.0, 815.0, 458.0, 777.0]}, {"path": [435.0, 840.0, 486.0, 798.0]}, {"path": [435.0, 1020.0, 482.66666666666663, 1033.0]}, {"path": [435.0, 1020.0, 492.0, 1077.0]}, {"path": [435.19999999999993, 1513.6, 453.0, 1495.0]}, {"path": [442.9622641509434, 1673.0943396226414, 530.6666666666666, 1667.0]}, {"path": [448.0, 105.0, 451.358024691358, 87.79012345679021]}, {"path": [448.0, 105.0, 484.0, 116.0]}, {"path": [451.358024691358, 87.79012345679021, 496.0, 22.0]}, {"path": [453.0, 1155.0, 486.65384615384613, 1125.2692307692307]}, {"path": [453.0, 1155.0, 494.50299401197594, 1212.245508982036]}, {"path": [453.0, 1495.0, 457.8555678059538, 1428.2359426681364]}, {"path": [453.0, 1495.0, 460.84069097888664, 1496.0940499040307]}, {"path": [453.0, 1495.0, 470.0, 1474.0]}, {"path": [457.8555678059538, 1428.2359426681364, 461.0, 1385.0]}, {"path": [458.0, 777.0, 461.0, 738.0]}, {"path": [458.0, 777.0, 461.4838709677417, 779.6129032258066]}, {"path": [458.0, 777.0, 466.5135135135135, 772.9189189189191]}, {"path": [459.4927007299269, 1775.2299270072995, 544.0, 1764.0]}, {"path": [460.84069097888664, 1496.0940499040307, 496.0, 1501.0]}, {"path": [461.0, 738.0, 486.0, 713.0]}, {"path": [461.0, 1385.0, 482.4285714285711, 1320.7142857142856]}, {"path": [461.0, 1385.0, 502.0, 1397.3362831858408]}, {"path": [461.0, 1385.0, 521.2564102564097, 1343.6813186813185]}, {"path": [461.4838709677417, 779.6129032258066, 486.0, 798.0]}, {"path": [462.2646264626462, 1298.6543654365437, 511.0, 1235.0]}, {"path": [465.0, 628.0, 511.0, 699.0]}, {"path": [465.0, 628.0, 526.0, 616.0]}, {"path": [465.0, 628.0, 544.0, 496.0]}, {"path": [466.5135135135135, 772.9189189189191, 512.0, 759.0]}, {"path": [467.0, 257.0, 579.0, 348.0]}, {"path": [467.0, 257.0, 596.0, 139.0]}, {"path": [467.0, 257.0, 684.0, 232.0]}, {"path": [470.0, 1474.0, 509.6592569069546, 1438.2123425426062]}, {"path": [482.4285714285711, 1320.7142857142856, 511.0, 1235.0]}, {"path": [482.66666666666663, 1033.0, 486.5, 1015.5]}, {"path": [482.66666666666663, 1033.0, 560.782336360789, 1046.5101785155027]}, {"path": [484.0, 116.0, 522.0, 111.0]}, {"path": [484.0, 1090.9402985074628, 514.0, 1099.0]}, {"path": [484.75609756097566, 1239.8048780487804, 511.0, 1235.0]}, {"path": [486.0, 713.0, 497.6153846153846, 735.9230769230769]}, {"path": [486.0, 713.0, 501.4528301886793, 704.6415094339623]}, {"path": [486.0, 798.0, 505.44041450777195, 844.6735751295337]}, {"path": [486.0, 798.0, 512.0, 759.0]}, {"path": [486.5, 1015.5, 495.0, 983.0]}, {"path": [486.65384615384613, 1125.2692307692307, 514.0, 1099.0]}, {"path": [489.0, 1614.0, 496.0, 1501.0]}, {"path": [489.0, 1614.0, 523.9884393063584, 1657.9248554913295]}, {"path": [489.0, 1614.0, 558.0, 1553.0]}, {"path": [489.0, 1614.0, 614.0, 1617.0]}, {"path": [492.0, 1077.0, 514.0, 1099.0]}, {"path": [494.50299401197594, 1212.245508982036, 511.0, 1235.0]}, {"path": [495.0, 983.0, 530.8101804270871, 940.2407217083478]}, {"path": [495.0, 983.0, 580.0, 972.0]}, {"path": [496.0, 22.0, 596.0, 139.0]}, {"path": [496.0, 22.0, 663.0, 17.0]}, {"path": [496.0, 1501.0, 540.0, 1465.0]}, {"path": [496.0, 1501.0, 558.0, 1553.0]}, {"path": [496.0, 1501.0, 565.1951219512196, 1513.2439024390244]}, {"path": [497.6153846153846, 735.9230769230769, 512.0, 759.0]}, {"path": [501.4528301886793, 704.6415094339623, 511.0, 699.0]}, {"path": [502.0, 1397.3362831858408, 574.0, 1419.0]}, {"path": [505.44041450777195, 844.6735751295337, 533.0, 889.0]}, {"path": [505.67567567567573, 889.0540540540544, 533.0, 889.0]}, {"path": [509.6592569069546, 1438.2123425426062, 537.0, 1427.0]}, {"path": [511.0, 699.0, 512.0, 759.0]}, {"path": [511.0, 699.0, 532.2400000000001, 700.3199999999999]}, {"path": [511.0, 1235.0, 524.0, 1225.0]}, {"path": [511.0, 1235.0, 545.0999999999999, 1228.3000000000006]}, {"path": [511.0, 1235.0, 548.5720164609053, 1288.2839506172838]}, {"path": [512.0, 759.0, 552.0, 747.4905660377358]}, {"path": [512.0, 759.0, 555.5882352941177, 794.3529411764706]}, {"path": [512.0, 759.0, 559.0, 783.1333333333333]}, {"path": [514.0, 1099.0, 519.0393700787401, 1156.4488188976377]}, {"path": [514.0, 1099.0, 547.7093551316984, 1143.6539509536785]}, {"path": [514.0, 1099.0, 599.7837837837837, 1079.936936936937]}, {"path": [519.0393700787401, 1156.4488188976377, 524.0, 1213.0]}, {"path": [521.2564102564097, 1343.6813186813185, 566.0, 1313.0]}, {"path": [522.0, 111.0, 548.7846153846154, 126.87692307692296]}, {"path": [523.9884393063584, 1657.9248554913295, 530.6666666666666, 1667.0]}, {"path": [524.0, 1213.0, 524.0, 1225.0]}, {"path": [526.0, 616.0, 593.0, 607.0]}, {"path": [530.6666666666666, 1667.0, 565.0, 1662.0]}, {"path": [530.8101804270871, 940.2407217083478, 567.3333333333334, 933.0]}, {"path": [532.2400000000001, 700.3199999999999, 554.0, 692.0]}, {"path": [533.0, 889.0, 547.8823529411765, 854.5294117647059]}, {"path": [533.0, 889.0, 550.0, 889.0]}, {"path": [533.0, 889.0, 555.5431309904153, 873.3578274760383]}, {"path": [537.0, 1427.0, 574.0, 1419.0]}, {"path": [540.0, 1465.0, 567.4409448818906, 1427.8740157480313]}, {"path": [540.0, 1465.0, 571.9024390243903, 1507.8780487804877]}, {"path": [540.0, 1465.0, 626.0, 1453.0]}, {"path": [540.0, 1465.0, 659.0, 1534.0]}, {"path": [544.0, 496.0, 578.0, 567.0]}, {"path": [544.0, 496.0, 579.0, 348.0]}, {"path": [544.0, 496.0, 631.0, 477.0]}, {"path": [544.0, 1764.0, 549.1153846153848, 1739.1538461538462]}, {"path": [544.0, 1764.0, 586.0, 1767.0]}, {"path": [545.0999999999999, 1228.3000000000006, 591.0, 1201.0]}, {"path": [547.7093551316984, 1143.6539509536785, 591.0, 1201.0]}, {"path": [547.8823529411765, 854.5294117647059, 563.0, 801.0]}, {"path": [548.5720164609053, 1288.2839506172838, 566.0, 1313.0]}, {"path": [548.7846153846154, 126.87692307692296, 596.0, 139.0]}, {"path": [549.1153846153848, 1739.1538461538462, 565.0, 1662.0]}, {"path": [550.0, 889.0, 556.0, 904.0]}, {"path": [550.0, 889.0, 578.1786273318243, 903.7550700961004]}, {"path": [552.0, 747.4905660377358, 616.0, 733.0]}, {"path": [554.0, 692.0, 568.0, 679.0]}, {"path": [555.5431309904153, 873.3578274760383, 582.0, 855.0]}, {"path": [555.5882352941177, 794.3529411764706, 563.0, 801.0]}, {"path": [556.0, 904.0, 579.0, 941.0]}, {"path": [558.0, 1553.0, 573.6999999999998, 1521.6]}, {"path": [558.0, 1553.0, 614.0, 1617.0]}, {"path": [558.0, 1553.0, 659.0, 1534.0]}, {"path": [559.0, 783.1333333333333, 585.0, 797.0]}, {"path": [560.782336360789, 1046.5101785155027, 658.0, 1067.0]}, {"path": [563.0, 801.0, 571.6216216216216, 799.4324324324324]}, {"path": [563.0, 801.0, 577.3321799307969, 841.7335640138407]}, {"path": [565.0, 1662.0, 568.3300970873788, 1658.9417475728155]}, {"path": [565.0, 1662.0, 592.0217391304348, 1669.3695652173913]}, {"path": [565.1951219512196, 1513.2439024390244, 577.0, 1515.0]}, {"path": [566.0, 1313.0, 569.57805907173, 1360.4092827004217]}, {"path": [566.0, 1313.0, 582.0714285714284, 1241.0]}, {"path": [566.0, 1313.0, 622.0, 1334.0]}, {"path": [566.0, 1313.0, 651.1557831839522, 1263.6683738796416]}, {"path": [567.3333333333334, 933.0, 579.0, 941.0]}, {"path": [567.4409448818906, 1427.8740157480313, 574.0, 1419.0]}, {"path": [568.0, 679.0, 580.2194513715709, 643.8079800498755]}, {"path": [568.0, 679.0, 598.3384030418249, 678.0988593155893]}, {"path": [568.0, 679.0, 608.2162162162163, 724.2432432432432]}, {"path": [568.3300970873788, 1658.9417475728155, 614.0, 1617.0]}, {"path": [569.57805907173, 1360.4092827004217, 574.0, 1419.0]}, {"path": [571.6216216216216, 799.4324324324324, 585.0, 797.0]}, {"path": [571.9024390243903, 1507.8780487804877, 577.0, 1515.0]}, {"path": [573.6999999999998, 1521.6, 577.0, 1515.0]}, {"path": [574.0, 1419.0, 581.0, 1423.0]}, {"path": [574.0, 1419.0, 617.1269296740995, 1400.3962264150955]}, {"path": [577.0, 1515.0, 601.9734345351044, 1520.7865275142312]}, {"path": [577.3321799307969, 841.7335640138407, 582.0, 855.0]}, {"path": [578.0, 567.0, 584.0, 585.0]}, {"path": [578.1786273318243, 903.7550700961004, 615.0, 920.0]}, {"path": [579.0, 348.0, 631.0, 477.0]}, {"path": [579.0, 348.0, 684.0, 232.0]}, {"path": [579.0, 348.0, 730.0, 415.0]}, {"path": [579.0, 941.0, 580.0, 945.7272727272727]}, {"path": [579.0, 941.0, 596.0526315789473, 931.0526315789473]}, {"path": [580.0, 945.7272727272727, 580.0, 972.0]}, {"path": [580.0, 972.0, 593.7837837837837, 1021.7027027027027]}, {"path": [580.0, 972.0, 646.0, 963.0]}, {"path": [580.2194513715709, 643.8079800498755, 593.0, 607.0]}, {"path": [581.0, 1423.0, 626.0, 1453.0]}, {"path": [582.0, 855.0, 601.67251975417, 893.7489025460932]}, {"path": [582.0, 855.0, 602.4347826086956, 846.8260869565217]}, {"path": [582.0, 855.0, 642.6666666666666, 854.3333333333334]}, {"path": [582.0, 855.0, 673.8994082840237, 896.5029585798816]}, {"path": [582.0714285714284, 1241.0, 591.0, 1201.0]}, {"path": [584.0, 585.0, 593.0, 607.0]}, {"path": [584.0, 585.0, 616.3, 551.1]}, {"path": [585.0, 797.0, 600.9779735682819, 764.013215859031]}, {"path": [585.0, 797.0, 601.0, 804.0]}, {"path": [585.0, 797.0, 632.446732318709, 761.2524619516557]}, {"path": [585.0, 797.0, 651.6470588235294, 806.0588235294118]}, {"path": [586.0, 1767.0, 622.0, 1751.0]}, {"path": [591.0, 1201.0, 637.0869565217391, 1108.8260869565217]}, {"path": [591.0, 1201.0, 681.0, 1222.0]}, {"path": [592.0217391304348, 1669.3695652173913, 642.0, 1683.0]}, {"path": [593.0, 607.0, 625.4074074074078, 601.5555555555557]}, {"path": [593.0, 607.0, 640.7612716763006, 639.7760528488852]}, {"path": [593.7837837837837, 1021.7027027027027, 658.0, 1067.0]}, {"path": [596.0, 139.0, 663.0, 17.0]}, {"path": [596.0, 139.0, 684.0, 232.0]}, {"path": [596.0, 139.0, 758.0, 112.0]}, {"path": [596.0526315789473, 931.0526315789473, 615.0, 920.0]}, {"path": [598.3384030418249, 678.0988593155893, 669.0, 676.0]}, {"path": [599.7837837837837, 1079.936936936937, 658.0, 1067.0]}, {"path": [600.9779735682819, 764.013215859031, 616.0, 733.0]}, {"path": [601.0, 804.0, 608.0, 838.0]}, {"path": [601.67251975417, 893.7489025460932, 615.0, 920.0]}, {"path": [601.9734345351044, 1520.7865275142312, 659.0, 1534.0]}, {"path": [602.4347826086956, 846.8260869565217, 608.0, 838.0]}, {"path": [608.2162162162163, 724.2432432432432, 616.0, 733.0]}, {"path": [614.0, 1617.0, 639.7894736842106, 1677.7894736842106]}, {"path": [614.0, 1617.0, 659.0, 1534.0]}, {"path": [614.0, 1617.0, 705.0, 1615.0]}, {"path": [615.0, 920.0, 624.2758620689655, 924.6896551724137]}, {"path": [615.0, 920.0, 630.24, 953.3199999999999]}, {"path": [616.0, 733.0, 625.3972602739726, 735.013698630137]}, {"path": [616.0, 733.0, 630.0, 717.9433962264152]}, {"path": [616.3, 551.1, 631.0, 477.0]}, {"path": [617.1269296740995, 1400.3962264150955, 676.0, 1375.0]}, {"path": [622.0, 1334.0, 630.2307692307693, 1344.1538461538462]}, {"path": [622.0, 1334.0, 648.607329842932, 1334.916230366492]}, {"path": [622.0, 1751.0, 639.3333333333334, 1740.3333333333333]}, {"path": [624.2758620689655, 924.6896551724137, 666.0, 941.0]}, {"path": [625.3972602739726, 735.013698630137, 658.0, 742.0]}, {"path": [625.4074074074078, 601.5555555555557, 722.0, 584.0]}, {"path": [626.0, 1453.0, 659.0, 1534.0]}, {"path": [626.0, 1453.0, 676.0, 1375.0]}, {"path": [626.0, 1453.0, 708.0, 1457.0]}, {"path": [630.0, 717.9433962264152, 669.0, 676.0]}, {"path": [630.2307692307693, 1344.1538461538462, 676.0, 1375.0]}, {"path": [630.24, 953.3199999999999, 646.0, 963.0]}, {"path": [631.0, 477.0, 722.0, 584.0]}, {"path": [631.0, 477.0, 730.0, 415.0]}, {"path": [631.0, 477.0, 734.0, 511.0]}, {"path": [632.446732318709, 761.2524619516557, 658.0, 742.0]}, {"path": [637.0869565217391, 1108.8260869565217, 658.0, 1067.0]}, {"path": [639.3333333333334, 1740.3333333333333, 642.0, 1683.0]}, {"path": [639.7894736842106, 1677.7894736842106, 642.0, 1683.0]}, {"path": [640.7612716763006, 639.7760528488852, 669.0, 676.0]}, {"path": [642.0, 1683.0, 690.2133168927251, 1698.8076448828606]}, {"path": [642.6666666666666, 854.3333333333334, 687.0, 825.0]}, {"path": [646.0, 963.0, 657.9139344262296, 966.7377049180327]}, {"path": [646.0, 963.0, 666.0, 941.0]}, {"path": [648.607329842932, 1334.916230366492, 703.0, 1337.0]}, {"path": [651.1557831839522, 1263.6683738796416, 711.0, 1229.0]}, {"path": [651.6470588235294, 806.0588235294118, 687.0, 825.0]}, {"path": [653.9553687356967, 1040.099023866237, 658.0, 1067.0]}, {"path": [653.9553687356967, 1040.099023866237, 697.0, 979.0]}, {"path": [657.9139344262296, 966.7377049180327, 697.0, 979.0]}, {"path": [658.0, 742.0, 669.0, 676.0]}, {"path": [658.0, 742.0, 687.0, 825.0]}, {"path": [658.0, 742.0, 741.0, 747.0]}, {"path": [658.0, 1067.0, 681.0, 1116.0]}, {"path": [658.0, 1067.0, 684.0, 1062.4117647058824]}, {"path": [659.0, 1534.0, 705.0, 1615.0]}, {"path": [659.0, 1534.0, 708.0, 1457.0]}, {"path": [659.0, 1534.0, 750.0, 1540.0]}, {"path": [663.0, 17.0, 758.0, 112.0]}, {"path": [663.0, 17.0, 837.0, 15.0]}, {"path": [666.0, 941.0, 706.0, 911.0]}, {"path": [669.0, 676.0, 722.0, 584.0]}, {"path": [669.0, 676.0, 741.0, 747.0]}, {"path": [669.0, 676.0, 828.0, 679.0]}, {"path": [673.8994082840237, 896.5029585798816, 706.0, 911.0]}, {"path": [676.0, 1375.0, 708.0, 1457.0]}, {"path": [676.0, 1375.0, 708.8113207547167, 1354.6037735849056]}, {"path": [676.0, 1375.0, 767.0, 1402.0]}, {"path": [681.0, 1116.0, 694.2, 1159.6]}, {"path": [681.0, 1116.0, 718.0, 1119.0]}, {"path": [681.0, 1222.0, 711.0, 1229.0]}, {"path": [684.0, 232.0, 730.0, 415.0]}, {"path": [684.0, 232.0, 758.0, 112.0]}, {"path": [684.0, 232.0, 810.0, 309.0]}, {"path": [684.0, 232.0, 883.0, 202.0]}, {"path": [684.0, 1062.4117647058824, 726.0, 1055.0]}, {"path": [687.0, 825.0, 706.0, 911.0]}, {"path": [687.0, 825.0, 741.0, 747.0]}, {"path": [687.0, 825.0, 774.0, 843.0]}, {"path": [690.2133168927251, 1698.8076448828606, 703.0, 1703.0]}, {"path": [694.2, 1159.6, 711.0, 1229.0]}, {"path": [697.0, 979.0, 704.1652754590986, 976.6527545909851]}, {"path": [697.0, 979.0, 708.0, 1001.6666666666667]}, {"path": [703.0, 1337.0, 713.0, 1352.0]}, {"path": [703.0, 1703.0, 703.7490774907749, 1670.040590405904]}, {"path": [703.0, 1703.0, 708.0, 1740.0]}, {"path": [703.0, 1703.0, 722.0, 1697.9811320754718]}, {"path": [703.7490774907749, 1670.040590405904, 705.0, 1615.0]}, {"path": [704.1652754590986, 976.6527545909851, 758.0, 959.0]}, {"path": [705.0, 1615.0, 740.92, 1674.56]}, {"path": [705.0, 1615.0, 750.0, 1540.0]}, {"path": [705.0, 1615.0, 796.0, 1642.0]}, {"path": [706.0, 911.0, 755.0, 954.0]}, {"path": [706.0, 911.0, 757.5074626865671, 918.1044776119404]}, {"path": [706.0, 911.0, 774.0, 843.0]}, {"path": [708.0, 1001.6666666666667, 710.4987212276214, 1014.3759590792838]}, {"path": [708.0, 1001.6666666666667, 720.0, 1002.0]}, {"path": [708.0, 1457.0, 750.0, 1540.0]}, {"path": [708.0, 1457.0, 767.0, 1402.0]}, {"path": [708.0, 1457.0, 784.0, 1469.0]}, {"path": [708.0, 1740.0, 726.0, 1776.0]}, {"path": [708.8113207547167, 1354.6037735849056, 713.0, 1352.0]}, {"path": [710.4987212276214, 1014.3759590792838, 726.0, 1055.0]}, {"path": [711.0, 1229.0, 712.0478558110626, 1293.4431323803603]}, {"path": [711.0, 1229.0, 741.9381538461539, 1170.229094017094]}, {"path": [711.0, 1229.0, 754.1881188118813, 1210.8811881188128]}, {"path": [711.0, 1229.0, 777.0, 1264.0625]}, {"path": [712.0478558110626, 1293.4431323803603, 713.0, 1352.0]}, {"path": [713.0, 1352.0, 731.1343283582091, 1368.7910447761194]}, {"path": [713.0, 1352.0, 765.3414634146342, 1286.9268292682927]}, {"path": [713.0, 1352.0, 791.4705882352941, 1332.8823529411766]}, {"path": [718.0, 1119.0, 787.0, 1103.0]}, {"path": [720.0, 1002.0, 745.0, 981.0]}, {"path": [722.0, 584.0, 734.0, 511.0]}, {"path": [722.0, 584.0, 821.0, 544.0]}, {"path": [722.0, 584.0, 828.0, 679.0]}, {"path": [722.0, 1697.9811320754718, 754.0, 1692.0]}, {"path": [726.0, 1055.0, 734.532934131737, 1021.7664670658685]}, {"path": [726.0, 1055.0, 748.362763915547, 1072.5969289827253]}, {"path": [726.0, 1776.0, 754.744827586207, 1784.9379310344827]}, {"path": [730.0, 415.0, 734.0, 511.0]}, {"path": [730.0, 415.0, 810.0, 309.0]}, {"path": [730.0, 415.0, 821.0, 544.0]}, {"path": [730.0, 415.0, 844.0, 433.0]}, {"path": [731.1343283582091, 1368.7910447761194, 767.0, 1402.0]}, {"path": [734.0, 511.0, 821.0, 544.0]}, {"path": [734.532934131737, 1021.7664670658685, 745.0, 981.0]}, {"path": [740.92, 1674.56, 754.0, 1692.0]}, {"path": [741.0, 747.0, 774.0, 843.0]}, {"path": [741.0, 747.0, 828.0, 679.0]}, {"path": [741.0, 747.0, 842.0, 771.0]}, {"path": [741.9381538461539, 1170.229094017094, 745.0, 1148.0]}, {"path": [745.0, 981.0, 748.8095238095239, 973.0]}, {"path": [745.0, 981.0, 774.0, 1029.0]}, {"path": [745.0, 981.0, 781.0, 996.0]}, {"path": [745.0, 1148.0, 787.0, 1103.0]}, {"path": [748.362763915547, 1072.5969289827253, 787.0, 1103.0]}, {"path": [748.8095238095239, 973.0, 758.0, 959.0]}, {"path": [750.0, 1540.0, 784.0, 1469.0]}, {"path": [750.0, 1540.0, 796.0, 1642.0]}, {"path": [750.0, 1540.0, 796.3291139240507, 1564.451476793249]}, {"path": [754.0, 1692.0, 757.7427385892115, 1686.9522821576763]}, {"path": [754.0, 1692.0, 760.0, 1732.0]}, {"path": [754.0, 1692.0, 781.2352941176471, 1693.9411764705883]}, {"path": [754.1881188118813, 1210.8811881188128, 809.0, 1187.0]}, {"path": [754.744827586207, 1784.9379310344827, 786.0, 1767.0]}, {"path": [755.0, 954.0, 758.0, 959.0]}, {"path": [757.5074626865671, 918.1044776119404, 764.0, 919.0]}, {"path": [757.7427385892115, 1686.9522821576763, 796.0, 1642.0]}, {"path": [758.0, 112.0, 837.0, 15.0]}, {"path": [758.0, 112.0, 883.0, 202.0]}, {"path": [758.0, 112.0, 1019.0, 113.0]}, {"path": [758.0, 112.0, 1047.0, 15.0]}, {"path": [758.0, 959.0, 764.0, 954.0]}, {"path": [760.0, 1732.0, 766.3076923076923, 1747.3076923076924]}, {"path": [764.0, 919.0, 772.1176470588236, 913.5882352941178]}, {"path": [764.0, 919.0, 780.0, 940.0]}, {"path": [764.0, 954.0, 780.0, 940.0]}, {"path": [765.3414634146342, 1286.9268292682927, 807.0, 1280.0]}, {"path": [766.3076923076923, 1747.3076923076924, 786.0, 1767.0]}, {"path": [767.0, 1402.0, 784.0, 1469.0]}, {"path": [767.0, 1402.0, 805.002493765586, 1345.9501246882794]}, {"path": [767.0, 1402.0, 821.1206896551726, 1439.9482758620693]}, {"path": [767.0, 1402.0, 863.0, 1360.0]}, {"path": [772.0, 1076.0, 774.9411764705883, 1051.764705882353]}, {"path": [772.0, 1076.0, 787.0, 1103.0]}, {"path": [772.1176470588236, 913.5882352941178, 779.0, 909.0]}, {"path": [774.0, 843.0, 777.1847133757962, 885.0382165605095]}, {"path": [774.0, 843.0, 842.0, 771.0]}, {"path": [774.0, 843.0, 880.0, 833.0]}, {"path": [774.0, 1029.0, 774.9411764705883, 1051.764705882353]}, {"path": [774.0, 1029.0, 791.9570815450645, 1052.5536480686694]}, {"path": [777.0, 1264.0625, 807.0, 1280.0]}, {"path": [777.1847133757962, 885.0382165605095, 779.0, 909.0]}, {"path": [779.0, 909.0, 785.0, 923.0]}, {"path": [780.0, 940.0, 804.0, 960.0]}, {"path": [781.0, 996.0, 798.0, 989.0]}, {"path": [781.0, 996.0, 805.1707317073171, 998.4634146341457]}, {"path": [781.2352941176471, 1693.9411764705883, 848.0, 1705.0]}, {"path": [784.0, 1469.0, 812.1999999999999, 1494.6]}, {"path": [784.0, 1469.0, 822.5, 1448.0]}, {"path": [784.0, 1469.0, 831.0, 1481.0]}, {"path": [785.0, 923.0, 814.0, 924.0]}, {"path": [786.0, 1767.0, 841.0, 1757.0]}, {"path": [787.0, 1103.0, 793.0749389730186, 1176.1400488215852]}, {"path": [787.0, 1103.0, 804.0270270270271, 1105.4324324324325]}, {"path": [789.0, 1236.0, 790.0, 1244.3333333333333]}, {"path": [789.0, 1236.0, 809.0, 1187.0]}, {"path": [790.0, 1244.3333333333333, 807.0, 1280.0]}, {"path": [791.4705882352941, 1332.8823529411766, 817.0, 1326.0]}, {"path": [791.9570815450645, 1052.5536480686694, 829.0, 1109.0]}, {"path": [793.0749389730186, 1176.1400488215852, 809.0, 1187.0]}, {"path": [796.0, 1642.0, 805.8589884551952, 1591.2147471137987]}, {"path": [796.0, 1642.0, 822.4, 1680.8]}, {"path": [796.0, 1642.0, 887.0, 1613.0]}, {"path": [796.0, 1642.0, 908.0, 1687.0]}, {"path": [796.3291139240507, 1564.451476793249, 822.0, 1578.0]}, {"path": [798.0, 989.0, 804.0, 960.0]}, {"path": [804.0, 960.0, 810.2254428341383, 971.1900161030596]}, {"path": [804.0270270270271, 1105.4324324324325, 829.0, 1109.0]}, {"path": [805.002493765586, 1345.9501246882794, 817.0, 1326.0]}, {"path": [805.1707317073171, 998.4634146341457, 826.0, 989.0]}, {"path": [805.8589884551952, 1591.2147471137987, 822.0, 1578.0]}, {"path": [807.0, 1280.0, 820.304347826087, 1277.6521739130435]}, {"path": [807.0, 1280.0, 822.6071428571431, 1295.1964285714284]}, {"path": [809.0, 1187.0, 816.1428571428578, 1159.1428571428569]}, {"path": [809.0, 1187.0, 819.0486486486487, 1233.908108108108]}, {"path": [809.0, 1187.0, 833.565683646113, 1163.3914209115283]}, {"path": [809.0, 1187.0, 837.1404958677687, 1179.4958677685952]}, {"path": [810.0, 309.0, 844.0, 433.0]}, {"path": [810.0, 309.0, 883.0, 202.0]}, {"path": [810.0, 309.0, 977.0, 342.0]}, {"path": [810.2254428341383, 971.1900161030596, 826.0, 989.0]}, {"path": [812.1999999999999, 1494.6, 828.5, 1536.0]}, {"path": [814.0, 924.0, 820.2061855670103, 941.5360824742268]}, {"path": [814.0, 924.0, 838.0, 910.0]}, {"path": [816.1428571428578, 1159.1428571428569, 829.0, 1109.0]}, {"path": [817.0, 1326.0, 845.0, 1317.0]}, {"path": [819.0486486486487, 1233.908108108108, 836.0, 1261.0]}, {"path": [820.2061855670103, 941.5360824742268, 826.0, 989.0]}, {"path": [820.304347826087, 1277.6521739130435, 858.0, 1271.0]}, {"path": [821.0, 544.0, 828.0, 679.0]}, {"path": [821.0, 544.0, 844.0, 433.0]}, {"path": [821.0, 544.0, 867.0, 605.0819672131148]}, {"path": [821.0, 544.0, 877.8470588235293, 528.188235294118]}, {"path": [821.1206896551726, 1439.9482758620693, 828.0, 1445.0]}, {"path": [822.0, 1578.0, 828.5, 1536.0]}, {"path": [822.0, 1578.0, 840.9692307692309, 1584.553846153846]}, {"path": [822.0, 1578.0, 844.1760000000002, 1570.0319999999992]}, {"path": [822.4, 1680.8, 848.0, 1705.0]}, {"path": [822.5, 1448.0, 828.0, 1445.0]}, {"path": [822.6071428571431, 1295.1964285714284, 845.0, 1317.0]}, {"path": [826.0, 989.0, 827.2, 1038.6]}, {"path": [826.0, 989.0, 865.0749826987611, 1023.4167243374627]}, {"path": [826.0, 989.0, 881.3668639053254, 939.3195266272189]}, {"path": [827.2, 1038.6, 829.0, 1109.0]}, {"path": [828.0, 679.0, 842.0, 771.0]}, {"path": [828.0, 679.0, 876.6470588235295, 630.3529411764706]}, {"path": [828.0, 679.0, 879.0, 754.0]}, {"path": [828.0, 679.0, 894.0, 707.0]}, {"path": [828.0, 679.0, 931.0, 679.0]}, {"path": [828.0, 1445.0, 831.3333333333333, 1474.9999999999998]}, {"path": [828.0, 1445.0, 842.3065693430658, 1410.2554744525548]}, {"path": [828.0, 1445.0, 847.0, 1455.0]}, {"path": [828.5, 1536.0, 831.0, 1504.5]}, {"path": [829.0, 1109.0, 843.8426763110303, 1110.0415913200725]}, {"path": [829.0, 1109.0, 862.6885245901641, 1042.426229508197]}, {"path": [831.0, 1481.0, 831.3333333333333, 1474.9999999999998]}, {"path": [831.0, 1481.0, 831.6153232758272, 1485.4835252463104]}, {"path": [831.0, 1481.0, 876.0, 1473.0]}, {"path": [831.0, 1504.5, 831.6153232758272, 1485.4835252463104]}, {"path": [831.0, 1504.5, 851.3185840707965, 1491.778761061947]}, {"path": [833.565683646113, 1163.3914209115283, 886.0, 1113.0]}, {"path": [836.0, 1261.0, 858.0, 1271.0]}, {"path": [837.0, 15.0, 1047.0, 15.0]}, {"path": [837.1404958677687, 1179.4958677685952, 854.0, 1175.0]}, {"path": [838.0, 910.0, 923.0, 928.0]}, {"path": [840.9692307692309, 1584.553846153846, 887.0, 1613.0]}, {"path": [841.0, 1757.0, 844.5228758169934, 1730.8300653594772]}, {"path": [841.0, 1757.0, 859.9999999999999, 1745.9999999999998]}, {"path": [841.0, 1757.0, 881.0, 1780.0]}, {"path": [842.0, 771.0, 879.0, 754.0]}, {"path": [842.0, 771.0, 880.0, 833.0]}, {"path": [842.3065693430658, 1410.2554744525548, 863.0, 1360.0]}, {"path": [843.8426763110303, 1110.0415913200725, 886.0, 1113.0]}, {"path": [844.0, 433.0, 977.0, 342.0]}, {"path": [844.1760000000002, 1570.0319999999992, 949.0, 1528.0]}, {"path": [844.5228758169934, 1730.8300653594772, 848.0, 1705.0]}, {"path": [845.0, 1317.0, 847.0799999999999, 1323.44]}, {"path": [845.0, 1317.0, 852.3100303951373, 1291.1337386018233]}, {"path": [845.0, 1317.0, 862.6456692913387, 1326.3543307086613]}, {"path": [845.0, 1317.0, 900.0, 1309.0]}, {"path": [847.0, 1455.0, 876.0, 1473.0]}, {"path": [847.0799999999999, 1323.44, 863.0, 1360.0]}, {"path": [848.0, 1705.0, 854.2191780821918, 1697.9178082191781]}, {"path": [851.3185840707965, 1491.778761061947, 876.0, 1473.0]}, {"path": [852.3100303951373, 1291.1337386018233, 858.0, 1271.0]}, {"path": [854.0, 1175.0, 862.5647058823542, 1158.405882352941]}, {"path": [854.0, 1175.0, 863.0, 1221.0]}, {"path": [854.0, 1175.0, 886.7457627118642, 1181.822033898305]}, {"path": [854.0, 1175.0, 887.8461538461538, 1167.6153846153848]}, {"path": [854.2191780821918, 1697.9178082191781, 908.0, 1687.0]}, {"path": [858.0, 1271.0, 860.0540540540541, 1247.3243243243244]}, {"path": [858.0, 1271.0, 891.7949228380969, 1286.8759747968222]}, {"path": [858.0, 1271.0, 895.0, 1273.0]}, {"path": [858.0, 1271.0, 917.0838332288776, 1233.934133166204]}, {"path": [859.9999999999999, 1745.9999999999998, 908.0, 1687.0]}, {"path": [860.0540540540541, 1247.3243243243244, 863.0, 1221.0]}, {"path": [862.5647058823542, 1158.405882352941, 886.0, 1113.0]}, {"path": [862.6456692913387, 1326.3543307086613, 928.0, 1361.0]}, {"path": [862.6885245901641, 1042.426229508197, 877.0, 1036.0]}, {"path": [863.0, 1360.0, 876.0, 1473.0]}, {"path": [863.0, 1360.0, 890.0, 1360.0]}, {"path": [865.0749826987611, 1023.4167243374627, 877.0, 1036.0]}, {"path": [867.0, 605.0819672131148, 882.0, 625.0]}, {"path": [876.0, 1473.0, 948.047619047619, 1460.047619047619]}, {"path": [876.0, 1473.0, 949.0, 1528.0]}, {"path": [876.6470588235295, 630.3529411764706, 882.0, 625.0]}, {"path": [877.0, 1036.0, 879.2198581560281, 1054.9921197793537]}, {"path": [877.0, 1036.0, 909.1538461538462, 958.2307692307693]}, {"path": [877.0, 1036.0, 932.0923076923077, 1050.7384615384615]}, {"path": [877.0, 1036.0, 960.2819145234444, 1017.5691487140666]}, {"path": [877.8470588235293, 528.188235294118, 885.0, 526.0]}, {"path": [879.0, 754.0, 880.0, 833.0]}, {"path": [879.0, 754.0, 894.0, 707.0]}, {"path": [879.0, 754.0, 913.2, 758.4]}, {"path": [879.2198581560281, 1054.9921197793537, 886.0, 1113.0]}, {"path": [880.0, 833.0, 903.0, 881.0]}, {"path": [880.0, 833.0, 922.2359550561798, 860.9775280898876]}, {"path": [881.0, 1780.0, 919.0, 1801.0]}, {"path": [881.3668639053254, 939.3195266272189, 923.0, 928.0]}, {"path": [882.0, 625.0, 913.0543130990416, 651.3578274760383]}, {"path": [882.0, 625.0, 925.9384615384615, 525.4923076923077]}, {"path": [882.0, 625.0, 937.8897637795276, 599.2047244094489]}, {"path": [883.0, 202.0, 977.0, 342.0]}, {"path": [883.0, 202.0, 1019.0, 113.0]}, {"path": [883.0, 202.0, 1079.0, 204.0]}, {"path": [885.0, 526.0, 910.0, 501.0]}, {"path": [885.0, 526.0, 924.0, 516.0]}, {"path": [886.0, 1113.0, 911.4254143646408, 1125.3867403314916]}, {"path": [886.7457627118642, 1181.822033898305, 950.0, 1195.0]}, {"path": [887.0, 1613.0, 908.0, 1687.0]}, {"path": [887.0, 1613.0, 949.0, 1528.0]}, {"path": [887.0, 1613.0, 975.0, 1616.0]}, {"path": [887.8461538461538, 1167.6153846153848, 964.0, 1151.0]}, {"path": [890.0, 1360.0, 928.0, 1361.0]}, {"path": [891.7949228380969, 1286.8759747968222, 937.0, 1321.0]}, {"path": [894.0, 707.0, 927.0, 725.0]}, {"path": [894.0, 707.0, 931.0, 679.0]}, {"path": [895.0, 1273.0, 904.2804878048781, 1278.5243902439024]}, {"path": [900.0, 1309.0, 937.0, 1321.0]}, {"path": [903.0, 881.0, 923.0, 928.0]}, {"path": [904.2804878048781, 1278.5243902439024, 913.0, 1283.0]}, {"path": [908.0, 1687.0, 919.0, 1801.0]}, {"path": [908.0, 1687.0, 950.7585924713583, 1672.5548281505728]}, {"path": [908.0, 1687.0, 975.0, 1616.0]}, {"path": [908.0, 1687.0, 1008.0, 1752.0]}, {"path": [909.1538461538462, 958.2307692307693, 923.0, 928.0]}, {"path": [910.0, 501.0, 927.0, 502.0]}, {"path": [911.4254143646408, 1125.3867403314916, 964.0, 1151.0]}, {"path": [913.0, 1283.0, 971.0, 1288.0]}, {"path": [913.0543130990416, 651.3578274760383, 931.0, 679.0]}, {"path": [913.2, 758.4, 963.0, 748.0]}, {"path": [917.0838332288776, 1233.934133166204, 950.0, 1195.0]}, {"path": [919.0, 1801.0, 1008.0, 1752.0]}, {"path": [919.0, 1801.0, 1064.0, 1806.0]}, {"path": [922.2359550561798, 860.9775280898876, 948.0, 871.0]}, {"path": [923.0, 928.0, 937.2818791946308, 892.5973154362416]}, {"path": [923.0, 928.0, 964.0484242890088, 963.3328209069946]}, {"path": [923.0, 928.0, 969.6554404145078, 934.0906735751296]}, {"path": [924.0, 516.0, 925.9384615384615, 525.4923076923077]}, {"path": [924.0, 516.0, 949.3333333333334, 507.0]}, {"path": [927.0, 502.0, 944.0, 494.0]}, {"path": [927.0, 725.0, 966.0, 708.0]}, {"path": [928.0, 1361.0, 933.1107644305771, 1338.2854914196566]}, {"path": [928.0, 1361.0, 942.5458015267172, 1398.740458015267]}, {"path": [931.0, 679.0, 994.0, 644.0]}, {"path": [932.0923076923077, 1050.7384615384615, 964.0, 1151.0]}, {"path": [933.1107644305771, 1338.2854914196566, 937.0, 1321.0]}, {"path": [937.0, 1321.0, 958.3302752293578, 1299.1009174311926]}, {"path": [937.0, 1321.0, 966.0, 1331.4021739130435]}, {"path": [937.0, 1321.0, 975.3333333333333, 1371.6666666666667]}, {"path": [937.2818791946308, 892.5973154362416, 948.0, 871.0]}, {"path": [937.8897637795276, 599.2047244094489, 1012.0, 565.0]}, {"path": [939.0, 483.0, 941.0, 468.0]}, {"path": [939.0, 483.0, 944.0, 494.0]}, {"path": [940.0, 819.0, 943.0668069709053, 786.763712893221]}, {"path": [940.0, 819.0, 948.0, 871.0]}, {"path": [941.0, 468.0, 1015.3178682044925, 408.71668387365503]}, {"path": [942.5458015267172, 1398.740458015267, 965.0, 1457.0]}, {"path": [943.0668069709053, 786.763712893221, 963.0, 748.0]}, {"path": [948.0, 871.0, 967.1111111111111, 841.2222222222223]}, {"path": [948.0, 871.0, 978.8643815201194, 894.3919523099851]}, {"path": [948.047619047619, 1460.047619047619, 965.0, 1457.0]}, {"path": [949.0, 1528.0, 960.746835443038, 1475.873417721519]}, {"path": [949.0, 1528.0, 975.0, 1616.0]}, {"path": [949.0, 1528.0, 1018.3277310924368, 1515.9831932773109]}, {"path": [949.0, 1528.0, 1041.0, 1567.0]}, {"path": [949.3333333333334, 507.0, 989.3333333333333, 476.33333333333337]}, {"path": [950.0, 1195.0, 956.0666666666666, 1175.9333333333334]}, {"path": [950.0, 1195.0, 961.424, 1245.5919999999996]}, {"path": [950.0, 1195.0, 1005.364534286284, 1189.8817809542797]}, {"path": [950.0, 1195.0, 1012.135135135135, 1230.8108108108108]}, {"path": [950.0, 1195.0, 1017.0, 1212.0]}, {"path": [950.7585924713583, 1672.5548281505728, 987.0, 1652.0]}, {"path": [956.0666666666666, 1175.9333333333334, 964.0, 1151.0]}, {"path": [958.3302752293578, 1299.1009174311926, 971.0, 1288.0]}, {"path": [960.2819145234444, 1017.5691487140666, 1002.0, 996.0]}, {"path": [960.746835443038, 1475.873417721519, 965.0, 1457.0]}, {"path": [961.424, 1245.5919999999996, 971.0, 1288.0]}, {"path": [963.0, 748.0, 964.333333333334, 737.0000000000001]}, {"path": [963.0, 748.0, 969.632075471698, 765.1037735849056]}, {"path": [963.0, 748.0, 980.5675675675675, 737.5945945945945]}, {"path": [964.0, 1151.0, 986.0715307582259, 1060.9713876967096]}, {"path": [964.0, 1151.0, 1010.9565217391305, 1156.2173913043478]}, {"path": [964.0, 1151.0, 1016.6041666666667, 1102.6041666666667]}, {"path": [964.0484242890088, 963.3328209069946, 1002.0, 996.0]}, {"path": [964.333333333334, 737.0000000000001, 965.0, 721.0]}, {"path": [965.0, 721.0, 965.1237113402061, 712.2783505154639]}, {"path": [965.0, 721.0, 979.2921348314607, 722.0674157303372]}, {"path": [965.0, 1457.0, 977.3181757850873, 1398.474744793372]}, {"path": [965.0, 1457.0, 990.5482866043612, 1482.1152647975077]}, {"path": [965.1237113402061, 712.2783505154639, 966.0, 708.0]}, {"path": [966.0, 708.0, 1005.0, 721.0]}, {"path": [966.0, 1331.4021739130435, 1029.0, 1354.0]}, {"path": [967.1111111111111, 841.2222222222223, 991.0, 804.0]}, {"path": [969.632075471698, 765.1037735849056, 980.0, 778.0]}, {"path": [969.6554404145078, 934.0906735751296, 1043.0, 943.0]}, {"path": [971.0, 1288.0, 974.6418604651162, 1292.1441860465115]}, {"path": [971.0, 1288.0, 989.0, 1273.0]}, {"path": [974.6418604651162, 1292.1441860465115, 1029.0, 1354.0]}, {"path": [975.0, 1616.0, 985.4285714285716, 1647.2857142857144]}, {"path": [975.0, 1616.0, 1041.0, 1567.0]}, {"path": [975.0, 1616.0, 1082.0, 1645.0]}, {"path": [975.3333333333333, 1371.6666666666667, 977.3181757850873, 1398.474744793372]}, {"path": [975.3333333333333, 1371.6666666666667, 1008.8, 1428.4]}, {"path": [977.0, 342.0, 1026.5, 389.5]}, {"path": [977.0, 342.0, 1079.0, 204.0]}, {"path": [978.8643815201194, 894.3919523099851, 1043.0, 943.0]}, {"path": [979.2921348314607, 722.0674157303372, 1005.0, 721.0]}, {"path": [980.0, 778.0, 991.0, 804.0]}, {"path": [980.0, 778.0, 1005.0, 721.0]}, {"path": [980.5675675675675, 737.5945945945945, 1005.0, 721.0]}, {"path": [985.4285714285716, 1647.2857142857144, 987.0, 1652.0]}, {"path": [986.0715307582259, 1060.9713876967096, 1002.0, 996.0]}, {"path": [987.0, 1652.0, 998.6666666666667, 1667.0]}, {"path": [989.0, 1273.0, 1010.3795620437957, 1268.2846715328467]}, {"path": [989.3333333333333, 476.33333333333337, 999.1276076123368, 497.4062462410873]}, {"path": [989.3333333333333, 476.33333333333337, 1035.3333333333333, 455.66666666666663]}, {"path": [990.5482866043612, 1482.1152647975077, 1010.0, 1494.0]}, {"path": [991.0, 804.0, 999.7019867549702, 797.7152317880792]}, {"path": [991.0, 804.0, 1045.0, 821.0]}, {"path": [994.0, 644.0, 1005.0, 721.0]}, {"path": [994.0, 644.0, 1008.5039370078739, 580.3438320209974]}, {"path": [994.0, 644.0, 1025.0182619647353, 678.1996221662469]}, {"path": [998.6666666666667, 1667.0, 1001.8808796579106, 1702.48869883934]}, {"path": [998.6666666666667, 1667.0, 1065.28, 1669.96]}, {"path": [999.1276076123368, 497.4062462410873, 1012.0, 565.0]}, {"path": [999.7019867549702, 797.7152317880792, 1027.0, 778.0]}, {"path": [1001.8808796579106, 1702.48869883934, 1008.0, 1752.0]}, {"path": [1002.0, 996.0, 1024.7294429708236, 966.6180371352784]}, {"path": [1002.0, 996.0, 1037.4568690095846, 1032.0287539936103]}, {"path": [1002.0, 996.0, 1062.593040847201, 1002.8623298033287]}, {"path": [1005.0, 721.0, 1026.5344827586207, 698.0862068965517]}, {"path": [1005.0, 721.0, 1040.0, 774.0]}, {"path": [1005.364534286284, 1189.8817809542797, 1054.0, 1161.0]}, {"path": [1008.0, 1752.0, 1064.0, 1806.0]}, {"path": [1008.0, 1752.0, 1125.0, 1700.0]}, {"path": [1008.5039370078739, 580.3438320209974, 1012.0, 565.0]}, {"path": [1008.8, 1428.4, 1027.0, 1493.0]}, {"path": [1010.0, 1494.0, 1024.0, 1515.0]}, {"path": [1010.3795620437957, 1268.2846715328467, 1026.0, 1280.0]}, {"path": [1010.9565217391305, 1156.2173913043478, 1054.0, 1161.0]}, {"path": [1012.0, 565.0, 1030.03208257554, 608.1336906927029]}, {"path": [1012.0, 565.0, 1040.922206506365, 578.8500707213578]}, {"path": [1012.135135135135, 1230.8108108108108, 1035.0, 1257.0]}, {"path": [1015.3178682044925, 408.71668387365503, 1034.0, 398.0]}, {"path": [1016.6041666666667, 1102.6041666666667, 1064.0, 1059.0]}, {"path": [1017.0, 1212.0, 1064.0, 1208.0]}, {"path": [1018.3277310924368, 1515.9831932773109, 1024.0, 1515.0]}, {"path": [1019.0, 113.0, 1047.0, 15.0]}, {"path": [1019.0, 113.0, 1079.0, 204.0]}, {"path": [1019.0, 113.0, 1201.0, 70.0]}, {"path": [1024.0, 1515.0, 1027.0, 1493.0]}, {"path": [1024.0, 1515.0, 1038.1734104046243, 1530.1271676300578]}, {"path": [1024.0, 1515.0, 1043.0, 1522.0]}, {"path": [1024.0, 1515.0, 1048.0, 1511.0]}, {"path": [1024.0, 1515.0, 1055.4268833087153, 1463.2038404726736]}, {"path": [1024.7294429708236, 966.6180371352784, 1043.0, 943.0]}, {"path": [1025.0182619647353, 678.1996221662469, 1033.0, 687.0]}, {"path": [1026.0, 1280.0, 1027.83923705722, 1325.367847411444]}, {"path": [1026.0, 1280.0, 1035.0, 1257.0]}, {"path": [1026.0, 1280.0, 1090.603839441536, 1318.5287958115184]}, {"path": [1026.0, 1280.0, 1143.494714587738, 1267.9894291754758]}, {"path": [1026.5, 389.5, 1034.0, 398.0]}, {"path": [1026.5344827586207, 698.0862068965517, 1033.0, 687.0]}, {"path": [1027.0, 778.0, 1035.5409836065573, 775.0491803278692]}, {"path": [1027.83923705722, 1325.367847411444, 1029.0, 1354.0]}, {"path": [1029.0, 1354.0, 1078.0, 1426.0]}, {"path": [1030.03208257554, 608.1336906927029, 1034.0, 660.0]}, {"path": [1033.0, 687.0, 1034.0, 660.0]}, {"path": [1033.0, 687.0, 1047.0, 646.0]}, {"path": [1033.0, 687.0, 1077.4137931034481, 706.0344827586223]}, {"path": [1033.0, 687.0, 1110.0, 626.0]}, {"path": [1034.0, 398.0, 1043.3333333333333, 392.3333333333333]}, {"path": [1034.0, 398.0, 1070.5, 422.5]}, {"path": [1035.0, 1257.0, 1043.70207253886, 1242.1321243523316]}, {"path": [1035.3333333333333, 455.66666666666663, 1065.659793814433, 457.4845360824742]}, {"path": [1035.5409836065573, 775.0491803278692, 1040.0, 774.0]}, {"path": [1037.4568690095846, 1032.0287539936103, 1064.0, 1059.0]}, {"path": [1038.1734104046243, 1530.1271676300578, 1041.0, 1567.0]}, {"path": [1039.079365079365, 846.552380952381, 1043.0, 943.0]}, {"path": [1039.079365079365, 846.552380952381, 1045.0, 821.0]}, {"path": [1040.0, 774.0, 1044.7622149837134, 772.3659066232356]}, {"path": [1040.0, 774.0, 1045.0, 821.0]}, {"path": [1040.922206506365, 578.8500707213578, 1064.0, 594.0]}, {"path": [1041.0, 1567.0, 1082.0, 1645.0]}, {"path": [1043.0, 943.0, 1067.4568081991215, 958.3909224011713]}, {"path": [1043.0, 943.0, 1083.3600000000001, 917.4800000000001]}, {"path": [1043.0, 1522.0, 1052.894099265452, 1527.9176575625988]}, {"path": [1043.3333333333333, 392.3333333333333, 1076.0, 354.3333333333333]}, {"path": [1043.70207253886, 1242.1321243523316, 1064.0, 1208.0]}, {"path": [1044.7622149837134, 772.3659066232356, 1077.0, 761.0]}, {"path": [1045.0, 821.0, 1056.3067415730338, 800.729213483146]}, {"path": [1045.0, 821.0, 1114.0, 831.0]}, {"path": [1045.0, 821.0, 1147.0, 771.0]}, {"path": [1047.0, 15.0, 1201.0, 70.0]}, {"path": [1047.0, 15.0, 1304.0, 6.0]}, {"path": [1047.0, 646.0, 1052.8238468049092, 603.2354704471718]}, {"path": [1048.0, 1511.0, 1074.0, 1514.0]}, {"path": [1052.8238468049092, 603.2354704471718, 1064.0, 594.0]}, {"path": [1052.894099265452, 1527.9176575625988, 1107.0, 1583.0]}, {"path": [1054.0, 1161.0, 1059.2525252525256, 1107.4242424242425]}, {"path": [1054.0, 1161.0, 1084.1706484641638, 1163.6621160409557]}, {"path": [1055.4268833087153, 1463.2038404726736, 1078.0, 1426.0]}, {"path": [1056.3067415730338, 800.729213483146, 1077.0, 761.0]}, {"path": [1059.2525252525256, 1107.4242424242425, 1064.0, 1059.0]}, {"path": [1062.593040847201, 1002.8623298033287, 1159.0, 1016.0]}, {"path": [1064.0, 594.0, 1083.0, 599.0]}, {"path": [1064.0, 1059.0, 1110.3600917431195, 1145.3256880733945]}, {"path": [1064.0, 1059.0, 1117.8067226890757, 1034.6453781512605]}, {"path": [1064.0, 1059.0, 1149.0, 1087.0]}, {"path": [1064.0, 1208.0, 1122.0, 1167.0]}, {"path": [1064.0, 1806.0, 1125.0, 1700.0]}, {"path": [1064.0, 1806.0, 1176.0, 1812.0]}, {"path": [1065.28, 1669.96, 1070.0, 1670.0]}, {"path": [1065.659793814433, 457.4845360824742, 1078.0, 460.0]}, {"path": [1067.4568081991215, 958.3909224011713, 1159.0, 1016.0]}, {"path": [1070.0, 1670.0, 1077.6, 1667.1999999999998]}, {"path": [1070.0, 1670.0, 1082.0, 1645.0]}, {"path": [1070.0, 1670.0, 1125.0, 1700.0]}, {"path": [1070.5, 422.5, 1078.0, 460.0]}, {"path": [1074.0, 1514.0, 1128.0, 1528.0]}, {"path": [1076.0, 354.3333333333333, 1093.0, 363.0]}, {"path": [1076.0, 354.3333333333333, 1144.0, 240.0]}, {"path": [1077.0, 761.0, 1142.0, 739.0]}, {"path": [1077.4137931034481, 706.0344827586223, 1142.0, 739.0]}, {"path": [1077.6, 1667.1999999999998, 1087.0, 1662.0]}, {"path": [1078.0, 460.0, 1083.0, 599.0]}, {"path": [1078.0, 1426.0, 1129.6382054992762, 1404.710564399421]}, {"path": [1078.0, 1426.0, 1149.0, 1468.0]}, {"path": [1079.0, 204.0, 1123.0, 230.0]}, {"path": [1079.0, 204.0, 1201.0, 70.0]}, {"path": [1083.0, 599.0, 1122.4717741935483, 603.0483870967741]}, {"path": [1083.0, 599.0, 1142.0, 487.0]}, {"path": [1083.3600000000001, 917.4800000000001, 1153.3333333333333, 872.3333333333334]}, {"path": [1084.1706484641638, 1163.6621160409557, 1122.0, 1167.0]}, {"path": [1087.0, 1662.0, 1114.0, 1622.0]}, {"path": [1090.603839441536, 1318.5287958115184, 1192.0, 1379.0]}, {"path": [1093.0, 363.0, 1138.0, 402.0]}, {"path": [1107.0, 1583.0, 1114.0, 1622.0]}, {"path": [1107.0, 1583.0, 1121.9840000000002, 1542.088]}, {"path": [1110.0, 626.0, 1132.0, 620.0]}, {"path": [1110.3600917431195, 1145.3256880733945, 1122.0, 1167.0]}, {"path": [1114.0, 831.0, 1138.6666666666667, 854.3333333333334]}, {"path": [1114.0, 831.0, 1147.0, 771.0]}, {"path": [1114.0, 831.0, 1216.0, 795.0]}, {"path": [1114.0, 1622.0, 1121.0000000000002, 1623.9999999999998]}, {"path": [1117.8067226890757, 1034.6453781512605, 1159.0, 1016.0]}, {"path": [1121.0000000000002, 1623.9999999999998, 1210.0, 1623.0]}, {"path": [1121.9840000000002, 1542.088, 1128.0, 1528.0]}, {"path": [1122.0, 1167.0, 1146.5, 1244.5]}, {"path": [1122.0, 1167.0, 1168.2, 1134.6000000000001]}, {"path": [1122.0, 1167.0, 1190.0, 1191.0833333333335]}, {"path": [1122.4717741935483, 603.0483870967741, 1148.0, 596.3333333333334]}, {"path": [1123.0, 230.0, 1144.0, 240.0]}, {"path": [1125.0, 1700.0, 1176.0, 1812.0]}, {"path": [1125.0, 1700.0, 1210.0, 1623.0]}, {"path": [1125.0, 1700.0, 1255.0, 1718.0]}, {"path": [1128.0, 1528.0, 1132.876712328767, 1511.3287671232877]}, {"path": [1128.0, 1528.0, 1166.0, 1543.0]}, {"path": [1129.6382054992762, 1404.710564399421, 1192.0, 1379.0]}, {"path": [1132.0, 620.0, 1150.6666666666667, 621.6666666666666]}, {"path": [1132.876712328767, 1511.3287671232877, 1149.0, 1468.0]}, {"path": [1138.0, 402.0, 1184.0, 456.0]}, {"path": [1138.6666666666667, 854.3333333333334, 1153.3333333333333, 872.3333333333334]}, {"path": [1142.0, 487.0, 1184.0, 456.0]}, {"path": [1142.0, 739.0, 1143.2446183953034, 749.7866927592954]}, {"path": [1142.0, 739.0, 1146.0, 664.3333333333334]}, {"path": [1143.2446183953034, 749.7866927592954, 1147.0, 771.0]}, {"path": [1143.494714587738, 1267.9894291754758, 1163.0, 1262.0]}, {"path": [1144.0, 240.0, 1162.0, 236.0]}, {"path": [1144.0, 240.0, 1169.0, 218.0]}, {"path": [1146.0, 605.0, 1148.0, 596.3333333333334]}, {"path": [1146.0, 605.0, 1157.3333333333333, 612.3333333333334]}, {"path": [1146.0, 664.3333333333334, 1146.0136718750014, 650.8231534090914]}, {"path": [1146.0, 664.3333333333334, 1163.5710898099596, 665.0047426605665]}, {"path": [1146.0136718750014, 650.8231534090914, 1150.6666666666667, 621.6666666666666]}, {"path": [1146.5, 1244.5, 1163.0, 1262.0]}, {"path": [1147.0, 771.0, 1190.0, 751.0]}, {"path": [1147.0, 771.0, 1216.0, 795.0]}, {"path": [1148.0, 596.3333333333334, 1188.0, 577.0]}, {"path": [1149.0, 1087.0, 1209.1111104306626, 1084.9722254543524]}, {"path": [1149.0, 1468.0, 1164.981519507186, 1434.9219712525667]}, {"path": [1149.0, 1468.0, 1197.0, 1520.0]}, {"path": [1150.6666666666667, 621.6666666666666, 1157.3333333333333, 612.3333333333334]}, {"path": [1153.3333333333333, 872.3333333333334, 1210.0, 869.0]}, {"path": [1159.0, 1016.0, 1163.211409395973, 966.8020134228187]}, {"path": [1159.0, 1016.0, 1185.2131147540983, 1004.9098360655737]}, {"path": [1159.0, 1016.0, 1193.0, 1052.0]}, {"path": [1162.0, 236.0, 1185.0, 252.0]}, {"path": [1163.0, 1262.0, 1251.0, 1257.0]}, {"path": [1163.211409395973, 966.8020134228187, 1186.0, 918.3333333333333]}, {"path": [1163.5710898099596, 665.0047426605665, 1198.0, 663.0]}, {"path": [1164.981519507186, 1434.9219712525667, 1173.0, 1415.0]}, {"path": [1166.0, 1543.0, 1169.746223564955, 1540.2205438066467]}, {"path": [1166.0, 1543.0, 1186.266009852217, 1579.8472906403938]}, {"path": [1168.0, 197.0, 1169.0, 218.0]}, {"path": [1168.0, 197.0, 1174.6341365461847, 196.09759036144578]}, {"path": [1168.0, 197.0, 1182.6746006249818, 179.33968049998543]}, {"path": [1168.2, 1134.6000000000001, 1226.0, 1106.0]}, {"path": [1169.746223564955, 1540.2205438066467, 1197.0, 1520.0]}, {"path": [1173.0, 1415.0, 1192.0, 1379.0]}, {"path": [1174.6341365461847, 196.09759036144578, 1208.0, 180.0]}, {"path": [1174.6666666666667, 519.6666666666666, 1178.0, 496.0]}, {"path": [1174.6666666666667, 519.6666666666666, 1188.0, 577.0]}, {"path": [1176.0, 1812.0, 1255.0, 1718.0]}, {"path": [1176.0, 1812.0, 1303.0, 1813.0]}, {"path": [1178.0, 496.0, 1184.0, 456.0]}, {"path": [1182.6746006249818, 179.33968049998543, 1206.0, 163.0]}, {"path": [1184.0, 456.0, 1190.4827586206898, 454.2068965517244]}, {"path": [1184.0, 456.0, 1193.053380782918, 466.4199288256227]}, {"path": [1184.0, 456.0, 1196.0, 418.0]}, {"path": [1185.0, 252.0, 1256.0, 383.0]}, {"path": [1185.2131147540983, 1004.9098360655737, 1237.0, 983.0]}, {"path": [1186.0, 918.3333333333333, 1198.5, 945.5]}, {"path": [1186.0, 918.3333333333333, 1210.0, 869.0]}, {"path": [1186.266009852217, 1579.8472906403938, 1210.0, 1623.0]}, {"path": [1188.0, 577.0, 1202.0, 607.0]}, {"path": [1188.0, 577.0, 1202.4339719029363, 552.6388250319274]}, {"path": [1190.0, 751.0, 1198.0, 663.0]}, {"path": [1190.0, 751.0, 1216.0, 795.0]}, {"path": [1190.0, 751.0, 1254.0, 705.0]}, {"path": [1190.0, 1191.0833333333335, 1218.0, 1201.0]}, {"path": [1190.4827586206898, 454.2068965517244, 1256.0, 437.0]}, {"path": [1192.0, 1379.0, 1201.109589041096, 1413.041095890411]}, {"path": [1192.0, 1379.0, 1210.039603960396, 1328.3960396039604]}, {"path": [1192.0, 1379.0, 1224.6067003212481, 1359.9017898118402]}, {"path": [1193.0, 1052.0, 1224.3371685294762, 1064.0900449265835]}, {"path": [1193.053380782918, 466.4199288256227, 1237.0, 517.0]}, {"path": [1196.0, 418.0, 1218.0849056603772, 398.952830188679]}, {"path": [1197.0, 1520.0, 1210.0, 1623.0]}, {"path": [1197.0, 1520.0, 1211.0791100123608, 1461.1990111248456]}, {"path": [1197.0, 1520.0, 1229.3846153846155, 1507.0769230769233]}, {"path": [1198.0, 663.0, 1237.0, 619.0]}, {"path": [1198.5, 945.5, 1237.0, 983.0]}, {"path": [1201.0, 70.0, 1237.1846153846154, 88.3230769230769]}, {"path": [1201.0, 70.0, 1304.0, 6.0]}, {"path": [1201.109589041096, 1413.041095890411, 1214.0, 1449.0]}, {"path": [1202.0, 607.0, 1206.2682926829268, 608.4634146341464]}, {"path": [1202.4339719029363, 552.6388250319274, 1237.0, 517.0]}, {"path": [1206.0, 163.0, 1214.0, 141.0]}, {"path": [1206.2682926829268, 608.4634146341464, 1237.0, 619.0]}, {"path": [1208.0, 180.0, 1256.0, 223.0]}, {"path": [1209.1111104306626, 1084.9722254543524, 1244.0, 1063.0]}, {"path": [1210.0, 869.0, 1210.9574468085107, 857.1914893617022]}, {"path": [1210.0, 869.0, 1220.0, 867.6666666666666]}, {"path": [1210.0, 1623.0, 1255.0, 1718.0]}, {"path": [1210.0, 1623.0, 1269.0, 1605.0]}, {"path": [1210.039603960396, 1328.3960396039604, 1251.0, 1257.0]}, {"path": [1210.9574468085107, 857.1914893617022, 1216.0, 795.0]}, {"path": [1211.0, 1403.0, 1214.0, 1449.0]}, {"path": [1211.0, 1403.0, 1230.5135135135138, 1385.9189189189192]}, {"path": [1211.0791100123608, 1461.1990111248456, 1214.0, 1449.0]}, {"path": [1214.0, 141.0, 1249.0, 111.0]}, {"path": [1214.0, 1449.0, 1220.0, 1465.0]}, {"path": [1214.0, 1449.0, 1243.9, 1426.9]}, {"path": [1216.0, 795.0, 1254.0, 705.0]}, {"path": [1216.0, 795.0, 1256.2975206611573, 826.9338842975206]}, {"path": [1216.0, 795.0, 1278.8, 766.5999999999999]}, {"path": [1218.0, 1201.0, 1221.0, 1132.0]}, {"path": [1218.0, 1201.0, 1236.2494802494784, 1231.9688149688152]}, {"path": [1218.0, 1201.0, 1279.49377593361, 1192.398340248962]}, {"path": [1218.0849056603772, 398.952830188679, 1256.0, 383.0]}, {"path": [1220.0, 867.6666666666666, 1241.3333333333333, 862.3333333333334]}, {"path": [1220.0, 1465.0, 1228.8707482993182, 1472.2380952380909]}, {"path": [1221.0, 1132.0, 1226.0, 1106.0]}, {"path": [1224.3371685294762, 1064.0900449265835, 1244.0, 1063.0]}, {"path": [1224.6067003212481, 1359.9017898118402, 1262.0, 1338.0]}, {"path": [1226.0, 1106.0, 1244.0, 1063.0]}, {"path": [1228.8707482993182, 1472.2380952380909, 1235.0, 1473.5]}, {"path": [1229.3846153846155, 1507.0769230769233, 1260.0, 1486.0]}, {"path": [1230.5135135135138, 1385.9189189189192, 1247.0, 1371.0]}, {"path": [1235.0, 1473.5, 1260.0, 1486.0]}, {"path": [1236.2494802494784, 1231.9688149688152, 1251.0, 1257.0]}, {"path": [1237.0, 517.0, 1237.0, 619.0]}, {"path": [1237.0, 517.0, 1256.0, 437.0]}, {"path": [1237.0, 517.0, 1290.1176470588236, 516.4705882352939]}, {"path": [1237.0, 517.0, 1318.0, 599.0]}, {"path": [1237.0, 619.0, 1254.0, 705.0]}, {"path": [1237.0, 619.0, 1318.0, 599.0]}, {"path": [1237.0, 983.0, 1243.6742833402477, 1059.2775238886586]}, {"path": [1237.0, 983.0, 1252.0, 972.864864864865]}, {"path": [1237.0, 983.0, 1276.0, 1002.0]}, {"path": [1237.1846153846154, 88.3230769230769, 1241.0, 93.0]}, {"path": [1241.0, 93.0, 1249.0, 111.0]}, {"path": [1241.3333333333333, 862.3333333333334, 1269.0, 837.0]}, {"path": [1243.6742833402477, 1059.2775238886586, 1244.0, 1063.0]}, {"path": [1243.9, 1426.9, 1260.0, 1415.0]}, {"path": [1244.0, 1063.0, 1271.0, 1074.0]}, {"path": [1247.0, 1371.0, 1262.0, 1338.0]}, {"path": [1249.0, 111.0, 1253.868945868946, 125.53276353276354]}, {"path": [1249.0, 111.0, 1282.0, 110.0]}, {"path": [1251.0, 1257.0, 1261.282608695652, 1332.717391304348]}, {"path": [1251.0, 1257.0, 1263.0757894736835, 1241.4021052631579]}, {"path": [1251.0, 1257.0, 1318.0, 1313.0]}, {"path": [1251.0, 1257.0, 1352.0, 1288.0]}, {"path": [1252.0, 972.864864864865, 1274.0, 958.0]}, {"path": [1253.868945868946, 125.53276353276354, 1292.0, 292.0]}, {"path": [1254.0, 705.0, 1318.0, 599.0]}, {"path": [1254.0, 705.0, 1353.0, 670.0]}, {"path": [1255.0, 1718.0, 1303.0, 1813.0]}, {"path": [1255.0, 1718.0, 1307.0060422960723, 1689.716012084592]}, {"path": [1255.0, 1718.0, 1349.0, 1778.0]}, {"path": [1256.0, 223.0, 1292.0, 292.0]}, {"path": [1256.0, 383.0, 1256.0, 391.22222222222223]}, {"path": [1256.0, 383.0, 1272.1212229732755, 310.83165465478913]}, {"path": [1256.0, 383.0, 1282.6666666666667, 349.0]}, {"path": [1256.0, 391.22222222222223, 1256.0, 437.0]}, {"path": [1256.0, 437.0, 1285.3846153846152, 419.9230769230762]}, {"path": [1256.0, 437.0, 1318.0, 337.0]}, {"path": [1256.2975206611573, 826.9338842975206, 1269.0, 837.0]}, {"path": [1260.0, 1415.0, 1260.1558441558443, 1409.0]}, {"path": [1260.0, 1415.0, 1270.9565217391305, 1413.4347826086955]}, {"path": [1260.0, 1415.0, 1289.1677852348998, 1453.41610738255]}, {"path": [1260.0, 1486.0, 1301.0, 1469.0]}, {"path": [1260.1558441558443, 1409.0, 1262.0, 1338.0]}, {"path": [1261.282608695652, 1332.717391304348, 1262.0, 1338.0]}, {"path": [1262.0, 1338.0, 1293.918215613383, 1378.7843866171004]}, {"path": [1263.0757894736835, 1241.4021052631579, 1299.0, 1195.0]}, {"path": [1269.0, 837.0, 1272.0769230769233, 911.4615384615386]}, {"path": [1269.0, 837.0, 1290.0, 827.0]}, {"path": [1269.0, 837.0, 1334.9541984732823, 830.8167938931297]}, {"path": [1269.0, 1605.0, 1278.0, 1587.0]}, {"path": [1270.9565217391305, 1413.4347826086955, 1316.0, 1407.0]}, {"path": [1271.0, 1074.0, 1301.0, 1063.0]}, {"path": [1271.0, 1074.0, 1333.1517976832915, 1142.3633820850378]}, {"path": [1272.0769230769233, 911.4615384615386, 1274.0, 958.0]}, {"path": [1272.1212229732755, 310.83165465478913, 1292.0, 292.0]}, {"path": [1274.0, 958.0, 1300.1151832460732, 980.5130890052355]}, {"path": [1274.0, 958.0, 1324.0, 940.0]}, {"path": [1274.0, 958.0, 1327.2477876106195, 901.2831858407079]}, {"path": [1276.0, 1002.0, 1307.0, 1007.0]}, {"path": [1278.0, 1587.0, 1287.36, 1595.32]}, {"path": [1278.0, 1587.0, 1288.8152240638424, 1531.513198281154]}, {"path": [1278.0, 1587.0, 1320.0, 1564.0]}, {"path": [1278.8, 766.5999999999999, 1373.0, 724.0]}, {"path": [1279.49377593361, 1192.398340248962, 1299.0, 1195.0]}, {"path": [1282.0, 110.0, 1298.0, 96.0]}, {"path": [1282.0, 110.0, 1328.0, 134.0]}, {"path": [1282.6666666666667, 349.0, 1318.0, 337.0]}, {"path": [1285.3846153846152, 419.9230769230762, 1290.0, 419.0]}, {"path": [1287.36, 1595.32, 1341.0, 1587.0]}, {"path": [1288.8152240638424, 1531.513198281154, 1301.0, 1469.0]}, {"path": [1289.1677852348998, 1453.41610738255, 1301.0, 1469.0]}, {"path": [1290.0, 419.0, 1291.6744186046508, 414.09634551495014]}, {"path": [1290.0, 419.0, 1304.5, 415.5]}, {"path": [1290.0, 419.0, 1321.3, 509.10000000000014]}, {"path": [1290.0, 827.0, 1308.0, 807.0]}, {"path": [1290.1176470588236, 516.4705882352939, 1324.0, 519.0]}, {"path": [1291.6744186046508, 414.09634551495014, 1318.0, 337.0]}, {"path": [1292.0, 292.0, 1294.5633802816901, 296.4366197183099]}, {"path": [1292.0, 292.0, 1300.0, 262.0]}, {"path": [1293.918215613383, 1378.7843866171004, 1316.0, 1407.0]}, {"path": [1294.5633802816901, 296.4366197183099, 1318.0, 337.0]}, {"path": [1298.0, 96.0, 1331.0, 78.0]}, {"path": [1299.0, 1195.0, 1335.0, 1159.0]}, {"path": [1299.0, 1195.0, 1343.9545454545453, 1222.3636363636365]}, {"path": [1299.0, 1195.0, 1383.0, 1187.0]}, {"path": [1300.0, 262.0, 1309.0, 252.0]}, {"path": [1300.1151832460732, 980.5130890052355, 1332.0, 1008.0]}, {"path": [1301.0, 1063.0, 1308.8914404759762, 1038.2391887754184]}, {"path": [1301.0, 1063.0, 1335.7949283351709, 1071.282249173098]}, {"path": [1301.0, 1469.0, 1316.0, 1451.0]}, {"path": [1301.0, 1469.0, 1343.6800195203803, 1458.1960185443613]}, {"path": [1301.0, 1469.0, 1355.181085555571, 1507.2648624242343]}, {"path": [1303.0, 1813.0, 1349.0, 1778.0]}, {"path": [1303.0, 1813.0, 1408.0, 1815.0]}, {"path": [1304.0, 6.0, 1476.0, 9.0]}, {"path": [1304.5, 415.5, 1362.2124937988935, 339.7535661133372]}, {"path": [1304.5, 415.5, 1397.0, 397.0]}, {"path": [1307.0, 1007.0, 1332.0, 1008.0]}, {"path": [1307.0060422960723, 1689.716012084592, 1312.0, 1687.0]}, {"path": [1308.0, 807.0, 1373.0, 724.0]}, {"path": [1308.8914404759762, 1038.2391887754184, 1332.0, 1008.0]}, {"path": [1309.0, 252.0, 1331.5124555160141, 269.83985765124527]}, {"path": [1312.0, 1687.0, 1331.0, 1723.0]}, {"path": [1312.0, 1687.0, 1364.5, 1670.5]}, {"path": [1312.0, 1687.0, 1367.3672787979967, 1716.0818030050086]}, {"path": [1316.0, 1407.0, 1316.0, 1451.0]}, {"path": [1316.0, 1407.0, 1319.8032786885246, 1345.1639344262296]}, {"path": [1316.0, 1407.0, 1345.0, 1363.0]}, {"path": [1316.0, 1407.0, 1349.75, 1418.25]}, {"path": [1318.0, 337.0, 1339.923076923077, 281.61538461538464]}, {"path": [1318.0, 599.0, 1327.8999999999999, 576.3]}, {"path": [1318.0, 599.0, 1353.0, 670.0]}, {"path": [1318.0, 599.0, 1444.0, 561.0]}, {"path": [1318.0, 1313.0, 1319.8032786885246, 1345.1639344262296]}, {"path": [1318.0, 1313.0, 1336.1402714932126, 1318.8597285067874]}, {"path": [1320.0, 1564.0, 1342.0, 1537.0]}, {"path": [1321.3, 509.10000000000014, 1324.0, 519.0]}, {"path": [1324.0, 519.0, 1371.0, 556.0]}, {"path": [1324.0, 940.0, 1341.6345609065156, 920.6515580736543]}, {"path": [1324.0, 940.0, 1373.5, 965.5]}, {"path": [1327.2477876106195, 901.2831858407079, 1346.0, 886.0]}, {"path": [1327.8999999999999, 576.3, 1371.0, 556.0]}, {"path": [1328.0, 134.0, 1335.0, 146.0]}, {"path": [1331.0, 78.0, 1384.0, 62.0]}, {"path": [1331.0, 1723.0, 1349.0, 1778.0]}, {"path": [1331.5124555160141, 269.83985765124527, 1341.0, 277.0]}, {"path": [1332.0, 1008.0, 1365.0584707646176, 1057.2203898050975]}, {"path": [1332.0, 1008.0, 1384.3076923076922, 998.4615384615382]}, {"path": [1333.1517976832915, 1142.3633820850378, 1335.0, 1159.0]}, {"path": [1334.9541984732823, 830.8167938931297, 1365.0, 828.0]}, {"path": [1335.0, 146.0, 1341.0, 277.0]}, {"path": [1335.0, 1159.0, 1355.5, 1132.5]}, {"path": [1335.7949283351709, 1071.282249173098, 1377.0, 1075.0]}, {"path": [1336.1402714932126, 1318.8597285067874, 1379.0, 1350.0]}, {"path": [1339.923076923077, 281.61538461538464, 1341.0, 277.0]}, {"path": [1340.3054298642535, 1573.6447963800904, 1341.0, 1587.0]}, {"path": [1340.3054298642535, 1573.6447963800904, 1342.0, 1537.0]}, {"path": [1341.0, 277.0, 1377.0, 300.0]}, {"path": [1341.0, 277.0, 1458.0, 223.0]}, {"path": [1341.0, 1587.0, 1345.0344827586207, 1623.5862068965516]}, {"path": [1341.0, 1587.0, 1367.2061711079944, 1591.929873772791]}, {"path": [1341.6345609065156, 920.6515580736543, 1401.0, 883.0]}, {"path": [1342.0, 1537.0, 1362.0, 1539.0]}, {"path": [1343.6800195203803, 1458.1960185443613, 1370.0, 1425.0]}, {"path": [1343.9545454545453, 1222.3636363636365, 1437.0, 1279.0]}, {"path": [1345.0, 1363.0, 1379.0, 1350.0]}, {"path": [1345.0344827586207, 1623.5862068965516, 1353.0, 1637.0]}, {"path": [1346.0, 886.0, 1365.0, 828.0]}, {"path": [1349.0, 1778.0, 1396.2691292875986, 1789.551451187335]}, {"path": [1349.0, 1778.0, 1408.0, 1815.0]}, {"path": [1349.0, 1778.0, 1411.0, 1739.0]}, {"path": [1349.75, 1418.25, 1370.0, 1425.0]}, {"path": [1350.9634146341464, 799.670731707317, 1365.0, 828.0]}, {"path": [1350.9634146341464, 799.670731707317, 1373.0, 724.0]}, {"path": [1352.0, 1288.0, 1397.0, 1282.0]}, {"path": [1353.0, 670.0, 1368.9151193633952, 712.9416445623342]}, {"path": [1353.0, 1637.0, 1399.0, 1661.0]}, {"path": [1355.181085555571, 1507.2648624242343, 1362.0, 1539.0]}, {"path": [1355.5, 1132.5, 1377.0, 1075.0]}, {"path": [1362.0, 1539.0, 1379.0, 1561.0]}, {"path": [1362.0, 1539.0, 1405.0, 1504.0]}, {"path": [1362.2124937988935, 339.7535661133372, 1373.0, 328.0]}, {"path": [1364.5, 1670.5, 1384.0, 1659.0]}, {"path": [1365.0, 828.0, 1393.8202247191011, 833.7640449438202]}, {"path": [1365.0584707646176, 1057.2203898050975, 1377.0, 1075.0]}, {"path": [1367.2061711079944, 1591.929873772791, 1437.0, 1605.0]}, {"path": [1367.3672787979967, 1716.0818030050086, 1411.0, 1739.0]}, {"path": [1368.9151193633952, 712.9416445623342, 1373.0, 724.0]}, {"path": [1370.0, 1425.0, 1374.7131782945726, 1385.7235142118861]}, {"path": [1370.0, 1425.0, 1386.3902439024391, 1414.7560975609756]}, {"path": [1370.0, 1425.0, 1394.0174672489081, 1438.9737991266377]}, {"path": [1370.0, 1425.0, 1400.1176470588234, 1477.5294117647059]}, {"path": [1371.0, 556.0, 1388.6666666666667, 523.0]}, {"path": [1371.0, 556.0, 1392.088888888889, 557.4444444444445]}, {"path": [1371.0, 556.0, 1441.1721311475408, 525.8934426229484]}, {"path": [1373.0, 328.0, 1377.0, 300.0]}, {"path": [1373.0, 724.0, 1389.180995475113, 771.1990950226246]}, {"path": [1373.0, 724.0, 1427.9190839694654, 596.393893129771]}, {"path": [1373.0, 724.0, 1478.0, 681.0]}, {"path": [1373.0, 724.0, 1517.435294117647, 760.458823529411]}, {"path": [1373.5, 965.5, 1423.0, 992.0]}, {"path": [1374.7131782945726, 1385.7235142118861, 1379.0, 1350.0]}, {"path": [1377.0, 300.0, 1426.0, 351.0]}, {"path": [1377.0, 1075.0, 1424.0, 1064.1014492753623]}, {"path": [1377.0, 1075.0, 1434.0, 1127.0]}, {"path": [1379.0, 1350.0, 1387.789831697055, 1292.3109396914444]}, {"path": [1379.0, 1350.0, 1404.0729335494327, 1347.2447325769856]}, {"path": [1379.0, 1350.0, 1404.5896226415098, 1391.27358490566]}, {"path": [1379.0, 1561.0, 1437.0, 1605.0]}, {"path": [1383.0, 1187.0, 1430.0728017787337, 1201.4546696312227]}, {"path": [1384.0, 62.0, 1408.0, 79.0]}, {"path": [1384.0, 1659.0, 1399.0, 1661.0]}, {"path": [1384.3076923076922, 998.4615384615382, 1423.0, 992.0]}, {"path": [1386.3902439024391, 1414.7560975609756, 1410.0, 1400.0]}, {"path": [1387.789831697055, 1292.3109396914444, 1397.0, 1282.0]}, {"path": [1388.6666666666667, 523.0, 1397.904537093808, 469.2489459038818]}, {"path": [1389.180995475113, 771.1990950226246, 1410.0, 837.0]}, {"path": [1392.088888888889, 557.4444444444445, 1444.0, 561.0]}, {"path": [1393.8202247191011, 833.7640449438202, 1410.0, 837.0]}, {"path": [1394.0174672489081, 1438.9737991266377, 1425.0, 1457.0]}, {"path": [1396.2691292875986, 1789.551451187335, 1414.0, 1788.0]}, {"path": [1397.0, 397.0, 1414.0, 411.0]}, {"path": [1397.0, 1282.0, 1437.0, 1279.0]}, {"path": [1397.904537093808, 469.2489459038818, 1424.0, 429.0]}, {"path": [1399.0, 1661.0, 1401.15625, 1675.015625]}, {"path": [1399.0, 1661.0, 1412.0, 1664.084745762712]}, {"path": [1400.1176470588234, 1477.5294117647059, 1405.0, 1504.0]}, {"path": [1401.0, 883.0, 1410.0, 837.0]}, {"path": [1401.15625, 1675.015625, 1411.0, 1739.0]}, {"path": [1404.0729335494327, 1347.2447325769856, 1470.0, 1340.0]}, {"path": [1404.5896226415098, 1391.27358490566, 1410.0, 1400.0]}, {"path": [1405.0, 1504.0, 1421.1666666666667, 1471.6666666666667]}, {"path": [1405.0, 1504.0, 1457.0, 1500.0]}, {"path": [1408.0, 79.0, 1422.490243902439, 102.81219512195119]}, {"path": [1408.0, 1815.0, 1411.2295081967213, 1801.0]}, {"path": [1408.0, 1815.0, 1559.0, 1813.0]}, {"path": [1410.0, 837.0, 1433.0, 900.0]}, {"path": [1410.0, 837.0, 1487.0, 815.0]}, {"path": [1410.0, 1400.0, 1417.152619589977, 1427.1799544419137]}, {"path": [1410.0, 1400.0, 1430.8823529411766, 1388.5294117647059]}, {"path": [1410.0, 1400.0, 1495.7561942517345, 1393.3002973240832]}, {"path": [1411.0, 1739.0, 1413.8163265306123, 1785.0]}, {"path": [1411.0, 1739.0, 1458.0, 1675.0]}, {"path": [1411.0, 1739.0, 1500.0, 1739.0]}, {"path": [1411.2295081967213, 1801.0, 1414.0, 1788.0]}, {"path": [1412.0, 1664.084745762712, 1458.0, 1675.0]}, {"path": [1413.8163265306123, 1785.0, 1414.0, 1788.0]}, {"path": [1414.0, 411.0, 1424.0, 429.0]}, {"path": [1414.0, 1788.0, 1422.0, 1797.0]}, {"path": [1417.152619589977, 1427.1799544419137, 1425.0, 1457.0]}, {"path": [1421.1666666666667, 1471.6666666666667, 1425.0, 1457.0]}, {"path": [1422.0, 1797.0, 1470.6666666666667, 1799.0]}, {"path": [1422.490243902439, 102.81219512195119, 1474.0, 158.0]}, {"path": [1423.0, 992.0, 1584.0, 1027.0]}, {"path": [1424.0, 429.0, 1428.0, 445.0]}, {"path": [1424.0, 1064.1014492753623, 1584.0, 1027.0]}, {"path": [1425.0, 1457.0, 1482.3962264150944, 1466.1132075471696]}, {"path": [1425.0, 1457.0, 1489.0333333333333, 1419.0333333333333]}, {"path": [1426.0, 351.0, 1445.0, 397.0]}, {"path": [1427.9190839694654, 596.393893129771, 1444.0, 561.0]}, {"path": [1428.0, 445.0, 1440.6666666666667, 454.33333333333337]}, {"path": [1430.0728017787337, 1201.4546696312227, 1503.0, 1205.0]}, {"path": [1430.8823529411766, 1388.5294117647059, 1470.0, 1340.0]}, {"path": [1433.0, 900.0, 1450.0, 934.0]}, {"path": [1434.0, 1127.0, 1451.0, 1170.0]}, {"path": [1434.0, 1127.0, 1455.9754098360656, 1134.7295081967213]}, {"path": [1437.0, 1279.0, 1444.2891566265057, 1292.4738955823295]}, {"path": [1437.0, 1279.0, 1467.5731707317075, 1237.1585365853673]}, {"path": [1437.0, 1279.0, 1471.6697247706422, 1280.8990825688072]}, {"path": [1437.0, 1605.0, 1453.2758620689656, 1520.3103448275863]}, {"path": [1437.0, 1605.0, 1458.0, 1675.0]}, {"path": [1437.0, 1605.0, 1477.0, 1569.0]}, {"path": [1437.0, 1605.0, 1514.3529411764705, 1626.411764705882]}, {"path": [1440.6666666666667, 454.33333333333337, 1463.0, 444.0]}, {"path": [1441.1721311475408, 525.8934426229484, 1463.5, 524.5]}, {"path": [1444.0, 561.0, 1469.4166666666667, 528.4166666666667]}, {"path": [1444.0, 561.0, 1505.058823529412, 562.7647058823528]}, {"path": [1444.2891566265057, 1292.4738955823295, 1470.0, 1340.0]}, {"path": [1445.0, 397.0, 1456.4, 318.8000000000002]}, {"path": [1445.0, 397.0, 1464.0, 429.0]}, {"path": [1450.0, 934.0, 1463.2923076923078, 954.6615384615384]}, {"path": [1451.0, 1170.0, 1467.132075471698, 1182.0377358490568]}, {"path": [1453.2758620689656, 1520.3103448275863, 1457.0, 1500.0]}, {"path": [1455.9754098360656, 1134.7295081967213, 1547.0, 1205.0]}, {"path": [1456.4, 318.8000000000002, 1495.0, 273.0]}, {"path": [1457.0, 1500.0, 1493.4337419354833, 1497.963806451616]}, {"path": [1458.0, 223.0, 1475.0, 202.0]}, {"path": [1458.0, 1675.0, 1500.0, 1739.0]}, {"path": [1458.0, 1675.0, 1506.1313559322034, 1651.584745762712]}, {"path": [1463.0, 444.0, 1464.0, 429.0]}, {"path": [1463.2923076923078, 954.6615384615384, 1584.0, 1027.0]}, {"path": [1463.5, 524.5, 1472.0, 525.0]}, {"path": [1464.0, 429.0, 1480.0, 429.0]}, {"path": [1467.132075471698, 1182.0377358490568, 1503.0, 1205.0]}, {"path": [1467.5731707317075, 1237.1585365853673, 1503.0, 1205.0]}, {"path": [1469.4166666666667, 528.4166666666667, 1472.0, 525.0]}, {"path": [1470.0, 1340.0, 1497.8898550724634, 1360.5072463768117]}, {"path": [1470.0, 1340.0, 1500.7058823529414, 1311.9411764705883]}, {"path": [1470.0, 1340.0, 1541.5172413793105, 1341.2068965517237]}, {"path": [1470.6666666666667, 1799.0, 1477.3333333333333, 1791.0]}, {"path": [1470.6666666666667, 1799.0, 1492.0, 1804.0]}, {"path": [1471.6697247706422, 1280.8990825688072, 1528.0, 1287.0]}, {"path": [1472.0, 525.0, 1493.0, 485.0]}, {"path": [1472.0, 525.0, 1496.0, 546.7142857142858]}, {"path": [1474.0, 158.0, 1475.0, 202.0]}, {"path": [1474.0, 158.0, 1561.8620689655172, 98.65517241379308]}, {"path": [1475.0, 202.0, 1488.0, 200.0]}, {"path": [1475.0, 202.0, 1495.0, 273.0]}, {"path": [1476.0, 9.0, 1583.0, 68.0]}, {"path": [1476.0, 9.0, 1663.0, 7.0]}, {"path": [1477.0, 1569.0, 1540.0, 1519.0]}, {"path": [1477.3333333333333, 1791.0, 1500.0, 1739.0]}, {"path": [1478.0, 681.0, 1498.8769230769233, 648.2153846153846]}, {"path": [1480.0, 429.0, 1502.0, 437.0]}, {"path": [1482.3962264150944, 1466.1132075471696, 1526.0, 1494.3333333333333]}, {"path": [1487.0, 815.0, 1514.0, 864.0]}, {"path": [1487.0, 815.0, 1528.0, 804.0]}, {"path": [1488.0, 200.0, 1492.234899328859, 201.3355704697987]}, {"path": [1489.0333333333333, 1419.0333333333333, 1538.0, 1390.0]}, {"path": [1492.0, 1804.0, 1506.5504587155963, 1805.165137614679]}, {"path": [1492.234899328859, 201.3355704697987, 1585.0, 254.0]}, {"path": [1493.0, 485.0, 1502.0, 437.0]}, {"path": [1493.4337419354833, 1497.963806451616, 1526.0, 1494.3333333333333]}, {"path": [1495.0, 273.0, 1532.6666666666667, 321.6666666666667]}, {"path": [1495.7561942517345, 1393.3002973240832, 1538.0, 1390.0]}, {"path": [1496.0, 546.7142857142858, 1514.0, 563.0]}, {"path": [1497.8898550724634, 1360.5072463768117, 1538.0, 1390.0]}, {"path": [1498.8769230769233, 648.2153846153846, 1514.0, 563.0]}, {"path": [1500.0, 1739.0, 1559.0, 1813.0]}, {"path": [1500.0, 1739.0, 1609.0, 1773.0]}, {"path": [1500.7058823529414, 1311.9411764705883, 1528.0, 1287.0]}, {"path": [1502.0, 437.0, 1503.9757575757576, 396.4969696969697]}, {"path": [1503.0, 1205.0, 1509.857142857143, 1205.0]}, {"path": [1503.0, 1205.0, 1509.8658536585365, 1223.7926829268292]}, {"path": [1503.9757575757576, 396.4969696969697, 1522.0, 351.0]}, {"path": [1505.058823529412, 562.7647058823528, 1514.0, 563.0]}, {"path": [1506.1313559322034, 1651.584745762712, 1532.0, 1639.0]}, {"path": [1506.5504587155963, 1805.165137614679, 1559.0, 1813.0]}, {"path": [1509.857142857143, 1205.0, 1547.0, 1205.0]}, {"path": [1509.8658536585365, 1223.7926829268292, 1528.0, 1287.0]}, {"path": [1512.0, 1433.0, 1516.7742210150784, 1451.751112835602]}, {"path": [1512.0, 1433.0, 1538.0, 1390.0]}, {"path": [1514.0, 563.0, 1563.0, 556.0]}, {"path": [1514.0, 563.0, 1571.915982339956, 641.8168035320089]}, {"path": [1514.0, 563.0, 1593.719495091164, 620.0406732117813]}, {"path": [1514.0, 864.0, 1571.0, 919.0]}, {"path": [1514.3529411764705, 1626.411764705882, 1532.0, 1639.0]}, {"path": [1516.7742210150784, 1451.751112835602, 1553.0, 1473.0]}, {"path": [1517.435294117647, 760.458823529411, 1607.0, 810.0]}, {"path": [1522.0, 351.0, 1532.6666666666667, 321.6666666666667]}, {"path": [1522.0, 351.0, 1544.7076923076925, 392.7384615384614]}, {"path": [1526.0, 1494.3333333333333, 1537.1764705882354, 1513.2941176470588]}, {"path": [1526.0, 1494.3333333333333, 1586.0, 1498.0]}, {"path": [1528.0, 804.0, 1607.0, 810.0]}, {"path": [1528.0, 1287.0, 1533.1089108910892, 1260.0891089108918]}, {"path": [1528.0, 1287.0, 1534.0, 1290.0]}, {"path": [1532.0, 1639.0, 1556.2687453600593, 1605.3704528582034]}, {"path": [1532.0, 1639.0, 1595.3333333333333, 1711.6666666666667]}, {"path": [1532.0, 1639.0, 1615.0, 1622.0]}, {"path": [1532.0, 1639.0, 1623.0386740331492, 1670.9226519337014]}, {"path": [1532.6666666666667, 321.6666666666667, 1573.7461538461537, 342.93076923076984]}, {"path": [1533.1089108910892, 1260.0891089108918, 1547.0, 1205.0]}, {"path": [1534.0, 1290.0, 1554.0, 1310.0]}, {"path": [1537.1764705882354, 1513.2941176470588, 1540.0, 1519.0]}, {"path": [1538.0, 1390.0, 1547.025641025642, 1369.8974358974356]}, {"path": [1538.0, 1390.0, 1548.8088235294117, 1392.5735294117649]}, {"path": [1540.0, 1519.0, 1584.0, 1519.0]}, {"path": [1541.5172413793105, 1341.2068965517237, 1560.0, 1341.0]}, {"path": [1544.7076923076925, 392.7384615384614, 1582.6666666666667, 539.0]}, {"path": [1547.0, 1205.0, 1554.0409836065571, 1296.5491803278687]}, {"path": [1547.0, 1205.0, 1575.6520076481834, 1067.1606118546845]}, {"path": [1547.0, 1205.0, 1597.8206896551726, 1273.9517241379308]}, {"path": [1547.0, 1205.0, 1603.1320754716983, 1236.962264150944]}, {"path": [1547.0, 1205.0, 1605.0, 1190.0]}, {"path": [1547.0, 1205.0, 1701.1369193154023, 1081.9608801955992]}, {"path": [1547.025641025642, 1369.8974358974356, 1560.0, 1341.0]}, {"path": [1548.8088235294117, 1392.5735294117649, 1606.0, 1447.0]}, {"path": [1553.0, 1473.0, 1586.0, 1498.0]}, {"path": [1554.0, 1310.0, 1554.0409836065571, 1296.5491803278687]}, {"path": [1554.0, 1310.0, 1560.0, 1341.0]}, {"path": [1556.2687453600593, 1605.3704528582034, 1602.0, 1542.0]}, {"path": [1559.0, 1813.0, 1609.0, 1773.0]}, {"path": [1559.0, 1813.0, 1761.0, 1815.0]}, {"path": [1560.0, 1341.0, 1565.0, 1341.483870967742]}, {"path": [1561.8620689655172, 98.65517241379308, 1583.0, 68.0]}, {"path": [1563.0, 556.0, 1582.6666666666667, 539.0]}, {"path": [1563.0, 556.0, 1706.0, 552.2048192771084]}, {"path": [1565.0, 1341.483870967742, 1622.0, 1347.0]}, {"path": [1571.0, 919.0, 1636.7607200257153, 939.9616200578591]}, {"path": [1571.915982339956, 641.8168035320089, 1607.0, 810.0]}, {"path": [1573.7461538461537, 342.93076923076984, 1603.0, 357.0]}, {"path": [1575.6520076481834, 1067.1606118546845, 1584.0, 1027.0]}, {"path": [1583.0, 68.0, 1663.0, 7.0]}, {"path": [1583.0, 68.0, 1701.0, 174.0]}, {"path": [1584.0, 1027.0, 1698.832786885246, 1025.0393442622951]}, {"path": [1584.0, 1519.0, 1592.1176470588234, 1536.4705882352941]}, {"path": [1584.0, 1519.0, 1595.0, 1500.0]}, {"path": [1585.0, 254.0, 1598.2432432432433, 330.5405405405406]}, {"path": [1585.0, 254.0, 1644.0, 257.0]}, {"path": [1586.0, 1498.0, 1591.4823529411765, 1499.3294117647058]}, {"path": [1591.4823529411765, 1499.3294117647058, 1595.0, 1500.0]}, {"path": [1592.1176470588234, 1536.4705882352941, 1602.0, 1542.0]}, {"path": [1593.719495091164, 620.0406732117813, 1746.0, 729.0]}, {"path": [1595.0, 1500.0, 1597.9269662921347, 1501.9831460674163]}, {"path": [1595.0, 1500.0, 1606.0, 1447.0]}, {"path": [1595.3333333333333, 1711.6666666666667, 1609.0, 1773.0]}, {"path": [1597.8206896551726, 1273.9517241379308, 1622.0, 1347.0]}, {"path": [1597.9269662921347, 1501.9831460674163, 1604.0, 1504.0]}, {"path": [1598.2432432432433, 330.5405405405406, 1603.0, 357.0]}, {"path": [1602.0, 1542.0, 1618.0, 1535.0]}, {"path": [1602.0, 1542.0, 1626.0, 1544.0]}, {"path": [1603.0, 357.0, 1699.0, 347.0]}, {"path": [1603.0, 357.0, 1763.0, 549.0]}, {"path": [1603.1320754716983, 1236.962264150944, 1661.0, 1285.0]}, {"path": [1604.0, 1504.0, 1643.0, 1504.0]}, {"path": [1605.0, 1190.0, 1662.0, 1183.0]}, {"path": [1606.0, 1447.0, 1622.0, 1347.0]}, {"path": [1606.0, 1447.0, 1684.0, 1434.0]}, {"path": [1606.0, 1447.0, 1710.0, 1360.0]}, {"path": [1607.0, 810.0, 1674.3899430740037, 770.7296015180267]}, {"path": [1607.0, 810.0, 1683.762295081967, 892.3852459016393]}, {"path": [1609.0, 1773.0, 1641.2478632478633, 1739.4957264957268]}, {"path": [1609.0, 1773.0, 1726.0, 1753.0]}, {"path": [1609.0, 1773.0, 1761.0, 1815.0]}, {"path": [1615.0, 1622.0, 1661.769230769231, 1590.846153846154]}, {"path": [1615.0, 1622.0, 1672.0, 1619.0]}, {"path": [1618.0, 1535.0, 1643.0, 1504.0]}, {"path": [1622.0, 1347.0, 1661.0, 1285.0]}, {"path": [1622.0, 1347.0, 1710.0, 1360.0]}, {"path": [1623.0386740331492, 1670.9226519337014, 1686.0, 1693.0]}, {"path": [1626.0, 1544.0, 1658.789751689041, 1548.9871896113011]}, {"path": [1636.7607200257153, 939.9616200578591, 1665.0, 942.0]}, {"path": [1641.2478632478633, 1739.4957264957268, 1686.0, 1693.0]}, {"path": [1643.0, 1504.0, 1666.0, 1497.0]}, {"path": [1644.0, 257.0, 1688.3463035019454, 284.5252918287938]}, {"path": [1644.0, 257.0, 1701.0, 174.0]}, {"path": [1658.789751689041, 1548.9871896113011, 1751.0, 1533.0]}, {"path": [1661.0, 1285.0, 1710.0, 1360.0]}, {"path": [1661.0, 1285.0, 1737.0, 1281.8333333333333]}, {"path": [1661.769230769231, 1590.846153846154, 1751.0, 1533.0]}, {"path": [1662.0, 1183.0, 1700.3118989688242, 1196.1435591268164]}, {"path": [1663.0, 7.0, 1701.0, 174.0]}, {"path": [1665.0, 942.0, 1775.0, 1023.0]}, {"path": [1666.0, 1497.0, 1668.082191780822, 1487.2191780821918]}, {"path": [1666.0, 1497.0, 1703.6165803108809, 1508.0569948186528]}, {"path": [1666.0, 1497.0, 1740.0, 1449.2428571428572]}, {"path": [1668.082191780822, 1487.2191780821918, 1684.0, 1434.0]}, {"path": [1672.0, 1619.0, 1690.759168542301, 1620.9659737852508]}, {"path": [1674.3899430740037, 770.7296015180267, 1746.0, 729.0]}, {"path": [1683.762295081967, 892.3852459016393, 1775.0, 1023.0]}, {"path": [1684.0, 1434.0, 1710.0, 1360.0]}, {"path": [1684.0, 1434.0, 1810.6153846153845, 1411.923076923077]}, {"path": [1686.0, 1693.0, 1690.1600000000008, 1699.2399999999998]}, {"path": [1686.0, 1693.0, 1719.8636363636365, 1662.5227272727277]}, {"path": [1688.3463035019454, 284.5252918287938, 1702.0, 293.0]}, {"path": [1690.1600000000008, 1699.2399999999998, 1726.0, 1753.0]}, {"path": [1690.759168542301, 1620.9659737852508, 1714.0, 1625.0]}, {"path": [1697.95, 332.4875, 1699.0, 347.0]}, {"path": [1697.95, 332.4875, 1702.0, 293.0]}, {"path": [1698.832786885246, 1025.0393442622951, 1775.0, 1023.0]}, {"path": [1699.0, 347.0, 1763.0, 549.0]}, {"path": [1700.3118989688242, 1196.1435591268164, 1722.6666666666667, 1210.3333333333333]}, {"path": [1701.1369193154023, 1081.9608801955992, 1775.0, 1023.0]}, {"path": [1703.6165803108809, 1508.0569948186528, 1751.0, 1533.0]}, {"path": [1706.0, 552.2048192771084, 1763.0, 549.0]}, {"path": [1710.0, 1360.0, 1743.6363636363635, 1309.6363636363635]}, {"path": [1710.0, 1360.0, 1783.0, 1341.0]}, {"path": [1710.0, 1360.0, 1810.6153846153845, 1411.923076923077]}, {"path": [1714.0, 1625.0, 1746.0, 1639.0]}, {"path": [1719.8636363636365, 1662.5227272727277, 1746.0, 1639.0]}, {"path": [1722.6666666666667, 1210.3333333333333, 1751.3333333333333, 1253.0]}, {"path": [1722.6666666666667, 1210.3333333333333, 1771.3333333333333, 1152.3333333333333]}, {"path": [1726.0, 1753.0, 1761.0, 1815.0]}, {"path": [1726.0, 1753.0, 1783.1791530944627, 1764.9478827361568]}, {"path": [1726.0, 1753.0, 1829.0, 1723.0]}, {"path": [1737.0, 1281.8333333333333, 1757.0, 1281.0]}, {"path": [1740.0, 1449.2428571428572, 1813.0, 1419.0]}, {"path": [1743.6363636363635, 1309.6363636363635, 1757.0, 1281.0]}, {"path": [1746.0, 729.0, 1755.7279538904895, 827.6213256484149]}, {"path": [1746.0, 729.0, 1760.5961259079902, 574.452784503632]}, {"path": [1746.0, 1639.0, 1748.3673328738796, 1588.812543073742]}, {"path": [1746.0, 1639.0, 1802.0, 1612.0]}, {"path": [1746.0, 1639.0, 1817.5688622754492, 1711.4311377245508]}, {"path": [1748.3673328738796, 1588.812543073742, 1751.0, 1533.0]}, {"path": [1751.0, 1533.0, 1790.3864734299518, 1460.5797101449275]}, {"path": [1751.3333333333333, 1253.0, 1757.0, 1281.0]}, {"path": [1755.7279538904895, 827.6213256484149, 1775.0, 1023.0]}, {"path": [1757.0, 1281.0, 1766.7249357326477, 1297.9562982005143]}, {"path": [1760.5961259079902, 574.452784503632, 1763.0, 549.0]}, {"path": [1761.0, 1815.0, 1782.7837837837837, 1795.0]}, {"path": [1761.0, 1815.0, 1871.0, 1813.0]}, {"path": [1766.7249357326477, 1297.9562982005143, 1783.0, 1341.0]}, {"path": [1771.1543624161077, 1078.1208053691275, 1771.3333333333333, 1152.3333333333333]}, {"path": [1771.1543624161077, 1078.1208053691275, 1775.0, 1023.0]}, {"path": [1771.3333333333333, 1152.3333333333333, 1801.6629834254147, 1183.5966850828725]}, {"path": [1782.7837837837837, 1795.0, 1793.0, 1767.0]}, {"path": [1783.0, 1341.0, 1810.6153846153845, 1411.923076923077]}, {"path": [1783.0, 1341.0, 1850.0, 1300.0]}, {"path": [1783.1791530944627, 1764.9478827361568, 1793.0, 1767.0]}, {"path": [1790.3864734299518, 1460.5797101449275, 1813.0, 1419.0]}, {"path": [1793.0, 1767.0, 1798.7272727272725, 1760.0]}, {"path": [1793.0, 1767.0, 1816.4375, 1779.5]}, {"path": [1798.7272727272725, 1760.0, 1829.0, 1723.0]}, {"path": [1801.6629834254147, 1183.5966850828725, 1836.0, 1227.0]}, {"path": [1802.0, 1612.0, 1806.9114631873254, 1577.4778657968313]}, {"path": [1806.9114631873254, 1577.4778657968313, 1831.0, 1472.0]}, {"path": [1810.6153846153845, 1411.923076923077, 1813.0, 1419.0]}, {"path": [1810.6153846153845, 1411.923076923077, 1850.0, 1300.0]}, {"path": [1813.0, 1419.0, 1831.0, 1472.0]}, {"path": [1816.4375, 1779.5, 1871.0, 1813.0]}, {"path": [1817.5688622754492, 1711.4311377245508, 1829.0, 1723.0]}, {"path": [1829.0, 1723.0, 1871.0, 1731.0]}, {"path": [1836.0, 1227.0, 1850.0, 1300.0]}, {"path": [1871.0, 1731.0, 1871.0, 1813.0]}], "height": 1820, "locations": [{"borders": [[1214.0, 931.0, 1194.0, 920.0, 1216.0, 901.0, 1214.0, 896.0, 1220.0, 891.0, 1220.0, 884.0, 1216.0, 879.0, 1220.0, 871.0, 1220.0, 862.0, 1226.0, 859.0, 1229.0, 863.0, 1236.0, 862.0, 1255.0, 841.0, 1262.0, 837.0, 1247.0, 837.0, 1248.0, 827.0, 1256.0, 827.0, 1265.0, 825.0, 1274.0, 828.0, 1285.0, 827.0, 1285.0, 814.0, 1288.0, 806.0, 1283.0, 805.0, 1285.0, 793.0, 1280.0, 782.0, 1286.0, 781.0, 1299.0, 808.0, 1303.0, 807.0, 1313.0, 807.0, 1311.0, 810.0, 1328.0, 813.0, 1325.0, 816.0, 1333.0, 823.0, 1336.0, 835.0, 1330.0, 844.0, 1334.0, 859.0, 1325.0, 861.0, 1327.0, 870.0, 1334.0, 886.0, 1319.0, 891.0, 1310.0, 895.0, 1304.0, 896.0, 1302.0, 903.0, 1294.0, 901.0, 1274.0, 913.0, 1264.0, 905.0, 1262.0, 914.0, 1258.0, 919.0, 1252.0, 915.0, 1242.0, 923.0, 1238.0, 918.0, 1232.0, 924.0, 1223.0, 924.0, 1221.0, 935.0, 1218.0, 936.0]], "capital": [1269.0, 837.0], "configuredProductions": {"0": 2900.0}, "locationType": "province", "name": "Königsberg", "neighbourIds": [5, 145, 146, 147, 156, 262, 264], "ownerId": 3, "population": 100000}, {"borders": [[819.0, 1158.0, 814.0, 1160.0, 811.0, 1163.0, 806.0, 1163.0, 804.0, 1159.0, 802.0, 1151.0, 796.0, 1146.0, 799.0, 1139.0, 808.0, 1125.0, 807.0, 1109.0, 802.0, 1103.0, 792.0, 1096.0, 789.0, 1085.0, 783.0, 1081.0, 777.0, 1074.0, 782.0, 1065.0, 781.0, 1057.0, 790.0, 1055.0, 798.0, 1045.0, 800.0, 1039.0, 814.0, 1043.0, 825.0, 1043.0, 832.0, 1029.0, 838.0, 1028.0, 838.0, 1021.0, 844.0, 1021.0, 844.0, 1029.0, 848.0, 1037.0, 854.0, 1041.0, 859.0, 1038.0, 864.0, 1044.0, 867.0, 1056.0, 865.0, 1064.0, 864.0, 1073.0, 866.0, 1081.0, 853.0, 1100.0, 845.0, 1109.0, 835.0, 1118.0, 832.0, 1129.0, 824.0, 1137.0, 823.0, 1145.0]], "capital": [829.0, 1109.0], "configuredProductions": {"2": 5800.0}, "id": 1, "locationType": "province", "name": "Strasburg", "neighbourIds": [7, 9, 11, 72, 73, 83], "ownerId": 3, "population": 100000, "productionType": 2}, {"borders": [[944.0, 798.0, 950.0, 805.0, 946.0, 805.0, 942.0, 810.0, 945.0, 809.0, 952.0, 818.0, 943.0, 828.0, 948.0, 829.0, 954.0, 827.0, 957.0, 840.0, 962.0, 839.0, 966.0, 839.0, 970.0, 847.0, 979.0, 843.0, 981.0, 845.0, 980.0, 853.0, 971.0, 853.0, 968.0, 857.0, 969.0, 861.0, 974.0, 861.0, 978.0, 859.0, 991.0, 868.0, 994.0, 864.0, 996.0, 858.0, 998.0, 855.0, 1011.0, 855.0, 1012.0, 853.0, 1018.0, 851.0, 1018.0, 880.0, 1015.0, 889.0, 1008.0, 897.0, 997.0, 899.0, 990.0, 893.0, 974.0, 895.0, 968.0, 898.0, 956.0, 899.0, 954.0, 903.0, 945.0, 898.0, 935.0, 891.0, 920.0, 875.0, 904.0, 865.0, 906.0, 859.0, 910.0, 857.0, 918.0, 860.0, 931.0, 863.0, 927.0, 856.0, 921.0, 854.0, 918.0, 851.0, 923.0, 848.0, 920.0, 845.0, 917.0, 840.0, 920.0, 833.0, 912.0, 835.0, 911.0, 831.0, 925.0, 827.0, 923.0, 821.0, 916.0, 810.0, 915.0, 800.0, 919.0, 794.0, 918.0, 787.0, 929.0, 787.0, 926.0, 779.0, 935.0, 779.0, 938.0, 784.0, 949.0, 790.0]], "capital": [948.0, 871.0], "configuredProductions": {"5": 2900.0}, "id": 2, "locationType": "province", "name": "Hamburg", "neighbourIds": [4, 6, 25, 251, 259, 260], "ownerId": 3, "population": 100000, "productionType": 5}, {"borders": [[1170.0, 968.0, 1176.0, 979.0, 1184.0, 990.0, 1184.0, 1004.0, 1188.0, 1007.0, 1184.0, 1017.0, 1193.0, 1023.0, 1200.0, 1031.0, 1206.0, 1032.0, 1218.0, 1050.0, 1216.0, 1057.0, 1222.0, 1060.0, 1226.0, 1067.0, 1219.0, 1077.0, 1211.0, 1076.0, 1207.0, 1078.0, 1202.0, 1079.0, 1192.0, 1079.0, 1174.0, 1065.0, 1176.0, 1063.0, 1174.0, 1058.0, 1168.0, 1059.0, 1151.0, 1049.0, 1148.0, 1051.0, 1151.0, 1057.0, 1147.0, 1061.0, 1139.0, 1063.0, 1122.0, 1049.0, 1134.0, 1045.0, 1130.0, 1038.0, 1122.0, 1038.0, 1112.0, 1030.0, 1101.0, 1029.0, 1098.0, 1018.0, 1093.0, 1014.0, 1087.0, 1022.0, 1078.0, 1025.0, 1071.0, 1016.0, 1067.0, 1015.0, 1066.0, 1022.0, 1058.0, 1022.0, 1064.0, 997.0, 1064.0, 979.0, 1058.0, 971.0, 1070.0, 955.0, 1083.0, 944.0, 1092.0, 938.0, 1100.0, 939.0, 1104.0, 935.0, 1108.0, 940.0, 1124.0, 939.0, 1134.0, 936.0, 1144.0, 942.0, 1152.0, 943.0, 1153.0, 953.0, 1153.0, 965.0]], "capital": [1159.0, 1016.0], "configuredProductions": {"4": 5800.0}, "id": 3, "locationType": "province", "name": "Breslau", "neighbourIds": [4, 5, 8, 14, 16, 145], "ownerId": 3, "population": 100000, "productionType": 4}, {"borders": [[994.0, 963.0, 985.0, 952.0, 974.0, 957.0, 972.0, 943.0, 967.0, 924.0, 958.0, 916.0, 954.0, 903.0, 956.0, 899.0, 968.0, 898.0, 974.0, 895.0, 990.0, 893.0, 997.0, 899.0, 1008.0, 897.0, 1015.0, 889.0, 1018.0, 880.0, 1018.0, 851.0, 1030.0, 848.0, 1035.0, 849.0, 1040.0, 846.0, 1046.0, 855.0, 1047.0, 855.0, 1051.0, 863.0, 1064.0, 860.0, 1077.0, 869.0, 1076.0, 875.0, 1070.0, 871.0, 1060.0, 875.0, 1062.0, 881.0, 1070.0, 883.0, 1080.0, 891.0, 1087.0, 902.0, 1080.0, 913.0, 1086.0, 921.0, 1094.0, 922.0, 1090.0, 932.0, 1092.0, 938.0, 1083.0, 944.0, 1070.0, 955.0, 1058.0, 971.0, 1049.0, 965.0, 1019.0, 967.0], [1055.0, 835.0, 1057.0, 840.0, 1061.0, 840.0, 1062.0, 844.0, 1059.0, 847.0, 1062.0, 853.0, 1057.0, 853.0, 1052.0, 857.0, 1048.0, 852.0, 1046.0, 844.0, 1054.0, 851.0, 1055.0, 847.0, 1052.0, 841.0, 1048.0, 840.0, 1048.0, 837.0, 1053.0, 833.0]], "capital": [1043.0, 943.0], "configuredProductions": {"4": 2900.0}, "id": 4, "locationType": "province", "name": "Berlin", "neighbourIds": [2, 3, 5, 6, 8, 260, 261], "ownerId": 3, "population": 100000, "productionType": 4}, {"borders": [[1220.0, 862.0, 1220.0, 871.0, 1216.0, 879.0, 1220.0, 884.0, 1220.0, 891.0, 1214.0, 896.0, 1216.0, 901.0, 1194.0, 920.0, 1214.0, 931.0, 1218.0, 936.0, 1207.0, 944.0, 1199.0, 945.0, 1193.0, 951.0, 1181.0, 953.0, 1170.0, 968.0, 1153.0, 965.0, 1153.0, 953.0, 1152.0, 943.0, 1144.0, 942.0, 1134.0, 936.0, 1124.0, 939.0, 1108.0, 940.0, 1104.0, 935.0, 1100.0, 939.0, 1092.0, 938.0, 1090.0, 932.0, 1094.0, 922.0, 1086.0, 921.0, 1080.0, 913.0, 1087.0, 902.0, 1087.0, 896.0, 1084.0, 887.0, 1088.0, 881.0, 1076.0, 875.0, 1077.0, 869.0, 1091.0, 869.0, 1135.0, 857.0, 1149.0, 847.0, 1158.0, 846.0, 1178.0, 838.0, 1198.0, 834.0, 1203.0, 836.0, 1206.0, 841.0, 1203.0, 842.0, 1214.0, 863.0]], "capital": [1210.0, 869.0], "configuredProductions": {"0": 2900.0}, "id": 5, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [0, 3, 4, 145, 261, 262], "ownerId": 3, "population": 100000}, {"borders": [[902.0, 869.0, 904.0, 865.0, 920.0, 875.0, 935.0, 891.0, 945.0, 898.0, 954.0, 903.0, 958.0, 916.0, 967.0, 924.0, 972.0, 943.0, 974.0, 957.0, 963.0, 964.0, 950.0, 963.0, 941.0, 958.0, 923.0, 961.0, 908.0, 958.0, 891.0, 944.0, 879.0, 945.0, 884.0, 933.0, 857.0, 931.0, 840.0, 923.0, 845.0, 919.0, 845.0, 914.0, 838.0, 913.0, 838.0, 908.0, 840.0, 905.0, 848.0, 904.0, 852.0, 899.0, 858.0, 888.0, 856.0, 885.0, 856.0, 875.0, 852.0, 871.0, 858.0, 865.0, 865.0, 864.0, 882.0, 865.0, 887.0, 872.0, 883.0, 877.0, 884.0, 878.0, 890.0, 879.0, 890.0, 875.0, 895.0, 873.0, 900.0, 880.0, 900.0, 884.0, 904.0, 880.0]], "capital": [923.0, 928.0], "configuredProductions": {"6": 2900.0}, "id": 6, "locationType": "province", "name": "Hanover", "neighbourIds": [2, 4, 7, 8, 11, 76, 251], "ownerId": 3, "population": 100000, "productionType": 6}, {"borders": [[828.0, 945.0, 836.0, 941.0, 830.0, 936.0, 840.0, 923.0, 857.0, 931.0, 884.0, 933.0, 879.0, 945.0, 876.0, 962.0, 885.0, 961.0, 884.0, 969.0, 880.0, 981.0, 874.0, 989.0, 873.0, 1002.0, 879.0, 1011.0, 867.0, 1017.0, 864.0, 1027.0, 854.0, 1031.0, 844.0, 1029.0, 844.0, 1021.0, 838.0, 1021.0, 838.0, 1028.0, 832.0, 1029.0, 825.0, 1043.0, 814.0, 1043.0, 800.0, 1039.0, 792.0, 1033.0, 794.0, 1021.0, 798.0, 1007.0, 805.0, 1000.0, 806.0, 991.0, 806.0, 977.0, 814.0, 966.0, 816.0, 955.0, 812.0, 948.0, 816.0, 946.0, 819.0, 941.0]], "capital": [826.0, 989.0], "configuredProductions": {"4": 2900.0}, "id": 7, "locationType": "province", "name": "Cologne", "neighbourIds": [1, 6, 11, 73, 75, 76], "ownerId": 3, "population": 100000, "productionType": 4}, {"borders": [[962.0, 1028.0, 966.0, 1021.0, 956.0, 1015.0, 950.0, 1014.0, 934.0, 1014.0, 930.0, 1010.0, 930.0, 1006.0, 935.0, 1006.0, 938.0, 1001.0, 946.0, 998.0, 951.0, 992.0, 946.0, 978.0, 950.0, 963.0, 963.0, 964.0, 974.0, 957.0, 985.0, 952.0, 994.0, 963.0, 1019.0, 967.0, 1049.0, 965.0, 1058.0, 971.0, 1064.0, 979.0, 1064.0, 997.0, 1058.0, 1022.0, 1045.0, 1027.0, 1036.0, 1033.0, 1026.0, 1037.0, 1018.0, 1038.0, 1015.0, 1043.0, 1008.0, 1041.0, 1004.0, 1043.0, 1000.0, 1049.0, 996.0, 1046.0, 992.0, 1047.0, 992.0, 1051.0, 996.0, 1057.0, 986.0, 1061.0, 974.0, 1061.0, 957.0, 1049.0]], "capital": [1002.0, 996.0], "configuredProductions": {"0": 2900.0}, "id": 8, "locationType": "province", "name": "Leipsic", "neighbourIds": [3, 4, 6, 10, 11, 14], "ownerId": 3, "population": 100000}, {"borders": [[823.0, 1145.0, 824.0, 1137.0, 832.0, 1129.0, 835.0, 1118.0, 845.0, 1109.0, 853.0, 1100.0, 866.0, 1081.0, 864.0, 1073.0, 865.0, 1064.0, 867.0, 1056.0, 864.0, 1044.0, 882.0, 1057.0, 900.0, 1066.0, 905.0, 1057.0, 911.0, 1049.0, 915.0, 1050.0, 916.0, 1056.0, 918.0, 1062.0, 923.0, 1061.0, 912.0, 1081.0, 908.0, 1111.0, 913.0, 1132.0, 900.0, 1141.0, 892.0, 1140.0, 882.0, 1146.0, 884.0, 1163.0, 880.0, 1163.0, 875.0, 1157.0, 874.0, 1160.0, 868.0, 1162.0, 865.0, 1158.0, 859.0, 1159.0, 855.0, 1153.0, 848.0, 1156.0, 849.0, 1163.0, 840.0, 1161.0, 836.0, 1164.0, 828.0, 1162.0, 826.0, 1158.0, 819.0, 1158.0]], "capital": [886.0, 1113.0], "configuredProductions": {"3": 2900.0}, "id": 9, "locationType": "province", "name": "Stuttgart", "neighbourIds": [1, 10, 11, 71, 72], "ownerId": 3, "population": 100000, "productionType": 3}, {"borders": [[936.0, 1179.0, 936.0, 1171.0, 927.0, 1171.0, 916.0, 1169.0, 918.0, 1180.0, 908.0, 1183.0, 904.0, 1173.0, 894.0, 1175.0, 884.0, 1163.0, 882.0, 1146.0, 892.0, 1140.0, 900.0, 1141.0, 913.0, 1132.0, 908.0, 1111.0, 912.0, 1081.0, 923.0, 1061.0, 928.0, 1053.0, 930.0, 1051.0, 938.0, 1050.0, 944.0, 1057.0, 949.0, 1055.0, 957.0, 1049.0, 974.0, 1061.0, 986.0, 1061.0, 996.0, 1057.0, 998.0, 1066.0, 1004.0, 1071.0, 1000.0, 1084.0, 1011.0, 1097.0, 1019.0, 1105.0, 1020.0, 1117.0, 1027.0, 1123.0, 1034.0, 1126.0, 1042.0, 1139.0, 1038.0, 1142.0, 1024.0, 1142.0, 1023.0, 1147.0, 1019.0, 1153.0, 1004.0, 1159.0, 1005.0, 1165.0, 1003.0, 1171.0, 1003.0, 1182.0, 992.0, 1170.0, 978.0, 1168.0, 971.0, 1171.0, 958.0, 1174.0, 953.0, 1179.0]], "capital": [964.0, 1151.0], "configuredProductions": {"3": 2900.0}, "id": 10, "locationType": "province", "name": "Munich", "neighbourIds": [8, 9, 11, 12, 13, 14, 71], "ownerId": 3, "population": 100000, "productionType": 3}, {"borders": [[891.0, 944.0, 908.0, 958.0, 923.0, 961.0, 941.0, 958.0, 950.0, 963.0, 946.0, 978.0, 951.0, 992.0, 946.0, 998.0, 938.0, 1001.0, 935.0, 1006.0, 930.0, 1006.0, 930.0, 1010.0, 934.0, 1014.0, 950.0, 1014.0, 956.0, 1015.0, 966.0, 1021.0, 962.0, 1028.0, 957.0, 1049.0, 949.0, 1055.0, 944.0, 1057.0, 938.0, 1050.0, 930.0, 1051.0, 928.0, 1053.0, 923.0, 1061.0, 918.0, 1062.0, 916.0, 1056.0, 915.0, 1050.0, 911.0, 1049.0, 905.0, 1057.0, 900.0, 1066.0, 882.0, 1057.0, 864.0, 1044.0, 859.0, 1038.0, 854.0, 1041.0, 848.0, 1037.0, 844.0, 1029.0, 854.0, 1031.0, 864.0, 1027.0, 867.0, 1017.0, 879.0, 1011.0, 873.0, 1002.0, 874.0, 989.0, 880.0, 981.0, 884.0, 969.0, 885.0, 961.0, 876.0, 962.0, 879.0, 945.0]], "capital": [877.0, 1036.0], "configuredProductions": {"2": 5800.0}, "id": 11, "locationType": "province", "name": "Frankfort", "neighbourIds": [1, 6, 7, 8, 9, 10], "ownerId": 3, "population": 100000, "productionType": 2}, {"borders": [[1006.0, 1192.0, 1004.0, 1199.0, 1017.0, 1208.0, 1017.0, 1230.0, 1011.0, 1231.0, 1007.0, 1235.0, 990.0, 1229.0, 979.0, 1230.0, 970.0, 1240.0, 962.0, 1243.0, 960.0, 1252.0, 952.0, 1257.0, 944.0, 1259.0, 936.0, 1269.0, 927.0, 1265.0, 920.0, 1260.0, 912.0, 1263.0, 913.0, 1247.0, 919.0, 1237.0, 914.0, 1229.0, 916.0, 1211.0, 908.0, 1204.0, 903.0, 1209.0, 896.0, 1211.0, 886.0, 1197.0, 884.0, 1188.0, 888.0, 1179.0, 894.0, 1175.0, 904.0, 1173.0, 908.0, 1183.0, 918.0, 1180.0, 916.0, 1169.0, 927.0, 1171.0, 936.0, 1171.0, 936.0, 1179.0, 953.0, 1179.0, 958.0, 1174.0, 971.0, 1171.0, 978.0, 1168.0, 992.0, 1170.0, 1003.0, 1182.0]], "capital": [950.0, 1195.0], "configuredProductions": {"2": 2900.0}, "id": 12, "locationType": "province", "name": "Innsbruck", "neighbourIds": [10, 13, 17, 18, 60, 61, 71], "ownerId": 8, "population": 100000, "productionType": 2}, {"borders": [[1023.0, 1147.0, 1024.0, 1142.0, 1038.0, 1142.0, 1042.0, 1139.0, 1034.0, 1126.0, 1027.0, 1123.0, 1020.0, 1117.0, 1019.0, 1105.0, 1033.0, 1099.0, 1040.0, 1107.0, 1050.0, 1103.0, 1054.0, 1109.0, 1064.0, 1106.0, 1082.0, 1124.0, 1088.0, 1129.0, 1096.0, 1141.0, 1098.0, 1151.0, 1090.0, 1157.0, 1083.0, 1165.0, 1080.0, 1172.0, 1075.0, 1177.0, 1082.0, 1183.0, 1076.0, 1192.0, 1066.0, 1193.0, 1046.0, 1203.0, 1032.0, 1202.0, 1017.0, 1208.0, 1004.0, 1199.0, 1006.0, 1192.0, 1003.0, 1182.0, 1003.0, 1171.0, 1005.0, 1165.0, 1004.0, 1159.0, 1019.0, 1153.0]], "capital": [1054.0, 1161.0], "configuredProductions": {"3": 2900.0}, "id": 13, "locationType": "province", "name": "Linz", "neighbourIds": [10, 12, 14, 17], "ownerId": 8, "population": 100000, "productionType": 3}, {"borders": [[1098.0, 1151.0, 1096.0, 1141.0, 1088.0, 1129.0, 1082.0, 1124.0, 1064.0, 1106.0, 1054.0, 1109.0, 1050.0, 1103.0, 1040.0, 1107.0, 1033.0, 1099.0, 1019.0, 1105.0, 1011.0, 1097.0, 1000.0, 1084.0, 1004.0, 1071.0, 998.0, 1066.0, 996.0, 1057.0, 992.0, 1051.0, 992.0, 1047.0, 996.0, 1046.0, 1000.0, 1049.0, 1004.0, 1043.0, 1008.0, 1041.0, 1015.0, 1043.0, 1018.0, 1038.0, 1026.0, 1037.0, 1036.0, 1033.0, 1045.0, 1027.0, 1058.0, 1022.0, 1066.0, 1022.0, 1067.0, 1015.0, 1071.0, 1016.0, 1078.0, 1025.0, 1087.0, 1022.0, 1093.0, 1014.0, 1098.0, 1018.0, 1101.0, 1029.0, 1112.0, 1030.0, 1122.0, 1038.0, 1130.0, 1038.0, 1134.0, 1045.0, 1122.0, 1049.0, 1139.0, 1063.0, 1147.0, 1061.0, 1151.0, 1057.0, 1148.0, 1051.0, 1151.0, 1049.0, 1168.0, 1059.0, 1174.0, 1058.0, 1176.0, 1063.0, 1174.0, 1065.0, 1192.0, 1079.0, 1202.0, 1079.0, 1207.0, 1078.0, 1211.0, 1076.0, 1207.0, 1095.0, 1195.0, 1102.0, 1186.0, 1109.0, 1182.0, 1119.0, 1174.0, 1128.0, 1164.0, 1131.0, 1157.0, 1126.0, 1151.0, 1130.0, 1144.0, 1141.0, 1122.0, 1142.0, 1108.0, 1146.0]], "capital": [1064.0, 1059.0], "configuredProductions": {"4": 5800.0}, "id": 14, "locationType": "province", "name": "Prague", "neighbourIds": [3, 8, 10, 13, 16, 17], "ownerId": 8, "population": 100000, "productionType": 4}, {"borders": [[1334.0, 1050.0, 1332.0, 1055.0, 1343.0, 1057.0, 1354.0, 1051.0, 1370.0, 1060.0, 1368.0, 1049.0, 1381.0, 1049.0, 1390.0, 1040.0, 1398.0, 1046.0, 1394.0, 1053.0, 1402.0, 1055.0, 1404.0, 1048.0, 1417.0, 1055.0, 1424.0, 1063.0, 1424.0, 1076.0, 1438.0, 1075.0, 1450.0, 1077.0, 1454.0, 1115.0, 1458.0, 1126.0, 1464.0, 1134.0, 1453.0, 1135.0, 1456.0, 1151.0, 1458.0, 1167.0, 1468.0, 1179.0, 1466.0, 1186.0, 1458.0, 1183.0, 1449.0, 1195.0, 1442.0, 1187.0, 1428.0, 1198.0, 1417.0, 1186.0, 1398.0, 1174.0, 1388.0, 1151.0, 1381.0, 1153.0, 1378.0, 1145.0, 1371.0, 1142.0, 1363.0, 1137.0, 1353.0, 1131.0, 1340.0, 1129.0, 1336.0, 1115.0, 1328.0, 1110.0, 1322.0, 1096.0, 1322.0, 1087.0, 1329.0, 1086.0, 1333.0, 1082.0, 1342.0, 1079.0, 1336.0, 1072.0, 1334.0, 1065.0, 1327.0, 1063.0, 1323.0, 1053.0]], "capital": [1377.0, 1075.0], "configuredProductions": {"6": 2900.0}, "id": 15, "locationType": "province", "name": "Lemberg", "neighbourIds": [16, 21, 51, 148, 149, 151], "ownerId": 8, "population": 100000, "productionType": 6}, {"borders": [[1252.0, 1059.0, 1268.0, 1065.0, 1291.0, 1052.0, 1303.0, 1044.0, 1306.0, 1037.0, 1313.0, 1040.0, 1317.0, 1034.0, 1322.0, 1043.0, 1319.0, 1050.0, 1323.0, 1053.0, 1327.0, 1063.0, 1334.0, 1065.0, 1336.0, 1072.0, 1342.0, 1079.0, 1333.0, 1082.0, 1329.0, 1086.0, 1322.0, 1087.0, 1322.0, 1096.0, 1328.0, 1110.0, 1336.0, 1115.0, 1340.0, 1129.0, 1338.0, 1138.0, 1328.0, 1147.0, 1292.0, 1122.0, 1286.0, 1128.0, 1277.0, 1128.0, 1279.0, 1137.0, 1264.0, 1136.0, 1235.0, 1131.0, 1228.0, 1136.0, 1223.0, 1134.0, 1213.0, 1124.0, 1192.0, 1132.0, 1180.0, 1131.0, 1171.0, 1137.0, 1164.0, 1131.0, 1174.0, 1128.0, 1182.0, 1119.0, 1186.0, 1109.0, 1195.0, 1102.0, 1207.0, 1095.0, 1211.0, 1076.0, 1219.0, 1077.0, 1226.0, 1067.0, 1222.0, 1060.0]], "capital": [1244.0, 1063.0], "configuredProductions": {"0": 2900.0}, "id": 16, "locationType": "province", "name": "Cracow", "neighbourIds": [3, 14, 15, 17, 20, 21, 145, 148], "ownerId": 8, "population": 100000}, {"borders": [[1171.0, 1137.0, 1180.0, 1131.0, 1192.0, 1132.0, 1189.0, 1137.0, 1184.0, 1150.0, 1176.0, 1161.0, 1182.0, 1178.0, 1186.0, 1181.0, 1190.0, 1189.0, 1190.0, 1209.0, 1186.0, 1221.0, 1180.0, 1218.0, 1163.0, 1232.0, 1151.0, 1235.0, 1149.0, 1242.0, 1144.0, 1247.0, 1140.0, 1261.0, 1116.0, 1255.0, 1111.0, 1261.0, 1098.0, 1248.0, 1090.0, 1249.0, 1084.0, 1243.0, 1069.0, 1239.0, 1058.0, 1238.0, 1047.0, 1243.0, 1028.0, 1238.0, 1017.0, 1230.0, 1017.0, 1208.0, 1032.0, 1202.0, 1046.0, 1203.0, 1066.0, 1193.0, 1076.0, 1192.0, 1082.0, 1183.0, 1075.0, 1177.0, 1080.0, 1172.0, 1083.0, 1165.0, 1090.0, 1157.0, 1098.0, 1151.0, 1108.0, 1146.0, 1122.0, 1142.0, 1144.0, 1141.0, 1151.0, 1130.0, 1157.0, 1126.0, 1164.0, 1131.0]], "capital": [1122.0, 1167.0], "configuredProductions": {"3": 2900.0}, "id": 17, "locationType": "province", "name": "Vienna", "neighbourIds": [12, 13, 14, 16, 18, 20, 23], "ownerId": 8, "population": 100000, "productionType": 3}, {"borders": [[1011.0, 1231.0, 1017.0, 1230.0, 1028.0, 1238.0, 1047.0, 1243.0, 1058.0, 1238.0, 1069.0, 1239.0, 1084.0, 1243.0, 1090.0, 1249.0, 1098.0, 1248.0, 1111.0, 1261.0, 1116.0, 1255.0, 1140.0, 1261.0, 1146.0, 1273.0, 1151.0, 1274.0, 1154.0, 1281.0, 1161.0, 1279.0, 1165.0, 1287.0, 1165.0, 1293.0, 1167.0, 1304.0, 1162.0, 1317.0, 1150.0, 1317.0, 1141.0, 1315.0, 1135.0, 1311.0, 1128.0, 1312.0, 1118.0, 1312.0, 1116.0, 1317.0, 1110.0, 1319.0, 1106.0, 1310.0, 1096.0, 1307.0, 1090.0, 1314.0, 1092.0, 1329.0, 1100.0, 1339.0, 1104.0, 1347.0, 1106.0, 1353.0, 1102.0, 1359.0, 1092.0, 1350.0, 1082.0, 1346.0, 1076.0, 1349.0, 1064.0, 1337.0, 1061.0, 1323.0, 1061.0, 1312.0, 1059.0, 1303.0, 1044.0, 1299.0, 1038.0, 1309.0, 1038.0, 1318.0, 1033.0, 1317.0, 1031.0, 1326.0, 1026.0, 1325.0, 1024.0, 1315.0, 1018.0, 1310.0, 1018.0, 1307.0, 1016.0, 1301.0, 1016.0, 1290.0, 1022.0, 1286.0, 1023.0, 1283.0, 1021.0, 1277.0, 1012.0, 1280.0, 1004.0, 1275.0, 1023.0, 1255.0, 1012.0, 1249.0, 1018.0, 1244.0, 1007.0, 1235.0]], "capital": [1026.0, 1280.0], "configuredProductions": {"4": 2900.0}, "id": 18, "locationType": "province", "name": "Trieste", "neighbourIds": [12, 17, 19, 23, 60, 193], "ownerId": 8, "population": 100000, "productionType": 4}, {"borders": [[1082.0, 1346.0, 1092.0, 1350.0, 1102.0, 1359.0, 1106.0, 1353.0, 1104.0, 1347.0, 1100.0, 1339.0, 1092.0, 1329.0, 1090.0, 1314.0, 1096.0, 1307.0, 1106.0, 1310.0, 1110.0, 1319.0, 1116.0, 1317.0, 1118.0, 1312.0, 1128.0, 1312.0, 1135.0, 1311.0, 1141.0, 1315.0, 1150.0, 1317.0, 1162.0, 1317.0, 1166.0, 1320.0, 1178.0, 1319.0, 1196.0, 1319.0, 1204.0, 1322.0, 1204.0, 1329.0, 1224.0, 1327.0, 1224.0, 1340.0, 1219.0, 1354.0, 1238.0, 1374.0, 1233.0, 1377.0, 1225.0, 1372.0, 1221.0, 1376.0, 1224.0, 1387.0, 1217.0, 1395.0, 1214.0, 1393.0, 1203.0, 1401.0, 1203.0, 1408.0, 1200.0, 1416.0, 1194.0, 1425.0, 1192.0, 1433.0, 1194.0, 1445.0, 1188.0, 1453.0, 1181.0, 1447.0, 1172.0, 1442.0, 1168.0, 1436.0, 1154.0, 1431.0, 1147.0, 1429.0, 1136.0, 1422.0, 1147.0, 1422.0, 1159.0, 1428.0, 1166.0, 1430.0, 1161.0, 1425.0, 1144.0, 1414.0, 1136.0, 1407.0, 1130.0, 1405.0, 1125.0, 1401.0, 1108.0, 1395.0, 1108.0, 1400.0, 1105.0, 1401.0, 1096.0, 1387.0, 1091.0, 1384.0, 1082.0, 1371.0, 1077.0, 1369.0, 1072.0, 1363.0, 1075.0, 1359.0, 1081.0, 1361.0, 1088.0, 1366.0, 1090.0, 1359.0, 1076.0, 1349.0]], "capital": [1192.0, 1379.0], "configuredProductions": {"1": 2900.0}, "id": 19, "locationType": "province", "name": "Serajevo", "neighbourIds": [18, 23, 45, 46, 191, 192, 193], "ownerId": 8, "population": 100000, "productionType": 1}, {"borders": [[1223.0, 1134.0, 1228.0, 1136.0, 1235.0, 1131.0, 1264.0, 1136.0, 1279.0, 1137.0, 1277.0, 1128.0, 1286.0, 1128.0, 1292.0, 1122.0, 1328.0, 1147.0, 1322.0, 1155.0, 1310.0, 1160.0, 1310.0, 1166.0, 1304.0, 1170.0, 1298.0, 1166.0, 1291.0, 1171.0, 1290.0, 1176.0, 1284.0, 1177.0, 1282.0, 1183.0, 1278.0, 1198.0, 1270.0, 1198.0, 1267.0, 1206.0, 1262.0, 1210.0, 1257.0, 1218.0, 1256.0, 1224.0, 1250.0, 1231.0, 1244.0, 1231.0, 1228.0, 1233.0, 1216.0, 1233.0, 1204.0, 1230.0, 1198.0, 1231.0, 1186.0, 1221.0, 1190.0, 1209.0, 1190.0, 1189.0, 1186.0, 1181.0, 1182.0, 1178.0, 1176.0, 1161.0, 1184.0, 1150.0, 1189.0, 1137.0, 1192.0, 1132.0, 1213.0, 1124.0]], "capital": [1218.0, 1201.0], "configuredProductions": {"0": 2900.0}, "id": 20, "locationType": "province", "name": "Budapest", "neighbourIds": [16, 17, 21, 23], "ownerId": 8, "population": 100000}, {"borders": [[1334.0, 1236.0, 1330.0, 1248.0, 1334.0, 1257.0, 1354.0, 1279.0, 1347.0, 1277.0, 1328.0, 1277.0, 1313.0, 1268.0, 1302.0, 1268.0, 1293.0, 1265.0, 1285.0, 1264.0, 1281.0, 1260.0, 1288.0, 1247.0, 1286.0, 1240.0, 1270.0, 1243.0, 1257.0, 1240.0, 1250.0, 1231.0, 1256.0, 1224.0, 1257.0, 1218.0, 1262.0, 1210.0, 1267.0, 1206.0, 1270.0, 1198.0, 1278.0, 1198.0, 1282.0, 1183.0, 1284.0, 1177.0, 1290.0, 1176.0, 1291.0, 1171.0, 1298.0, 1166.0, 1304.0, 1170.0, 1310.0, 1166.0, 1310.0, 1160.0, 1322.0, 1155.0, 1328.0, 1147.0, 1338.0, 1138.0, 1340.0, 1129.0, 1353.0, 1131.0, 1363.0, 1137.0, 1371.0, 1142.0, 1378.0, 1145.0, 1381.0, 1153.0, 1388.0, 1151.0, 1398.0, 1174.0, 1417.0, 1186.0, 1428.0, 1198.0, 1434.0, 1208.0, 1431.0, 1221.0, 1426.0, 1222.0, 1420.0, 1220.0, 1414.0, 1216.0, 1398.0, 1215.0, 1380.0, 1207.0, 1352.0, 1217.0, 1340.0, 1225.0]], "capital": [1299.0, 1195.0], "configuredProductions": {"0": 5800.0}, "id": 21, "locationType": "province", "name": "<PERSON><PERSON><PERSON><PERSON>", "neighbourIds": [15, 16, 20, 22, 23, 51], "ownerId": 8, "population": 100000}, {"borders": [[1438.0, 1206.0, 1450.0, 1213.0, 1466.0, 1227.0, 1467.0, 1232.0, 1468.0, 1241.0, 1482.0, 1261.0, 1477.0, 1271.0, 1470.0, 1284.0, 1464.0, 1286.0, 1457.0, 1290.0, 1445.0, 1292.0, 1439.0, 1296.0, 1432.0, 1288.0, 1409.0, 1294.0, 1404.0, 1288.0, 1395.0, 1291.0, 1384.0, 1293.0, 1378.0, 1286.0, 1372.0, 1291.0, 1378.0, 1299.0, 1372.0, 1304.0, 1363.0, 1301.0, 1353.0, 1303.0, 1352.0, 1295.0, 1349.0, 1289.0, 1355.0, 1287.0, 1354.0, 1279.0, 1334.0, 1257.0, 1330.0, 1248.0, 1334.0, 1236.0, 1340.0, 1225.0, 1352.0, 1217.0, 1380.0, 1207.0, 1398.0, 1215.0, 1414.0, 1216.0, 1420.0, 1220.0, 1426.0, 1222.0, 1431.0, 1221.0, 1434.0, 1208.0]], "capital": [1437.0, 1279.0], "configuredProductions": {"3": 5800.0}, "id": 22, "locationType": "province", "name": "Kronstadt", "neighbourIds": [21, 23, 48, 49, 51, 52], "ownerId": 8, "population": 100000, "productionType": 3}, {"borders": [[1224.0, 1327.0, 1204.0, 1329.0, 1204.0, 1322.0, 1196.0, 1319.0, 1178.0, 1319.0, 1166.0, 1320.0, 1162.0, 1317.0, 1167.0, 1304.0, 1165.0, 1293.0, 1165.0, 1287.0, 1161.0, 1279.0, 1154.0, 1281.0, 1151.0, 1274.0, 1146.0, 1273.0, 1140.0, 1261.0, 1144.0, 1247.0, 1149.0, 1242.0, 1156.0, 1239.0, 1164.0, 1240.0, 1174.0, 1232.0, 1184.0, 1227.0, 1186.0, 1221.0, 1198.0, 1231.0, 1204.0, 1230.0, 1216.0, 1233.0, 1228.0, 1233.0, 1244.0, 1231.0, 1250.0, 1231.0, 1257.0, 1240.0, 1270.0, 1243.0, 1286.0, 1240.0, 1288.0, 1247.0, 1281.0, 1260.0, 1285.0, 1264.0, 1293.0, 1265.0, 1302.0, 1268.0, 1313.0, 1268.0, 1328.0, 1277.0, 1347.0, 1277.0, 1354.0, 1279.0, 1355.0, 1287.0, 1349.0, 1289.0, 1352.0, 1295.0, 1353.0, 1303.0, 1344.0, 1303.0, 1342.0, 1313.0, 1331.0, 1324.0, 1334.0, 1331.0, 1329.0, 1340.0, 1328.0, 1347.0, 1323.0, 1349.0, 1318.0, 1343.0, 1306.0, 1341.0, 1304.0, 1335.0, 1298.0, 1333.0, 1282.0, 1342.0, 1272.0, 1337.0, 1271.0, 1331.0, 1263.0, 1331.0, 1257.0, 1337.0, 1251.0, 1338.0, 1242.0, 1335.0, 1234.0, 1335.0, 1234.0, 1329.0]], "capital": [1251.0, 1257.0], "configuredProductions": {"0": 2900.0}, "id": 23, "locationType": "province", "name": "Szeged", "neighbourIds": [17, 18, 19, 20, 21, 22, 45, 47, 49], "ownerId": 8, "population": 100000}, {"borders": [[1025.0, 788.0, 1025.0, 794.0, 1030.0, 797.0, 1028.0, 800.0, 1022.0, 805.0, 1022.0, 812.0, 1006.0, 807.0, 1010.0, 803.0, 1009.0, 797.0, 996.0, 798.0, 991.0, 793.0, 994.0, 787.0, 989.0, 777.0, 992.0, 776.0, 1000.0, 777.0, 1002.0, 771.0, 996.0, 763.0, 1012.0, 768.0, 1009.0, 773.0, 1008.0, 771.0, 1006.0, 777.0, 1014.0, 781.0, 1017.0, 774.0, 1021.0, 780.0, 1023.0, 771.0, 1016.0, 765.0, 1032.0, 767.0, 1036.0, 770.0, 1035.0, 781.0], [1010.0, 823.0, 1006.0, 814.0, 1011.0, 815.0, 1020.0, 822.0, 1020.0, 825.0, 1015.0, 830.0, 1016.0, 833.0, 1012.0, 838.0], [999.0, 833.0, 994.0, 829.0, 989.0, 831.0, 984.0, 829.0, 984.0, 827.0, 990.0, 825.0, 984.0, 819.0, 987.0, 814.0, 994.0, 815.0, 1000.0, 821.0, 1004.0, 821.0, 1007.0, 827.0, 1007.0, 831.0, 1006.0, 833.0], [978.0, 811.0, 985.0, 803.0, 985.0, 808.0, 979.0, 817.0, 976.0, 824.0, 970.0, 824.0], [978.0, 803.0, 966.0, 801.0, 958.0, 789.0, 959.0, 786.0, 965.0, 787.0, 972.0, 783.0, 973.0, 791.0, 975.0, 792.0, 977.0, 788.0, 977.0, 783.0, 982.0, 787.0, 980.0, 793.0, 982.0, 795.0], [1101.0, 809.0, 1105.0, 822.0, 1099.0, 819.0, 1094.0, 812.0, 1098.0, 805.0]], "capital": [1027.0, 778.0], "configuredProductions": {"0": 5800.0}, "id": 24, "locationType": "province", "name": "Copenhagen", "neighbourIds": [256, 257, 258, 259, 260], "ownerId": 15, "population": 100000}, {"borders": [[918.0, 777.0, 913.0, 771.0, 906.0, 777.0, 906.0, 767.0, 910.0, 760.0, 920.0, 755.0, 917.0, 743.0, 923.0, 741.0, 938.0, 734.0, 955.0, 735.0, 969.0, 738.0, 974.0, 740.0, 978.0, 734.0, 983.0, 741.0, 990.0, 741.0, 992.0, 744.0, 986.0, 751.0, 985.0, 755.0, 981.0, 754.0, 976.0, 757.0, 977.0, 749.0, 967.0, 754.0, 971.0, 761.0, 969.0, 767.0, 962.0, 765.0, 956.0, 775.0, 958.0, 781.0, 951.0, 785.0, 949.0, 790.0, 938.0, 784.0, 935.0, 779.0, 926.0, 779.0, 929.0, 787.0, 918.0, 787.0]], "capital": [963.0, 748.0], "configuredProductions": {"0": 2900.0}, "id": 25, "locationType": "province", "name": "Aarhus", "neighbourIds": [2, 26, 252, 256, 258, 333], "ownerId": 15, "population": 100000}, {"borders": [[955.0, 735.0, 938.0, 734.0, 923.0, 741.0, 917.0, 743.0, 910.0, 755.0, 909.0, 741.0, 914.0, 735.0, 912.0, 727.0, 916.0, 721.0, 919.0, 725.0, 924.0, 727.0, 925.0, 731.0, 932.0, 730.0, 935.0, 723.0, 943.0, 720.0, 944.0, 721.0, 942.0, 729.0, 951.0, 725.0, 951.0, 717.0, 952.0, 715.0, 959.0, 715.0, 968.0, 711.0, 974.0, 714.0, 980.0, 719.0, 977.0, 732.0, 971.0, 733.0, 969.0, 738.0], [988.0, 684.0, 986.0, 689.0, 990.0, 694.0, 990.0, 699.0, 986.0, 707.0, 982.0, 712.0, 977.0, 706.0, 969.0, 703.0, 956.0, 707.0, 950.0, 703.0, 946.0, 703.0, 944.0, 707.0, 933.0, 708.0, 929.0, 713.0, 927.0, 719.0, 920.0, 709.0, 928.0, 703.0, 932.0, 698.0, 940.0, 696.0, 959.0, 699.0, 970.0, 692.0, 979.0, 683.0, 992.0, 676.0, 994.0, 680.0]], "capital": [965.0, 721.0], "configuredProductions": {"1": 2900.0}, "id": 26, "locationType": "province", "name": "Aalborg", "neighbourIds": [25, 256, 331, 333], "ownerId": 15, "population": 100000, "productionType": 1}, {"borders": [[330.0, 133.0, 340.0, 122.0, 341.0, 131.0, 353.0, 132.0, 366.0, 129.0, 360.0, 125.0, 366.0, 120.0, 373.0, 120.0, 370.0, 116.0, 360.0, 117.0, 358.0, 108.0, 365.0, 109.0, 371.0, 101.0, 362.0, 96.0, 356.0, 95.0, 350.0, 86.0, 346.0, 87.0, 340.0, 79.0, 344.0, 75.0, 352.0, 80.0, 367.0, 83.0, 378.0, 91.0, 388.0, 97.0, 392.0, 90.0, 388.0, 91.0, 376.0, 82.0, 383.0, 81.0, 392.0, 85.0, 397.0, 83.0, 390.0, 77.0, 390.0, 69.0, 382.0, 65.0, 368.0, 62.0, 366.0, 66.0, 358.0, 56.0, 349.0, 48.0, 359.0, 45.0, 362.0, 49.0, 367.0, 44.0, 371.0, 45.0, 372.0, 52.0, 378.0, 56.0, 386.0, 55.0, 376.0, 45.0, 378.0, 42.0, 384.0, 47.0, 384.0, 35.0, 390.0, 34.0, 394.0, 36.0, 394.0, 41.0, 396.0, 47.0, 399.0, 50.0, 399.0, 58.0, 402.0, 59.0, 403.0, 45.0, 399.0, 37.0, 412.0, 41.0, 411.0, 38.0, 401.0, 29.0, 408.0, 27.0, 418.0, 37.0, 420.0, 45.0, 422.0, 46.0, 423.0, 57.0, 430.0, 59.0, 424.0, 63.0, 428.0, 69.0, 417.0, 75.0, 412.0, 67.0, 411.0, 81.0, 402.0, 86.0, 406.0, 87.0, 397.0, 97.0, 413.0, 90.0, 416.0, 94.0, 418.0, 103.0, 420.0, 119.0, 438.0, 131.0, 447.0, 143.0, 450.0, 155.0, 446.0, 161.0, 431.0, 169.0, 426.0, 178.0, 434.0, 189.0, 429.0, 191.0, 427.0, 195.0, 418.0, 199.0, 414.0, 194.0, 410.0, 201.0, 394.0, 195.0, 384.0, 181.0, 377.0, 173.0, 390.0, 173.0, 376.0, 156.0, 370.0, 158.0, 366.0, 153.0, 365.0, 149.0, 358.0, 149.0, 355.0, 143.0, 338.0, 140.0]], "capital": [361.0, 137.0], "configuredProductions": {"5": 2900.0}, "id": 27, "locationType": "province", "name": "Reykjavik", "neighbourIds": [28, 301, 302, 304], "ownerId": 15, "population": 100000, "productionType": 5}, {"borders": [[416.0, 84.0, 428.0, 79.0, 427.0, 88.0, 442.0, 76.0, 447.0, 66.0, 452.0, 67.0, 452.0, 73.0, 450.0, 81.0, 452.0, 91.0, 452.0, 99.0, 455.0, 100.0, 460.0, 86.0, 460.0, 81.0, 466.0, 80.0, 470.0, 83.0, 477.0, 81.0, 479.0, 88.0, 473.0, 95.0, 477.0, 99.0, 478.0, 109.0, 482.0, 93.0, 484.0, 89.0, 488.0, 90.0, 493.0, 93.0, 495.0, 101.0, 500.0, 101.0, 506.0, 97.0, 509.0, 97.0, 512.0, 102.0, 521.0, 103.0, 523.0, 99.0, 522.0, 93.0, 528.0, 90.0, 540.0, 91.0, 540.0, 97.0, 542.0, 99.0, 540.0, 105.0, 543.0, 111.0, 552.0, 107.0, 556.0, 109.0, 556.0, 112.0, 540.0, 115.0, 544.0, 121.0, 551.0, 123.0, 547.0, 130.0, 539.0, 133.0, 540.0, 138.0, 554.0, 137.0, 551.0, 144.0, 557.0, 143.0, 560.0, 154.0, 549.0, 159.0, 550.0, 163.0, 546.0, 165.0, 552.0, 171.0, 544.0, 177.0, 537.0, 172.0, 532.0, 173.0, 540.0, 176.0, 538.0, 183.0, 532.0, 183.0, 532.0, 186.0, 522.0, 177.0, 521.0, 187.0, 512.0, 191.0, 514.0, 194.0, 506.0, 198.0, 501.0, 196.0, 495.0, 201.0, 492.0, 189.0, 488.0, 196.0, 478.0, 196.0, 475.0, 199.0, 469.0, 195.0, 457.0, 200.0, 454.0, 195.0, 448.0, 200.0, 444.0, 197.0, 446.0, 193.0, 442.0, 191.0, 437.0, 196.0, 434.0, 189.0, 426.0, 178.0, 431.0, 169.0, 446.0, 161.0, 450.0, 155.0, 447.0, 143.0, 438.0, 131.0, 420.0, 119.0, 418.0, 103.0, 416.0, 94.0, 413.0, 90.0]], "capital": [448.0, 105.0], "configuredProductions": {"1": 2900.0}, "id": 28, "locationType": "province", "name": "<PERSON><PERSON><PERSON><PERSON>", "neighbourIds": [27, 301, 303, 304, 308], "ownerId": 15, "population": 100000, "productionType": 1}, {"borders": [[1393.0, 1403.0, 1393.0, 1407.0, 1388.0, 1411.0, 1385.0, 1418.0, 1388.0, 1423.0, 1395.0, 1429.0, 1396.0, 1436.0, 1394.0, 1439.0, 1392.0, 1447.0, 1393.0, 1456.0, 1396.0, 1459.0, 1400.0, 1459.0, 1407.0, 1461.0, 1419.0, 1462.0, 1420.0, 1467.0, 1414.0, 1466.0, 1410.0, 1471.0, 1410.0, 1477.0, 1406.0, 1479.0, 1398.0, 1477.0, 1394.0, 1469.0, 1386.0, 1463.0, 1374.0, 1465.0, 1362.0, 1463.0, 1354.0, 1468.0, 1334.0, 1449.0, 1334.0, 1440.0, 1336.0, 1434.0, 1345.0, 1423.0, 1353.0, 1415.0, 1356.0, 1407.0, 1347.0, 1404.0, 1339.0, 1399.0, 1335.0, 1389.0, 1335.0, 1381.0, 1340.0, 1372.0, 1348.0, 1367.0, 1357.0, 1375.0, 1356.0, 1377.0, 1351.0, 1377.0, 1349.0, 1381.0, 1351.0, 1386.0, 1359.0, 1388.0, 1365.0, 1385.0, 1372.0, 1385.0, 1387.0, 1389.0]], "capital": [1370.0, 1425.0], "configuredProductions": {"3": 2900.0}, "id": 29, "locationType": "province", "name": "Sofia", "neighbourIds": [30, 31, 34, 37, 47, 49], "ownerId": 11, "population": 100000, "productionType": 3}, {"borders": [[1493.0, 1375.0, 1492.0, 1384.0, 1496.0, 1392.0, 1493.0, 1408.0, 1484.0, 1414.0, 1458.0, 1423.0, 1448.0, 1417.0, 1436.0, 1420.0, 1415.0, 1428.0, 1395.0, 1429.0, 1388.0, 1423.0, 1385.0, 1418.0, 1388.0, 1411.0, 1393.0, 1407.0, 1393.0, 1403.0, 1387.0, 1389.0, 1395.0, 1391.0, 1402.0, 1393.0, 1408.0, 1389.0, 1412.0, 1387.0, 1416.0, 1387.0, 1420.0, 1392.0, 1425.0, 1390.0, 1433.0, 1388.0, 1440.0, 1390.0, 1445.0, 1387.0, 1452.0, 1387.0, 1461.0, 1389.0, 1474.0, 1373.0, 1480.0, 1371.0, 1485.0, 1366.0]], "capital": [1410.0, 1400.0], "configuredProductions": {"0": 2900.0}, "id": 30, "locationType": "province", "name": "Pleven", "neighbourIds": [29, 31, 32, 48, 49], "ownerId": 11, "population": 100000}, {"borders": [[1419.0, 1462.0, 1407.0, 1461.0, 1400.0, 1459.0, 1396.0, 1459.0, 1393.0, 1456.0, 1392.0, 1447.0, 1394.0, 1439.0, 1396.0, 1436.0, 1395.0, 1429.0, 1415.0, 1428.0, 1436.0, 1420.0, 1448.0, 1417.0, 1458.0, 1423.0, 1484.0, 1414.0, 1492.0, 1422.0, 1498.0, 1435.0, 1500.0, 1462.0, 1492.0, 1465.0, 1486.0, 1461.0, 1483.0, 1461.0, 1483.0, 1464.0, 1481.0, 1471.0, 1466.0, 1475.0, 1462.0, 1481.0, 1456.0, 1479.0, 1452.0, 1474.0, 1440.0, 1471.0, 1429.0, 1477.0, 1422.0, 1475.0, 1420.0, 1467.0]], "capital": [1425.0, 1457.0], "configuredProductions": {"4": 2900.0}, "id": 31, "locationType": "province", "name": "Plovdiv", "neighbourIds": [29, 30, 32, 33, 34, 168, 169], "ownerId": 11, "population": 100000, "productionType": 4}, {"borders": [[1500.0, 1462.0, 1498.0, 1435.0, 1492.0, 1422.0, 1484.0, 1414.0, 1493.0, 1408.0, 1496.0, 1392.0, 1492.0, 1384.0, 1493.0, 1375.0, 1485.0, 1366.0, 1493.0, 1364.0, 1500.0, 1359.0, 1512.0, 1358.0, 1521.0, 1368.0, 1528.0, 1363.0, 1534.0, 1369.0, 1538.0, 1364.0, 1546.0, 1370.0, 1556.0, 1369.0, 1569.0, 1371.0, 1569.0, 1379.0, 1568.0, 1384.0, 1562.0, 1389.0, 1556.0, 1385.0, 1550.0, 1389.0, 1548.0, 1395.0, 1544.0, 1396.0, 1545.0, 1402.0, 1543.0, 1409.0, 1546.0, 1417.0, 1544.0, 1421.0, 1540.0, 1422.0, 1540.0, 1425.0, 1526.0, 1432.0, 1521.0, 1432.0, 1520.0, 1434.0, 1523.0, 1434.0, 1528.0, 1437.0, 1536.0, 1435.0, 1539.0, 1437.0, 1540.0, 1446.0, 1533.0, 1448.0, 1532.0, 1451.0, 1528.0, 1454.0, 1518.0, 1450.0, 1511.0, 1460.0]], "capital": [1538.0, 1390.0], "configuredProductions": {"0": 2900.0}, "id": 32, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [30, 31, 33, 48, 50, 164, 165, 168], "ownerId": 11, "population": 100000}, {"borders": [[1481.0, 1471.0, 1483.0, 1464.0, 1483.0, 1461.0, 1486.0, 1461.0, 1492.0, 1465.0, 1500.0, 1462.0, 1511.0, 1460.0, 1518.0, 1450.0, 1528.0, 1454.0, 1532.0, 1451.0, 1533.0, 1448.0, 1540.0, 1446.0, 1548.0, 1453.0, 1552.0, 1459.0, 1550.0, 1464.0, 1558.0, 1469.0, 1566.0, 1481.0, 1578.0, 1489.0, 1591.0, 1491.0, 1592.0, 1497.0, 1590.0, 1506.0, 1588.0, 1507.0, 1580.0, 1505.0, 1571.0, 1501.0, 1564.0, 1501.0, 1558.0, 1505.0, 1553.0, 1507.0, 1547.0, 1506.0, 1541.0, 1511.0, 1536.0, 1514.0, 1534.0, 1519.0, 1529.0, 1521.0, 1525.0, 1529.0, 1514.0, 1533.0, 1512.0, 1537.0, 1505.0, 1539.0, 1504.0, 1545.0, 1490.0, 1555.0, 1491.0, 1550.0, 1486.0, 1545.0, 1492.0, 1540.0, 1501.0, 1537.0, 1510.0, 1531.0, 1504.0, 1531.0, 1490.0, 1533.0, 1486.0, 1531.0, 1484.0, 1527.0, 1487.0, 1524.0, 1485.0, 1519.0, 1489.0, 1516.0, 1492.0, 1515.0, 1492.0, 1510.0, 1488.0, 1506.0, 1490.0, 1501.0, 1493.0, 1501.0, 1494.0, 1494.0, 1500.0, 1495.0, 1500.0, 1490.0, 1498.0, 1485.0, 1498.0, 1481.0, 1493.0, 1476.0, 1487.0, 1477.0], [1480.0, 1553.0, 1480.0, 1557.0, 1473.0, 1561.0, 1470.0, 1559.0, 1471.0, 1557.0], [1458.0, 1577.0, 1454.0, 1573.0, 1447.0, 1574.0, 1448.0, 1570.0, 1453.0, 1569.0, 1457.0, 1565.0, 1461.0, 1565.0], [1543.0, 1528.0, 1539.0, 1530.0, 1538.0, 1527.0, 1544.0, 1525.0]], "capital": [1586.0, 1498.0], "configuredProductions": {"4": 2900.0}, "id": 33, "locationType": "province", "name": "Constantinople", "neighbourIds": [31, 32, 34, 165, 166, 167, 168], "ownerId": 7, "population": 100000, "productionType": 4}, {"borders": [[1381.0, 1569.0, 1388.0, 1572.0, 1394.0, 1577.0, 1392.0, 1578.0, 1380.0, 1576.0, 1376.0, 1571.0, 1372.0, 1561.0, 1366.0, 1555.0, 1357.0, 1550.0, 1359.0, 1546.0, 1358.0, 1541.0, 1354.0, 1540.0, 1352.0, 1544.0, 1344.0, 1549.0, 1348.0, 1551.0, 1349.0, 1555.0, 1345.0, 1563.0, 1348.0, 1570.0, 1329.0, 1579.0, 1326.0, 1568.0, 1314.0, 1560.0, 1322.0, 1549.0, 1322.0, 1541.0, 1326.0, 1533.0, 1323.0, 1528.0, 1331.0, 1520.0, 1344.0, 1514.0, 1342.0, 1509.0, 1342.0, 1504.0, 1345.0, 1502.0, 1352.0, 1509.0, 1363.0, 1503.0, 1367.0, 1500.0, 1380.0, 1501.0, 1387.0, 1507.0, 1394.0, 1505.0, 1386.0, 1498.0, 1374.0, 1474.0, 1382.0, 1473.0, 1386.0, 1463.0, 1394.0, 1469.0, 1398.0, 1477.0, 1406.0, 1479.0, 1410.0, 1477.0, 1410.0, 1471.0, 1414.0, 1466.0, 1420.0, 1467.0, 1422.0, 1475.0, 1429.0, 1477.0, 1440.0, 1471.0, 1452.0, 1474.0, 1456.0, 1479.0, 1462.0, 1481.0, 1466.0, 1475.0, 1481.0, 1471.0, 1487.0, 1477.0, 1493.0, 1476.0, 1498.0, 1481.0, 1498.0, 1485.0, 1500.0, 1490.0, 1500.0, 1495.0, 1494.0, 1494.0, 1493.0, 1501.0, 1490.0, 1501.0, 1488.0, 1506.0, 1492.0, 1510.0, 1492.0, 1515.0, 1489.0, 1516.0, 1485.0, 1519.0, 1480.0, 1526.0, 1474.0, 1521.0, 1463.0, 1521.0, 1460.0, 1523.0, 1450.0, 1519.0, 1449.0, 1510.0, 1444.0, 1509.0, 1442.0, 1519.0, 1438.0, 1518.0, 1432.0, 1521.0, 1431.0, 1525.0, 1426.0, 1527.0, 1423.0, 1519.0, 1417.0, 1521.0, 1412.0, 1529.0, 1404.0, 1530.0, 1400.0, 1527.0, 1394.0, 1528.0, 1392.0, 1536.0, 1400.0, 1541.0, 1396.0, 1544.0, 1400.0, 1549.0, 1402.0, 1547.0, 1416.0, 1557.0, 1416.0, 1559.0, 1420.0, 1563.0, 1416.0, 1566.0, 1412.0, 1564.0, 1408.0, 1558.0, 1404.0, 1556.0, 1398.0, 1554.0, 1394.0, 1555.0, 1393.0, 1558.0, 1398.0, 1561.0, 1402.0, 1565.0, 1406.0, 1567.0, 1406.0, 1571.0, 1403.0, 1574.0, 1397.0, 1570.0, 1394.0, 1563.0, 1386.0, 1559.0, 1380.0, 1559.0, 1378.0, 1563.0], [1434.0, 1536.0, 1432.0, 1539.0, 1427.0, 1538.0, 1424.0, 1534.0, 1426.0, 1531.0, 1430.0, 1529.0], [1460.0, 1547.0, 1458.0, 1543.0, 1464.0, 1541.0, 1468.0, 1542.0, 1468.0, 1545.0]], "capital": [1362.0, 1539.0], "configuredProductions": {"6": 5800.0}, "id": 34, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [29, 31, 33, 35, 37, 55, 169, 170], "ownerId": 7, "population": 100000, "productionType": 6}, {"borders": [[1283.0, 1618.0, 1279.0, 1623.0, 1277.0, 1616.0, 1271.0, 1609.0, 1267.0, 1601.0, 1264.0, 1605.0, 1260.0, 1603.0, 1261.0, 1597.0, 1258.0, 1597.0, 1257.0, 1591.0, 1251.0, 1589.0, 1250.0, 1581.0, 1242.0, 1570.0, 1234.0, 1563.0, 1229.0, 1561.0, 1228.0, 1555.0, 1232.0, 1555.0, 1234.0, 1545.0, 1228.0, 1545.0, 1226.0, 1534.0, 1230.0, 1533.0, 1232.0, 1528.0, 1242.0, 1526.0, 1246.0, 1522.0, 1248.0, 1516.0, 1257.0, 1510.0, 1261.0, 1513.0, 1268.0, 1515.0, 1272.0, 1513.0, 1275.0, 1519.0, 1277.0, 1527.0, 1282.0, 1532.0, 1296.0, 1531.0, 1305.0, 1523.0, 1312.0, 1531.0, 1323.0, 1528.0, 1326.0, 1533.0, 1322.0, 1541.0, 1322.0, 1549.0, 1314.0, 1560.0, 1326.0, 1568.0, 1329.0, 1579.0, 1331.0, 1587.0, 1325.0, 1587.0, 1316.0, 1585.0, 1310.0, 1577.0, 1293.0, 1583.0, 1288.0, 1587.0, 1288.0, 1595.0, 1282.0, 1598.0, 1289.0, 1620.0]], "capital": [1278.0, 1587.0], "configuredProductions": {"4": 2900.0}, "id": 35, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [34, 37, 55, 189, 190], "ownerId": 7, "population": 100000, "productionType": 4}, {"borders": [[1279.0, 1415.0, 1282.0, 1419.0, 1287.0, 1419.0, 1291.0, 1428.0, 1293.0, 1435.0, 1297.0, 1439.0, 1306.0, 1436.0, 1312.0, 1446.0, 1312.0, 1450.0, 1308.0, 1449.0, 1304.0, 1452.0, 1297.0, 1455.0, 1290.0, 1453.0, 1286.0, 1455.0, 1282.0, 1463.0, 1274.0, 1462.0, 1264.0, 1470.0, 1256.0, 1482.0, 1247.0, 1473.0, 1227.0, 1467.0, 1229.0, 1462.0, 1222.0, 1460.0, 1222.0, 1454.0, 1225.0, 1450.0, 1230.0, 1448.0, 1235.0, 1448.0, 1240.0, 1445.0, 1243.0, 1441.0, 1248.0, 1439.0, 1252.0, 1444.0, 1256.0, 1440.0, 1250.0, 1433.0, 1242.0, 1425.0, 1233.0, 1420.0, 1227.0, 1421.0, 1224.0, 1415.0, 1217.0, 1412.0, 1216.0, 1401.0, 1223.0, 1397.0, 1223.0, 1399.0, 1226.0, 1401.0, 1231.0, 1398.0, 1230.0, 1393.0, 1242.0, 1395.0, 1249.0, 1399.0, 1256.0, 1409.0, 1262.0, 1409.0, 1267.0, 1408.0, 1272.0, 1415.0]], "capital": [1260.0, 1415.0], "configuredProductions": {"3": 2900.0}, "id": 36, "locationType": "province", "name": "Novi-Bazar", "neighbourIds": [37, 45, 46, 47], "ownerId": 7, "population": 100000, "productionType": 3}, {"borders": [[1354.0, 1468.0, 1362.0, 1463.0, 1374.0, 1465.0, 1386.0, 1463.0, 1382.0, 1473.0, 1374.0, 1474.0, 1386.0, 1498.0, 1394.0, 1505.0, 1387.0, 1507.0, 1380.0, 1501.0, 1367.0, 1500.0, 1363.0, 1503.0, 1352.0, 1509.0, 1345.0, 1502.0, 1342.0, 1504.0, 1342.0, 1509.0, 1344.0, 1514.0, 1331.0, 1520.0, 1323.0, 1528.0, 1312.0, 1531.0, 1305.0, 1523.0, 1296.0, 1531.0, 1282.0, 1532.0, 1277.0, 1527.0, 1275.0, 1519.0, 1272.0, 1513.0, 1268.0, 1515.0, 1261.0, 1513.0, 1257.0, 1510.0, 1248.0, 1516.0, 1246.0, 1522.0, 1242.0, 1526.0, 1232.0, 1528.0, 1231.0, 1518.0, 1232.0, 1511.0, 1228.0, 1505.0, 1231.0, 1499.0, 1237.0, 1496.0, 1236.0, 1489.0, 1232.0, 1481.0, 1227.0, 1467.0, 1247.0, 1473.0, 1256.0, 1482.0, 1264.0, 1470.0, 1274.0, 1462.0, 1282.0, 1463.0, 1286.0, 1455.0, 1290.0, 1453.0, 1297.0, 1455.0, 1304.0, 1452.0, 1308.0, 1449.0, 1312.0, 1450.0, 1324.0, 1453.0, 1334.0, 1449.0]], "capital": [1301.0, 1469.0], "configuredProductions": {"2": 5800.0}, "id": 37, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [29, 34, 35, 36, 46, 47, 190], "ownerId": 7, "population": 100000, "productionType": 2}, {"borders": [[1800.0, 1720.0, 1799.0, 1711.0, 1792.0, 1707.0, 1772.0, 1707.0, 1770.0, 1701.0, 1754.0, 1684.0, 1732.0, 1679.0, 1726.0, 1667.0, 1720.0, 1663.0, 1718.0, 1656.0, 1719.0, 1652.0, 1716.0, 1646.0, 1707.0, 1639.0, 1702.0, 1640.0, 1698.0, 1633.0, 1699.0, 1625.0, 1692.0, 1622.0, 1686.0, 1617.0, 1688.0, 1613.0, 1698.0, 1610.0, 1706.0, 1595.0, 1708.0, 1589.0, 1718.0, 1584.0, 1722.0, 1581.0, 1749.0, 1589.0, 1750.0, 1595.0, 1760.0, 1597.0, 1766.0, 1591.0, 1775.0, 1594.0, 1784.0, 1603.0, 1791.0, 1606.0, 1796.0, 1603.0, 1793.0, 1595.0, 1788.0, 1590.0, 1781.0, 1585.0, 1792.0, 1581.0, 1800.0, 1583.0, 1805.0, 1577.0, 1809.0, 1578.0, 1815.0, 1583.0, 1825.0, 1585.0, 1830.0, 1584.0, 1832.0, 1580.0, 1848.0, 1571.0, 1850.0, 1566.0, 1858.0, 1561.0, 1864.0, 1561.0, 1866.0, 1563.0, 1872.0, 1555.0, 1876.0, 1553.0, 1881.0, 1552.0, 1881.0, 1679.0, 1874.0, 1681.0, 1869.0, 1679.0, 1860.0, 1677.0, 1852.0, 1672.0, 1844.0, 1677.0, 1835.0, 1681.0, 1830.0, 1686.0, 1828.0, 1690.0, 1828.0, 1697.0, 1824.0, 1701.0, 1826.0, 1705.0, 1822.0, 1713.0, 1820.0, 1709.0, 1810.0, 1719.0]], "capital": [1746.0, 1639.0], "configuredProductions": {"0": 2900.0}, "id": 38, "locationType": "province", "name": "Konich", "neighbourIds": [39, 40, 43, 44, 175, 176], "ownerId": 7, "population": 100000}, {"borders": [[1794.0, 1466.0, 1790.0, 1460.0, 1783.0, 1459.0, 1768.0, 1463.0, 1756.0, 1470.0, 1748.0, 1466.0, 1750.0, 1462.0, 1746.0, 1458.0, 1742.0, 1458.0, 1740.0, 1453.0, 1740.0, 1447.0, 1738.0, 1445.0, 1736.0, 1438.0, 1732.0, 1435.0, 1740.0, 1429.0, 1750.0, 1424.0, 1761.0, 1427.0, 1774.0, 1424.0, 1788.0, 1424.0, 1794.0, 1421.0, 1806.0, 1421.0, 1810.0, 1420.0, 1809.0, 1413.0, 1812.0, 1411.0, 1815.0, 1412.0, 1818.0, 1421.0, 1826.0, 1425.0, 1832.0, 1431.0, 1840.0, 1432.0, 1848.0, 1425.0, 1854.0, 1423.0, 1860.0, 1425.0, 1865.0, 1431.0, 1865.0, 1442.0, 1870.0, 1446.0, 1882.0, 1445.0, 1881.0, 1552.0, 1876.0, 1553.0, 1872.0, 1555.0, 1866.0, 1563.0, 1864.0, 1561.0, 1858.0, 1561.0, 1850.0, 1566.0, 1848.0, 1571.0, 1832.0, 1580.0, 1830.0, 1584.0, 1825.0, 1585.0, 1815.0, 1583.0, 1809.0, 1578.0, 1805.0, 1577.0, 1800.0, 1573.0, 1796.0, 1568.0, 1784.0, 1567.0, 1780.0, 1561.0, 1780.0, 1556.0, 1773.0, 1551.0, 1774.0, 1539.0, 1770.0, 1532.0, 1769.0, 1520.0, 1776.0, 1518.0, 1777.0, 1515.0, 1782.0, 1514.0, 1791.0, 1506.0, 1792.0, 1500.0, 1800.0, 1497.0, 1803.0, 1492.0, 1814.0, 1489.0, 1816.0, 1485.0, 1816.0, 1479.0, 1820.0, 1473.0, 1816.0, 1470.0, 1803.0, 1471.0, 1800.0, 1470.0]], "capital": [1813.0, 1419.0], "configuredProductions": {"3": 5800.0}, "id": 39, "locationType": "province", "name": "Sinope", "neighbourIds": [38, 40, 41, 161, 163, 183, 327], "ownerId": 7, "population": 100000, "productionType": 3}, {"borders": [[1727.0, 1493.0, 1741.0, 1476.0, 1749.0, 1469.0, 1748.0, 1466.0, 1756.0, 1470.0, 1768.0, 1463.0, 1783.0, 1459.0, 1790.0, 1460.0, 1794.0, 1466.0, 1800.0, 1470.0, 1803.0, 1471.0, 1816.0, 1470.0, 1820.0, 1473.0, 1816.0, 1479.0, 1816.0, 1485.0, 1814.0, 1489.0, 1803.0, 1492.0, 1800.0, 1497.0, 1792.0, 1500.0, 1791.0, 1506.0, 1782.0, 1514.0, 1777.0, 1515.0, 1776.0, 1518.0, 1769.0, 1520.0, 1770.0, 1532.0, 1774.0, 1539.0, 1773.0, 1551.0, 1780.0, 1556.0, 1780.0, 1561.0, 1784.0, 1567.0, 1796.0, 1568.0, 1800.0, 1573.0, 1805.0, 1577.0, 1800.0, 1583.0, 1792.0, 1581.0, 1781.0, 1585.0, 1777.0, 1586.0, 1776.0, 1578.0, 1770.0, 1572.0, 1767.0, 1575.0, 1768.0, 1579.0, 1771.0, 1583.0, 1764.0, 1583.0, 1764.0, 1586.0, 1766.0, 1591.0, 1760.0, 1597.0, 1750.0, 1595.0, 1749.0, 1589.0, 1722.0, 1581.0, 1718.0, 1584.0, 1708.0, 1589.0, 1706.0, 1595.0, 1698.0, 1610.0, 1688.0, 1613.0, 1686.0, 1617.0, 1668.0, 1595.0, 1644.0, 1579.0, 1639.0, 1568.0, 1642.0, 1564.0, 1654.0, 1566.0, 1660.0, 1562.0, 1662.0, 1553.0, 1658.0, 1548.0, 1658.0, 1545.0, 1665.0, 1535.0, 1682.0, 1533.0, 1690.0, 1520.0, 1702.0, 1509.0, 1714.0, 1502.0]], "capital": [1751.0, 1533.0], "configuredProductions": {"0": 2900.0}, "id": 40, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [38, 39, 41, 42, 43, 327], "ownerId": 7, "population": 100000}, {"borders": [[1736.0, 1438.0, 1738.0, 1445.0, 1740.0, 1447.0, 1740.0, 1453.0, 1742.0, 1458.0, 1746.0, 1458.0, 1750.0, 1462.0, 1748.0, 1466.0, 1749.0, 1469.0, 1741.0, 1476.0, 1727.0, 1493.0, 1714.0, 1502.0, 1702.0, 1509.0, 1690.0, 1520.0, 1682.0, 1533.0, 1665.0, 1535.0, 1657.0, 1529.0, 1622.0, 1539.0, 1614.0, 1531.0, 1607.0, 1529.0, 1601.0, 1523.0, 1612.0, 1521.0, 1616.0, 1518.0, 1626.0, 1516.0, 1630.0, 1514.0, 1626.0, 1512.0, 1610.0, 1515.0, 1606.0, 1512.0, 1600.0, 1511.0, 1597.0, 1506.0, 1600.0, 1493.0, 1602.0, 1492.0, 1611.0, 1492.0, 1619.0, 1490.0, 1626.0, 1491.0, 1642.0, 1486.0, 1646.0, 1488.0, 1656.0, 1488.0, 1666.0, 1488.0, 1674.0, 1485.0, 1683.0, 1484.0, 1682.0, 1475.0, 1687.0, 1472.0, 1694.0, 1465.0, 1696.0, 1460.0, 1704.0, 1455.0, 1706.0, 1451.0, 1711.0, 1445.0, 1724.0, 1435.0, 1732.0, 1435.0]], "capital": [1604.0, 1504.0], "configuredProductions": {"3": 2900.0}, "id": 41, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [39, 40, 42, 165, 166, 167, 327], "ownerId": 7, "population": 100000, "productionType": 3}, {"borders": [[1607.0, 1529.0, 1614.0, 1531.0, 1622.0, 1539.0, 1657.0, 1529.0, 1665.0, 1535.0, 1658.0, 1545.0, 1658.0, 1548.0, 1662.0, 1553.0, 1660.0, 1562.0, 1654.0, 1566.0, 1642.0, 1564.0, 1639.0, 1568.0, 1644.0, 1579.0, 1638.0, 1581.0, 1632.0, 1593.0, 1612.0, 1610.0, 1606.0, 1610.0, 1600.0, 1605.0, 1594.0, 1608.0, 1587.0, 1609.0, 1578.0, 1609.0, 1567.0, 1604.0, 1558.0, 1606.0, 1547.0, 1602.0, 1539.0, 1609.0, 1526.0, 1611.0, 1517.0, 1611.0, 1514.0, 1605.0, 1508.0, 1601.0, 1514.0, 1594.0, 1519.0, 1584.0, 1517.0, 1581.0, 1509.0, 1581.0, 1500.0, 1584.0, 1493.0, 1589.0, 1489.0, 1587.0, 1490.0, 1577.0, 1489.0, 1570.0, 1490.0, 1564.0, 1494.0, 1561.0, 1507.0, 1548.0, 1523.0, 1537.0, 1531.0, 1537.0, 1534.0, 1544.0, 1538.0, 1546.0, 1547.0, 1545.0, 1550.0, 1541.0, 1546.0, 1538.0, 1547.0, 1534.0, 1561.0, 1533.0, 1554.0, 1541.0, 1556.0, 1541.0, 1559.0, 1539.0, 1568.0, 1539.0, 1570.0, 1535.0, 1587.0, 1535.0, 1590.0, 1537.0, 1594.0, 1536.0, 1602.0, 1535.0, 1597.0, 1531.0, 1590.0, 1529.0, 1601.0, 1523.0], [1503.0, 1614.0, 1498.0, 1614.0, 1492.0, 1611.0, 1496.0, 1608.0, 1493.0, 1605.0, 1484.0, 1608.0, 1481.0, 1605.0, 1480.0, 1601.0, 1493.0, 1598.0, 1493.0, 1595.0, 1498.0, 1593.0, 1501.0, 1598.0, 1498.0, 1603.0, 1502.0, 1605.0, 1508.0, 1611.0]], "capital": [1602.0, 1542.0], "configuredProductions": {"1": 2900.0}, "id": 42, "locationType": "province", "name": "Brusa", "neighbourIds": [40, 41, 43, 167, 168], "ownerId": 7, "population": 100000, "productionType": 1}, {"borders": [[1521.0, 1619.0, 1529.0, 1615.0, 1526.0, 1611.0, 1539.0, 1609.0, 1547.0, 1602.0, 1558.0, 1606.0, 1567.0, 1604.0, 1578.0, 1609.0, 1587.0, 1609.0, 1594.0, 1608.0, 1600.0, 1605.0, 1606.0, 1610.0, 1612.0, 1610.0, 1632.0, 1593.0, 1638.0, 1581.0, 1644.0, 1579.0, 1668.0, 1595.0, 1686.0, 1617.0, 1692.0, 1622.0, 1699.0, 1625.0, 1698.0, 1633.0, 1690.0, 1640.0, 1684.0, 1633.0, 1683.0, 1627.0, 1679.0, 1627.0, 1675.0, 1632.0, 1681.0, 1637.0, 1679.0, 1645.0, 1670.0, 1645.0, 1662.0, 1648.0, 1649.0, 1662.0, 1644.0, 1660.0, 1635.0, 1660.0, 1626.0, 1665.0, 1618.0, 1681.0, 1615.0, 1686.0, 1618.0, 1696.0, 1628.0, 1691.0, 1633.0, 1691.0, 1632.0, 1699.0, 1628.0, 1708.0, 1632.0, 1711.0, 1633.0, 1715.0, 1624.0, 1717.0, 1615.0, 1717.0, 1613.0, 1723.0, 1605.0, 1717.0, 1600.0, 1712.0, 1596.0, 1711.0, 1592.0, 1715.0, 1586.0, 1717.0, 1586.0, 1722.0, 1581.0, 1725.0, 1580.0, 1728.0, 1578.0, 1729.0, 1577.0, 1724.0, 1580.0, 1718.0, 1576.0, 1717.0, 1568.0, 1719.0, 1566.0, 1724.0, 1562.0, 1720.0, 1554.0, 1726.0, 1550.0, 1725.0, 1554.0, 1719.0, 1558.0, 1716.0, 1564.0, 1715.0, 1568.0, 1713.0, 1575.0, 1713.0, 1576.0, 1709.0, 1583.0, 1708.0, 1583.0, 1704.0, 1567.0, 1705.0, 1556.0, 1710.0, 1550.0, 1707.0, 1541.0, 1710.0, 1542.0, 1703.0, 1544.0, 1699.0, 1547.0, 1701.0, 1552.0, 1701.0, 1557.0, 1697.0, 1556.0, 1693.0, 1550.0, 1696.0, 1549.0, 1691.0, 1549.0, 1685.0, 1539.0, 1690.0, 1537.0, 1677.0, 1530.0, 1675.0, 1537.0, 1669.0, 1537.0, 1659.0, 1532.0, 1655.0, 1524.0, 1656.0, 1520.0, 1650.0, 1516.0, 1651.0, 1516.0, 1657.0, 1508.0, 1652.0, 1499.0, 1650.0, 1500.0, 1647.0, 1508.0, 1647.0, 1506.0, 1644.0, 1507.0, 1639.0, 1502.0, 1635.0, 1500.0, 1628.0, 1506.0, 1629.0, 1516.0, 1641.0, 1518.0, 1639.0, 1522.0, 1641.0, 1528.0, 1640.0, 1528.0, 1637.0, 1522.0, 1635.0, 1519.0, 1631.0, 1515.0, 1629.0, 1514.0, 1625.0, 1516.0, 1624.0, 1522.0, 1622.0], [1492.0, 1631.0, 1494.0, 1634.0, 1494.0, 1650.0, 1491.0, 1652.0, 1487.0, 1651.0, 1484.0, 1645.0, 1486.0, 1644.0, 1486.0, 1638.0, 1480.0, 1633.0, 1483.0, 1631.0], [1569.0, 1757.0, 1571.0, 1754.0, 1567.0, 1748.0, 1575.0, 1739.0, 1581.0, 1735.0, 1586.0, 1734.0, 1583.0, 1740.0, 1586.0, 1745.0, 1581.0, 1753.0, 1576.0, 1755.0, 1573.0, 1761.0], [1520.0, 1669.0, 1532.0, 1669.0, 1526.0, 1673.0, 1518.0, 1675.0, 1513.0, 1671.0], [1545.0, 1719.0, 1540.0, 1721.0, 1535.0, 1725.0, 1531.0, 1724.0, 1536.0, 1718.0, 1544.0, 1716.0]], "capital": [1532.0, 1639.0], "configuredProductions": {"2": 2900.0}, "id": 43, "locationType": "province", "name": "Smyrna", "neighbourIds": [38, 40, 42, 44, 168, 169, 170, 171, 172, 174], "ownerId": 7, "population": 100000, "productionType": 2}, {"borders": [[1629.0, 1735.0, 1624.0, 1733.0, 1623.0, 1726.0, 1618.0, 1720.0, 1626.0, 1721.0, 1624.0, 1717.0, 1633.0, 1715.0, 1632.0, 1711.0, 1628.0, 1708.0, 1632.0, 1699.0, 1633.0, 1691.0, 1628.0, 1691.0, 1618.0, 1696.0, 1615.0, 1686.0, 1618.0, 1681.0, 1626.0, 1665.0, 1635.0, 1660.0, 1644.0, 1660.0, 1649.0, 1662.0, 1662.0, 1648.0, 1670.0, 1645.0, 1679.0, 1645.0, 1682.0, 1648.0, 1686.0, 1646.0, 1688.0, 1644.0, 1690.0, 1640.0, 1698.0, 1633.0, 1702.0, 1640.0, 1703.0, 1645.0, 1708.0, 1647.0, 1712.0, 1650.0, 1712.0, 1653.0, 1714.0, 1657.0, 1718.0, 1656.0, 1720.0, 1663.0, 1726.0, 1667.0, 1732.0, 1679.0, 1754.0, 1684.0, 1770.0, 1701.0, 1772.0, 1707.0, 1792.0, 1707.0, 1799.0, 1711.0, 1800.0, 1720.0, 1793.0, 1720.0, 1779.0, 1727.0, 1779.0, 1730.0, 1776.0, 1732.0, 1763.0, 1727.0, 1760.0, 1729.0, 1750.0, 1721.0, 1742.0, 1709.0, 1725.0, 1705.0, 1717.0, 1699.0, 1702.0, 1699.0, 1691.0, 1699.0, 1684.0, 1701.0, 1684.0, 1711.0, 1686.0, 1719.0, 1679.0, 1733.0, 1667.0, 1731.0, 1650.0, 1741.0, 1650.0, 1745.0, 1644.0, 1745.0, 1641.0, 1739.0, 1634.0, 1741.0]], "capital": [1686.0, 1693.0], "configuredProductions": {"2": 5800.0}, "id": 44, "locationType": "province", "name": "Adalia", "neighbourIds": [38, 43, 174, 175], "ownerId": 7, "population": 100000, "productionType": 2}, {"borders": [[1225.0, 1372.0, 1233.0, 1377.0, 1238.0, 1374.0, 1219.0, 1354.0, 1224.0, 1340.0, 1224.0, 1327.0, 1234.0, 1329.0, 1234.0, 1335.0, 1242.0, 1335.0, 1251.0, 1338.0, 1257.0, 1337.0, 1263.0, 1331.0, 1271.0, 1331.0, 1272.0, 1337.0, 1282.0, 1342.0, 1298.0, 1333.0, 1304.0, 1335.0, 1306.0, 1341.0, 1318.0, 1343.0, 1314.0, 1352.0, 1313.0, 1359.0, 1310.0, 1367.0, 1301.0, 1367.0, 1296.0, 1377.0, 1289.0, 1383.0, 1283.0, 1391.0, 1279.0, 1391.0, 1275.0, 1389.0, 1268.0, 1393.0, 1272.0, 1400.0, 1272.0, 1406.0, 1267.0, 1408.0, 1262.0, 1409.0, 1256.0, 1409.0, 1249.0, 1399.0, 1242.0, 1395.0, 1230.0, 1393.0, 1232.0, 1388.0, 1227.0, 1381.0, 1221.0, 1376.0]], "capital": [1262.0, 1338.0], "configuredProductions": {"4": 2900.0}, "id": 45, "locationType": "province", "name": "Belgrade", "neighbourIds": [19, 23, 36, 46, 47], "ownerId": 16, "population": 100000, "productionType": 4}, {"borders": [[1227.0, 1381.0, 1232.0, 1388.0, 1230.0, 1393.0, 1231.0, 1398.0, 1226.0, 1401.0, 1223.0, 1399.0, 1223.0, 1397.0, 1216.0, 1401.0, 1217.0, 1412.0, 1224.0, 1415.0, 1227.0, 1421.0, 1233.0, 1420.0, 1242.0, 1425.0, 1250.0, 1433.0, 1256.0, 1440.0, 1252.0, 1444.0, 1248.0, 1439.0, 1243.0, 1441.0, 1240.0, 1445.0, 1235.0, 1448.0, 1230.0, 1448.0, 1225.0, 1450.0, 1222.0, 1454.0, 1222.0, 1460.0, 1229.0, 1462.0, 1227.0, 1467.0, 1232.0, 1481.0, 1224.0, 1477.0, 1219.0, 1470.0, 1210.0, 1460.0, 1205.0, 1461.0, 1198.0, 1458.0, 1203.0, 1451.0, 1198.0, 1451.0, 1194.0, 1455.0, 1188.0, 1453.0, 1194.0, 1445.0, 1192.0, 1433.0, 1194.0, 1425.0, 1200.0, 1416.0, 1203.0, 1408.0, 1203.0, 1401.0, 1214.0, 1393.0, 1217.0, 1395.0, 1224.0, 1387.0, 1221.0, 1376.0]], "capital": [1214.0, 1449.0], "configuredProductions": {"0": 2900.0}, "id": 46, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [19, 36, 37, 45, 190, 191], "ownerId": 16, "population": 100000}, {"borders": [[1272.0, 1406.0, 1272.0, 1400.0, 1268.0, 1393.0, 1275.0, 1389.0, 1279.0, 1391.0, 1283.0, 1391.0, 1289.0, 1383.0, 1296.0, 1377.0, 1301.0, 1367.0, 1310.0, 1367.0, 1313.0, 1359.0, 1314.0, 1352.0, 1318.0, 1343.0, 1323.0, 1349.0, 1328.0, 1347.0, 1329.0, 1340.0, 1334.0, 1331.0, 1339.0, 1331.0, 1339.0, 1336.0, 1347.0, 1340.0, 1345.0, 1344.0, 1339.0, 1342.0, 1334.0, 1345.0, 1333.0, 1351.0, 1337.0, 1353.0, 1342.0, 1359.0, 1348.0, 1367.0, 1340.0, 1372.0, 1335.0, 1381.0, 1335.0, 1389.0, 1339.0, 1399.0, 1347.0, 1404.0, 1356.0, 1407.0, 1353.0, 1415.0, 1345.0, 1423.0, 1336.0, 1434.0, 1334.0, 1440.0, 1334.0, 1449.0, 1324.0, 1453.0, 1312.0, 1450.0, 1312.0, 1446.0, 1306.0, 1436.0, 1297.0, 1439.0, 1293.0, 1435.0, 1291.0, 1428.0, 1287.0, 1419.0, 1282.0, 1419.0, 1279.0, 1415.0, 1272.0, 1415.0, 1267.0, 1408.0]], "capital": [1316.0, 1407.0], "configuredProductions": {"3": 2900.0}, "id": 47, "locationType": "province", "name": "Nisk", "neighbourIds": [23, 29, 36, 37, 45, 49], "ownerId": 16, "population": 100000, "productionType": 3}, {"borders": [[1540.0, 1348.0, 1536.0, 1352.0, 1525.0, 1357.0, 1512.0, 1358.0, 1500.0, 1359.0, 1493.0, 1364.0, 1485.0, 1366.0, 1480.0, 1371.0, 1474.0, 1373.0, 1461.0, 1389.0, 1452.0, 1387.0, 1445.0, 1387.0, 1440.0, 1390.0, 1433.0, 1388.0, 1425.0, 1390.0, 1425.0, 1385.0, 1418.0, 1379.0, 1414.0, 1371.0, 1412.0, 1365.0, 1405.0, 1356.0, 1403.0, 1351.0, 1405.0, 1344.0, 1400.0, 1337.0, 1400.0, 1331.0, 1398.0, 1324.0, 1402.0, 1316.0, 1402.0, 1309.0, 1396.0, 1305.0, 1401.0, 1300.0, 1402.0, 1295.0, 1395.0, 1291.0, 1404.0, 1288.0, 1409.0, 1294.0, 1432.0, 1288.0, 1439.0, 1296.0, 1445.0, 1292.0, 1457.0, 1290.0, 1464.0, 1286.0, 1470.0, 1284.0, 1471.0, 1289.0, 1475.0, 1295.0, 1487.0, 1305.0, 1496.0, 1311.0, 1506.0, 1313.0, 1520.0, 1315.0, 1530.0, 1321.0, 1535.0, 1325.0, 1538.0, 1330.0, 1538.0, 1336.0, 1542.0, 1340.0, 1540.0, 1345.0]], "capital": [1470.0, 1340.0], "configuredProductions": {"5": 2900.0}, "id": 48, "locationType": "province", "name": "Bukharest", "neighbourIds": [22, 30, 32, 49, 50, 52], "ownerId": 17, "population": 100000, "productionType": 5}, {"borders": [[1420.0, 1392.0, 1416.0, 1387.0, 1412.0, 1387.0, 1408.0, 1389.0, 1402.0, 1393.0, 1395.0, 1391.0, 1387.0, 1389.0, 1372.0, 1385.0, 1365.0, 1385.0, 1359.0, 1388.0, 1351.0, 1386.0, 1349.0, 1381.0, 1351.0, 1377.0, 1356.0, 1377.0, 1357.0, 1375.0, 1348.0, 1367.0, 1342.0, 1359.0, 1337.0, 1353.0, 1333.0, 1351.0, 1334.0, 1345.0, 1339.0, 1342.0, 1345.0, 1344.0, 1347.0, 1340.0, 1339.0, 1336.0, 1339.0, 1331.0, 1334.0, 1331.0, 1331.0, 1324.0, 1342.0, 1313.0, 1344.0, 1303.0, 1353.0, 1303.0, 1363.0, 1301.0, 1372.0, 1304.0, 1378.0, 1299.0, 1372.0, 1291.0, 1378.0, 1286.0, 1384.0, 1293.0, 1395.0, 1291.0, 1402.0, 1295.0, 1401.0, 1300.0, 1396.0, 1305.0, 1402.0, 1309.0, 1402.0, 1316.0, 1398.0, 1324.0, 1400.0, 1331.0, 1400.0, 1337.0, 1405.0, 1344.0, 1403.0, 1351.0, 1405.0, 1356.0, 1412.0, 1365.0, 1414.0, 1371.0, 1418.0, 1379.0, 1425.0, 1385.0, 1425.0, 1390.0]], "capital": [1379.0, 1350.0], "configuredProductions": {"6": 2900.0}, "id": 49, "locationType": "province", "name": "Craiova", "neighbourIds": [22, 23, 29, 30, 47, 48], "ownerId": 17, "population": 100000, "productionType": 6}, {"borders": [[1566.0, 1350.0, 1564.0, 1353.0, 1564.0, 1358.0, 1568.0, 1363.0, 1569.0, 1371.0, 1556.0, 1369.0, 1546.0, 1370.0, 1538.0, 1364.0, 1534.0, 1369.0, 1528.0, 1363.0, 1521.0, 1368.0, 1512.0, 1358.0, 1525.0, 1357.0, 1536.0, 1352.0, 1540.0, 1348.0, 1540.0, 1345.0, 1542.0, 1340.0, 1538.0, 1336.0, 1538.0, 1330.0, 1535.0, 1325.0, 1530.0, 1321.0, 1537.0, 1320.0, 1543.0, 1317.0, 1540.0, 1310.0, 1537.0, 1306.0, 1541.0, 1303.0, 1540.0, 1298.0, 1534.0, 1299.0, 1531.0, 1297.0, 1530.0, 1291.0, 1538.0, 1289.0, 1548.0, 1296.0, 1559.0, 1297.0, 1558.0, 1291.0, 1570.0, 1291.0, 1569.0, 1288.0, 1579.0, 1281.0, 1581.0, 1283.0, 1588.0, 1279.0, 1594.0, 1285.0, 1593.0, 1290.0, 1595.0, 1295.0, 1595.0, 1303.0, 1592.0, 1308.0, 1586.0, 1311.0, 1578.0, 1313.0, 1575.0, 1317.0, 1572.0, 1307.0, 1568.0, 1312.0, 1569.0, 1319.0, 1568.0, 1327.0, 1565.0, 1335.0, 1565.0, 1345.0]], "capital": [1560.0, 1341.0], "configuredProductions": {"1": 2900.0}, "id": 50, "locationType": "province", "name": "Constanza", "neighbourIds": [32, 48, 52, 149, 162, 164], "ownerId": 17, "population": 100000, "productionType": 1}, {"borders": [[1517.0, 1223.0, 1508.0, 1224.0, 1500.0, 1228.0, 1496.0, 1235.0, 1491.0, 1236.0, 1484.0, 1236.0, 1468.0, 1241.0, 1467.0, 1232.0, 1466.0, 1227.0, 1450.0, 1213.0, 1438.0, 1206.0, 1434.0, 1208.0, 1428.0, 1198.0, 1442.0, 1187.0, 1449.0, 1195.0, 1458.0, 1183.0, 1466.0, 1186.0, 1468.0, 1179.0, 1458.0, 1167.0, 1456.0, 1151.0, 1464.0, 1159.0, 1468.0, 1159.0, 1472.0, 1155.0, 1480.0, 1156.0, 1484.0, 1162.0, 1488.0, 1165.0, 1488.0, 1169.0, 1492.0, 1175.0, 1493.0, 1182.0, 1503.0, 1194.0, 1509.0, 1203.0, 1512.0, 1210.0, 1522.0, 1212.0, 1526.0, 1215.0, 1528.0, 1221.0]], "capital": [1503.0, 1205.0], "configuredProductions": {"3": 2900.0}, "id": 51, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [15, 21, 22, 52, 149], "ownerId": 17, "population": 100000, "productionType": 3}, {"borders": [[1534.0, 1279.0, 1538.0, 1289.0, 1530.0, 1291.0, 1531.0, 1297.0, 1534.0, 1299.0, 1540.0, 1298.0, 1541.0, 1303.0, 1537.0, 1306.0, 1540.0, 1310.0, 1543.0, 1317.0, 1537.0, 1320.0, 1530.0, 1321.0, 1520.0, 1315.0, 1506.0, 1313.0, 1496.0, 1311.0, 1487.0, 1305.0, 1475.0, 1295.0, 1471.0, 1289.0, 1470.0, 1284.0, 1477.0, 1271.0, 1482.0, 1261.0, 1468.0, 1241.0, 1484.0, 1236.0, 1491.0, 1236.0, 1496.0, 1235.0, 1500.0, 1228.0, 1508.0, 1224.0, 1517.0, 1223.0, 1528.0, 1221.0, 1532.0, 1231.0, 1532.0, 1249.0, 1533.0, 1259.0, 1534.0, 1269.0]], "capital": [1528.0, 1287.0], "configuredProductions": {"0": 2900.0}, "id": 52, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [22, 48, 50, 51, 149, 162], "ownerId": 17, "population": 100000}, {"borders": [[1369.0, 1712.0, 1367.0, 1717.0, 1372.0, 1721.0, 1374.0, 1726.0, 1373.0, 1731.0, 1380.0, 1736.0, 1373.0, 1738.0, 1379.0, 1746.0, 1378.0, 1749.0, 1371.0, 1747.0, 1363.0, 1741.0, 1361.0, 1736.0, 1354.0, 1737.0, 1349.0, 1751.0, 1346.0, 1750.0, 1344.0, 1745.0, 1347.0, 1742.0, 1345.0, 1738.0, 1345.0, 1733.0, 1341.0, 1728.0, 1335.0, 1728.0, 1336.0, 1723.0, 1332.0, 1722.0, 1329.0, 1725.0, 1328.0, 1729.0, 1328.0, 1736.0, 1325.0, 1736.0, 1318.0, 1733.0, 1318.0, 1729.0, 1320.0, 1725.0, 1315.0, 1722.0, 1315.0, 1715.0, 1318.0, 1709.0, 1312.0, 1701.0, 1312.0, 1695.0, 1309.0, 1693.0, 1308.0, 1690.0, 1301.0, 1688.0, 1300.0, 1682.0, 1294.0, 1681.0, 1295.0, 1675.0, 1302.0, 1673.0, 1303.0, 1666.0, 1305.0, 1662.0, 1315.0, 1661.0, 1318.0, 1655.0, 1322.0, 1655.0, 1326.0, 1653.0, 1328.0, 1655.0, 1330.0, 1654.0, 1338.0, 1660.0, 1351.0, 1662.0, 1355.0, 1665.0, 1358.0, 1665.0, 1362.0, 1668.0, 1366.0, 1672.0, 1377.0, 1678.0, 1378.0, 1681.0, 1376.0, 1684.0, 1382.0, 1691.0, 1387.0, 1690.0, 1392.0, 1698.0, 1384.0, 1700.0, 1378.0, 1706.0, 1374.0, 1701.0, 1372.0, 1693.0, 1356.0, 1692.0, 1358.0, 1697.0, 1361.0, 1701.0, 1362.0, 1706.0], [1288.0, 1685.0, 1285.0, 1687.0, 1275.0, 1682.0, 1274.0, 1675.0, 1284.0, 1679.0]], "capital": [1312.0, 1687.0], "configuredProductions": {"4": 2900.0}, "id": 53, "locationType": "province", "name": "Pyrgos", "neighbourIds": [54, 173, 182, 186, 189], "ownerId": 12, "population": 100000, "productionType": 4}, {"borders": [[1350.0, 1624.0, 1358.0, 1630.0, 1368.0, 1633.0, 1373.0, 1636.0, 1376.0, 1635.0, 1380.0, 1637.0, 1381.0, 1641.0, 1390.0, 1643.0, 1392.0, 1650.0, 1395.0, 1648.0, 1398.0, 1651.0, 1401.0, 1651.0, 1407.0, 1654.0, 1410.0, 1654.0, 1412.0, 1657.0, 1412.0, 1678.0, 1410.0, 1683.0, 1402.0, 1676.0, 1396.0, 1669.0, 1392.0, 1664.0, 1387.0, 1661.0, 1387.0, 1662.0, 1384.0, 1663.0, 1383.0, 1665.0, 1378.0, 1665.0, 1371.0, 1670.0, 1366.0, 1672.0, 1362.0, 1668.0, 1368.0, 1666.0, 1365.0, 1663.0, 1374.0, 1661.0, 1374.0, 1658.0, 1359.0, 1655.0, 1359.0, 1649.0, 1355.0, 1649.0, 1350.0, 1651.0, 1344.0, 1645.0, 1343.0, 1647.0, 1344.0, 1651.0, 1334.0, 1647.0, 1324.0, 1646.0, 1318.0, 1648.0, 1319.0, 1651.0, 1312.0, 1650.0, 1305.0, 1653.0, 1304.0, 1648.0, 1298.0, 1644.0, 1296.0, 1649.0, 1299.0, 1655.0, 1292.0, 1652.0, 1288.0, 1639.0, 1286.0, 1635.0, 1281.0, 1633.0, 1280.0, 1627.0, 1283.0, 1625.0, 1293.0, 1624.0, 1304.0, 1627.0, 1310.0, 1625.0, 1322.0, 1627.0, 1322.0, 1629.0, 1332.0, 1634.0, 1344.0, 1635.0, 1342.0, 1627.0, 1338.0, 1623.0], [1270.0, 1647.0, 1274.0, 1651.0, 1275.0, 1656.0, 1281.0, 1659.0, 1280.0, 1663.0, 1272.0, 1662.0, 1268.0, 1658.0, 1266.0, 1660.0, 1264.0, 1653.0, 1268.0, 1653.0], [1254.0, 1605.0, 1244.0, 1601.0, 1242.0, 1591.0, 1234.0, 1583.0, 1245.0, 1580.0, 1244.0, 1587.0, 1248.0, 1589.0, 1247.0, 1595.0, 1252.0, 1599.0]], "capital": [1399.0, 1661.0], "configuredProductions": {"1": 2900.0}, "id": 54, "locationType": "province", "name": "Athens", "neighbourIds": [53, 55, 170, 171, 173], "ownerId": 12, "population": 100000, "productionType": 1}, {"borders": [[1288.0, 1587.0, 1293.0, 1583.0, 1310.0, 1577.0, 1316.0, 1585.0, 1325.0, 1587.0, 1331.0, 1587.0, 1329.0, 1579.0, 1348.0, 1570.0, 1352.0, 1573.0, 1355.0, 1579.0, 1357.0, 1578.0, 1364.0, 1590.0, 1366.0, 1590.0, 1371.0, 1598.0, 1371.0, 1600.0, 1376.0, 1603.0, 1376.0, 1613.0, 1372.0, 1613.0, 1372.0, 1608.0, 1363.0, 1599.0, 1358.0, 1600.0, 1359.0, 1604.0, 1356.0, 1608.0, 1369.0, 1618.0, 1367.0, 1619.0, 1362.0, 1619.0, 1356.0, 1623.0, 1354.0, 1621.0, 1350.0, 1624.0, 1338.0, 1623.0, 1342.0, 1627.0, 1344.0, 1635.0, 1332.0, 1634.0, 1322.0, 1629.0, 1322.0, 1627.0, 1310.0, 1625.0, 1304.0, 1627.0, 1293.0, 1624.0, 1293.0, 1619.0, 1289.0, 1620.0, 1282.0, 1598.0, 1288.0, 1595.0], [1378.0, 1615.0, 1380.0, 1617.0, 1384.0, 1619.0, 1382.0, 1622.0, 1386.0, 1624.0, 1387.0, 1626.0, 1389.0, 1626.0, 1398.0, 1633.0, 1404.0, 1632.0, 1405.0, 1634.0, 1412.0, 1633.0, 1416.0, 1636.0, 1417.0, 1639.0, 1415.0, 1640.0, 1425.0, 1657.0, 1432.0, 1659.0, 1432.0, 1661.0, 1426.0, 1665.0, 1423.0, 1663.0, 1423.0, 1660.0, 1420.0, 1660.0, 1415.0, 1653.0, 1415.0, 1650.0, 1411.0, 1648.0, 1412.0, 1646.0, 1402.0, 1643.0, 1403.0, 1645.0, 1395.0, 1641.0, 1394.0, 1638.0, 1391.0, 1638.0, 1389.0, 1636.0, 1387.0, 1636.0, 1386.0, 1634.0, 1383.0, 1634.0, 1385.0, 1631.0, 1374.0, 1627.0, 1372.0, 1625.0, 1368.0, 1627.0, 1365.0, 1625.0]], "capital": [1341.0, 1587.0], "configuredProductions": {"2": 2900.0}, "id": 55, "locationType": "province", "name": "Larissa", "neighbourIds": [34, 35, 54, 170], "ownerId": 12, "population": 100000, "productionType": 2}, {"borders": [[1420.0, 1791.0, 1428.0, 1793.0, 1435.0, 1789.0, 1442.0, 1793.0, 1452.0, 1788.0, 1456.0, 1787.0, 1462.0, 1793.0, 1473.0, 1791.0, 1478.0, 1791.0, 1482.0, 1791.0, 1486.0, 1789.0, 1489.0, 1797.0, 1485.0, 1801.0, 1492.0, 1801.0, 1499.0, 1796.0, 1509.0, 1797.0, 1506.0, 1807.0, 1484.0, 1807.0, 1476.0, 1811.0, 1466.0, 1807.0, 1462.0, 1813.0, 1457.0, 1811.0, 1452.0, 1813.0, 1448.0, 1811.0, 1444.0, 1814.0, 1440.0, 1812.0, 1444.0, 1808.0, 1442.0, 1804.0, 1422.0, 1801.0, 1402.0, 1801.0, 1397.0, 1799.0, 1396.0, 1790.0, 1402.0, 1780.0, 1406.0, 1785.0, 1420.0, 1785.0]], "capital": [1414.0, 1788.0], "configuredProductions": {"1": 2900.0}, "id": 56, "locationType": "province", "name": "Canea", "neighbourIds": [172, 173, 180, 181, 182], "ownerId": 12, "population": 100000, "productionType": 1}, {"borders": [[1806.0, 1757.0, 1812.0, 1757.0, 1818.0, 1752.0, 1824.0, 1749.0, 1828.0, 1745.0, 1840.0, 1738.0, 1848.0, 1731.0, 1851.0, 1731.0, 1850.0, 1735.0, 1834.0, 1748.0, 1832.0, 1753.0, 1826.0, 1755.0, 1831.0, 1766.0, 1836.0, 1771.0, 1825.0, 1775.0, 1822.0, 1775.0, 1817.0, 1778.0, 1814.0, 1786.0, 1812.0, 1790.0, 1803.0, 1794.0, 1800.0, 1792.0, 1795.0, 1795.0, 1794.0, 1801.0, 1788.0, 1801.0, 1787.0, 1795.0, 1781.0, 1795.0, 1772.0, 1801.0, 1766.0, 1797.0, 1765.0, 1791.0, 1758.0, 1786.0, 1757.0, 1776.0, 1766.0, 1777.0, 1771.0, 1772.0, 1776.0, 1769.0, 1781.0, 1770.0, 1784.0, 1767.0, 1782.0, 1762.0, 1784.0, 1759.0, 1796.0, 1760.0, 1802.0, 1760.0]], "capital": [1793.0, 1767.0], "configuredProductions": {"2": 2900.0}, "id": 57, "locationType": "province", "name": "Nicosia", "neighbourIds": [175, 176, 177, 178, 179], "ownerId": 12, "population": 100000, "productionType": 2}, {"borders": [[994.0, 1653.0, 996.0, 1661.0, 1007.0, 1661.0, 1012.0, 1657.0, 1017.0, 1660.0, 1030.0, 1660.0, 1034.0, 1661.0, 1039.0, 1655.0, 1045.0, 1655.0, 1053.0, 1652.0, 1056.0, 1658.0, 1058.0, 1650.0, 1067.0, 1654.0, 1072.0, 1649.0, 1076.0, 1653.0, 1072.0, 1659.0, 1066.0, 1669.0, 1054.0, 1685.0, 1051.0, 1695.0, 1057.0, 1710.0, 1055.0, 1720.0, 1050.0, 1727.0, 1049.0, 1732.0, 1045.0, 1726.0, 1037.0, 1730.0, 1028.0, 1721.0, 1022.0, 1725.0, 1017.0, 1721.0, 1019.0, 1713.0, 1013.0, 1706.0, 994.0, 1700.0, 988.0, 1693.0, 984.0, 1695.0, 975.0, 1686.0, 974.0, 1682.0, 967.0, 1682.0, 958.0, 1676.0, 953.0, 1675.0, 942.0, 1663.0, 949.0, 1662.0, 943.0, 1657.0, 951.0, 1648.0, 957.0, 1646.0, 966.0, 1654.0, 971.0, 1653.0, 969.0, 1646.0, 985.0, 1647.0]], "capital": [987.0, 1652.0], "configuredProductions": {"5": 5800.0}, "id": 58, "locationType": "province", "name": "Palermo", "neighbourIds": [195, 196, 197, 198, 199, 201], "ownerId": 4, "population": 100000, "productionType": 5}, {"borders": [[839.0, 1588.0, 830.0, 1582.0, 825.0, 1580.0, 817.0, 1583.0, 822.0, 1589.0, 816.0, 1592.0, 808.0, 1598.0, 809.0, 1592.0, 805.0, 1591.0, 801.0, 1597.0, 795.0, 1582.0, 791.0, 1584.0, 788.0, 1579.0, 795.0, 1577.0, 796.0, 1565.0, 799.0, 1560.0, 801.0, 1553.0, 805.0, 1550.0, 804.0, 1545.0, 798.0, 1542.0, 800.0, 1534.0, 799.0, 1515.0, 792.0, 1509.0, 795.0, 1503.0, 798.0, 1489.0, 802.0, 1491.0, 808.0, 1496.0, 823.0, 1491.0, 828.0, 1486.0, 835.0, 1485.0, 836.0, 1488.0, 840.0, 1487.0, 847.0, 1488.0, 855.0, 1495.0, 850.0, 1497.0, 853.0, 1504.0, 853.0, 1523.0, 844.0, 1533.0, 847.0, 1542.0, 848.0, 1551.0, 846.0, 1560.0, 844.0, 1571.0, 843.0, 1581.0]], "capital": [822.0, 1578.0], "configuredProductions": {"0": 5800.0}, "id": 59, "locationType": "province", "name": "Cagliari", "neighbourIds": [202, 203, 204, 205, 209, 328], "ownerId": 4, "population": 100000}, {"borders": [[977.0, 1289.0, 974.0, 1293.0, 968.0, 1293.0, 962.0, 1298.0, 952.0, 1301.0, 943.0, 1301.0, 936.0, 1295.0, 932.0, 1293.0, 926.0, 1286.0, 913.0, 1285.0, 900.0, 1279.0, 909.0, 1278.0, 915.0, 1280.0, 918.0, 1278.0, 918.0, 1273.0, 922.0, 1267.0, 927.0, 1265.0, 936.0, 1269.0, 944.0, 1259.0, 952.0, 1257.0, 960.0, 1252.0, 962.0, 1243.0, 970.0, 1240.0, 979.0, 1230.0, 990.0, 1229.0, 1007.0, 1235.0, 1018.0, 1244.0, 1012.0, 1249.0, 1023.0, 1255.0, 1004.0, 1275.0, 997.0, 1275.0, 1002.0, 1279.0, 999.0, 1281.0, 979.0, 1285.0]], "capital": [971.0, 1288.0], "configuredProductions": {"0": 2900.0}, "id": 60, "locationType": "province", "name": "Venice", "neighbourIds": [12, 18, 61, 63, 193], "ownerId": 4, "population": 100000}, {"borders": [[914.0, 1229.0, 919.0, 1237.0, 913.0, 1247.0, 912.0, 1263.0, 920.0, 1260.0, 917.0, 1269.0, 909.0, 1278.0, 900.0, 1279.0, 895.0, 1281.0, 889.0, 1292.0, 884.0, 1295.0, 876.0, 1292.0, 870.0, 1295.0, 862.0, 1289.0, 858.0, 1289.0, 850.0, 1292.0, 846.0, 1289.0, 833.0, 1285.0, 825.0, 1280.0, 819.0, 1277.0, 816.0, 1265.0, 804.0, 1260.0, 794.0, 1249.0, 791.0, 1236.0, 798.0, 1235.0, 809.0, 1237.0, 822.0, 1233.0, 830.0, 1231.0, 836.0, 1223.0, 840.0, 1219.0, 845.0, 1234.0, 850.0, 1239.0, 845.0, 1243.0, 840.0, 1249.0, 843.0, 1261.0, 845.0, 1263.0, 848.0, 1256.0, 848.0, 1251.0, 851.0, 1242.0, 856.0, 1248.0, 862.0, 1247.0, 864.0, 1254.0, 870.0, 1251.0, 874.0, 1255.0, 875.0, 1251.0, 873.0, 1249.0, 873.0, 1243.0, 877.0, 1239.0, 875.0, 1234.0, 872.0, 1236.0, 870.0, 1240.0, 865.0, 1237.0, 869.0, 1228.0, 870.0, 1223.0, 875.0, 1221.0, 879.0, 1226.0, 880.0, 1233.0, 883.0, 1235.0, 892.0, 1235.0, 896.0, 1239.0, 902.0, 1238.0, 900.0, 1231.0, 905.0, 1223.0]], "capital": [858.0, 1271.0], "configuredProductions": {"0": 2900.0}, "id": 61, "locationType": "province", "name": "Milan", "neighbourIds": [12, 60, 62, 63, 68, 71, 72], "ownerId": 4, "population": 100000}, {"borders": [[833.0, 1285.0, 846.0, 1289.0, 850.0, 1292.0, 858.0, 1289.0, 862.0, 1289.0, 870.0, 1295.0, 876.0, 1292.0, 884.0, 1295.0, 889.0, 1292.0, 900.0, 1301.0, 898.0, 1305.0, 902.0, 1313.0, 888.0, 1314.0, 880.0, 1321.0, 872.0, 1324.0, 863.0, 1326.0, 860.0, 1329.0, 856.0, 1328.0, 851.0, 1324.0, 844.0, 1323.0, 835.0, 1324.0, 823.0, 1335.0, 818.0, 1337.0, 812.0, 1345.0, 806.0, 1346.0, 786.0, 1345.0, 786.0, 1338.0, 792.0, 1335.0, 791.0, 1331.0, 778.0, 1326.0, 770.0, 1321.0, 767.0, 1301.0, 767.0, 1289.0, 779.0, 1297.0, 786.0, 1304.0, 797.0, 1297.0, 806.0, 1296.0, 819.0, 1297.0, 827.0, 1293.0]], "capital": [845.0, 1317.0], "configuredProductions": {"6": 2900.0}, "id": 62, "locationType": "province", "name": "Genoa", "neighbourIds": [61, 63, 68, 69, 79, 206, 207], "ownerId": 4, "population": 100000, "productionType": 6}, {"borders": [[898.0, 1305.0, 900.0, 1301.0, 889.0, 1292.0, 895.0, 1281.0, 900.0, 1279.0, 913.0, 1285.0, 926.0, 1286.0, 932.0, 1293.0, 936.0, 1295.0, 943.0, 1301.0, 952.0, 1301.0, 962.0, 1298.0, 964.0, 1303.0, 970.0, 1304.0, 976.0, 1309.0, 970.0, 1317.0, 962.0, 1318.0, 966.0, 1327.0, 966.0, 1338.0, 974.0, 1359.0, 981.0, 1362.0, 992.0, 1372.0, 999.0, 1380.0, 1005.0, 1385.0, 1012.0, 1387.0, 1012.0, 1397.0, 1016.0, 1409.0, 1022.0, 1420.0, 1013.0, 1427.0, 1004.0, 1430.0, 999.0, 1431.0, 995.0, 1436.0, 990.0, 1429.0, 986.0, 1417.0, 986.0, 1410.0, 983.0, 1401.0, 974.0, 1397.0, 967.0, 1400.0, 956.0, 1382.0, 957.0, 1373.0, 947.0, 1355.0, 942.0, 1344.0, 928.0, 1335.0, 908.0, 1320.0, 902.0, 1313.0]], "capital": [937.0, 1321.0], "configuredProductions": {"6": 2900.0}, "id": 63, "locationType": "province", "name": "Bologna", "neighbourIds": [60, 61, 62, 64, 65, 69, 193, 206], "ownerId": 4, "population": 100000, "productionType": 6}, {"borders": [[975.0, 1490.0, 968.0, 1488.0, 963.0, 1480.0, 963.0, 1477.0, 955.0, 1473.0, 955.0, 1469.0, 949.0, 1461.0, 944.0, 1456.0, 941.0, 1457.0, 930.0, 1435.0, 926.0, 1435.0, 922.0, 1430.0, 914.0, 1430.0, 915.0, 1425.0, 910.0, 1418.0, 903.0, 1416.0, 905.0, 1413.0, 902.0, 1409.0, 899.0, 1407.0, 894.0, 1406.0, 897.0, 1400.0, 897.0, 1387.0, 908.0, 1388.0, 916.0, 1387.0, 927.0, 1388.0, 936.0, 1395.0, 943.0, 1399.0, 948.0, 1404.0, 967.0, 1400.0, 974.0, 1397.0, 983.0, 1401.0, 986.0, 1410.0, 986.0, 1417.0, 990.0, 1429.0, 995.0, 1436.0, 995.0, 1447.0, 996.0, 1454.0, 1000.0, 1465.0, 998.0, 1474.0, 996.0, 1479.0, 989.0, 1483.0, 986.0, 1487.0, 986.0, 1500.0], [890.0, 1418.0, 884.0, 1413.0, 880.0, 1413.0, 880.0, 1409.0, 890.0, 1408.0, 890.0, 1413.0, 892.0, 1416.0]], "capital": [965.0, 1457.0], "configuredProductions": {"4": 5800.0}, "id": 64, "locationType": "province", "name": "Rome", "neighbourIds": [63, 65, 69, 192, 193, 203, 205], "ownerId": 4, "population": 100000, "productionType": 4}, {"borders": [[986.0, 1500.0, 986.0, 1487.0, 989.0, 1483.0, 996.0, 1479.0, 998.0, 1474.0, 1000.0, 1465.0, 996.0, 1454.0, 995.0, 1447.0, 995.0, 1436.0, 999.0, 1431.0, 1004.0, 1430.0, 1013.0, 1427.0, 1022.0, 1420.0, 1025.0, 1432.0, 1030.0, 1435.0, 1032.0, 1441.0, 1051.0, 1457.0, 1054.0, 1463.0, 1061.0, 1464.0, 1066.0, 1465.0, 1065.0, 1484.0, 1072.0, 1493.0, 1074.0, 1501.0, 1074.0, 1516.0, 1063.0, 1523.0, 1058.0, 1525.0, 1051.0, 1529.0, 1050.0, 1533.0, 1039.0, 1530.0, 1026.0, 1532.0, 1032.0, 1525.0, 1029.0, 1521.0, 1020.0, 1521.0, 1018.0, 1515.0, 1014.0, 1509.0, 1013.0, 1503.0, 1008.0, 1501.0, 998.0, 1500.0]], "capital": [1024.0, 1515.0], "configuredProductions": {"0": 2900.0}, "id": 65, "locationType": "province", "name": "Naples", "neighbourIds": [63, 64, 66, 67, 191, 192, 198, 203], "ownerId": 4, "population": 100000}, {"borders": [[1126.0, 1555.0, 1121.0, 1562.0, 1117.0, 1564.0, 1117.0, 1569.0, 1118.0, 1573.0, 1115.0, 1577.0, 1114.0, 1585.0, 1124.0, 1593.0, 1126.0, 1590.0, 1130.0, 1591.0, 1140.0, 1603.0, 1140.0, 1615.0, 1132.0, 1621.0, 1132.0, 1615.0, 1129.0, 1614.0, 1121.0, 1624.0, 1116.0, 1632.0, 1120.0, 1639.0, 1118.0, 1645.0, 1112.0, 1649.0, 1102.0, 1659.0, 1096.0, 1667.0, 1094.0, 1671.0, 1084.0, 1671.0, 1078.0, 1668.0, 1076.0, 1664.0, 1078.0, 1659.0, 1088.0, 1651.0, 1089.0, 1645.0, 1088.0, 1639.0, 1094.0, 1631.0, 1100.0, 1631.0, 1104.0, 1627.0, 1098.0, 1616.0, 1097.0, 1601.0, 1090.0, 1592.0, 1089.0, 1570.0, 1084.0, 1563.0, 1074.0, 1569.0, 1068.0, 1567.0, 1063.0, 1558.0, 1051.0, 1557.0, 1049.0, 1554.0, 1053.0, 1545.0, 1052.0, 1537.0, 1050.0, 1533.0, 1051.0, 1529.0, 1058.0, 1525.0, 1063.0, 1523.0, 1080.0, 1539.0, 1096.0, 1540.0, 1104.0, 1543.0, 1116.0, 1541.0, 1127.0, 1543.0]], "capital": [1087.0, 1662.0], "configuredProductions": {"1": 5800.0}, "id": 66, "locationType": "province", "name": "Reggio", "neighbourIds": [65, 67, 188, 189, 196, 197], "ownerId": 4, "population": 100000, "productionType": 1}, {"borders": [[1116.0, 1541.0, 1104.0, 1543.0, 1096.0, 1540.0, 1080.0, 1539.0, 1063.0, 1523.0, 1074.0, 1516.0, 1074.0, 1501.0, 1072.0, 1493.0, 1065.0, 1484.0, 1066.0, 1465.0, 1076.0, 1469.0, 1096.0, 1470.0, 1106.0, 1472.0, 1106.0, 1477.0, 1098.0, 1480.0, 1097.0, 1486.0, 1104.0, 1493.0, 1114.0, 1497.0, 1121.0, 1502.0, 1121.0, 1504.0, 1128.0, 1505.0, 1130.0, 1507.0, 1132.0, 1511.0, 1140.0, 1514.0, 1148.0, 1521.0, 1154.0, 1529.0, 1162.0, 1534.0, 1167.0, 1539.0, 1176.0, 1543.0, 1182.0, 1553.0, 1189.0, 1556.0, 1192.0, 1561.0, 1189.0, 1568.0, 1186.0, 1581.0, 1182.0, 1581.0, 1172.0, 1573.0, 1171.0, 1569.0, 1165.0, 1564.0, 1166.0, 1557.0, 1152.0, 1556.0, 1148.0, 1552.0, 1150.0, 1547.0, 1142.0, 1542.0, 1134.0, 1540.0, 1127.0, 1543.0]], "capital": [1166.0, 1543.0], "configuredProductions": {"2": 2900.0}, "id": 67, "locationType": "province", "name": "Brindisi", "neighbourIds": [65, 66, 189, 190, 191, 197, 198], "ownerId": 4, "population": 100000, "productionType": 2}, {"borders": [[791.0, 1236.0, 794.0, 1249.0, 804.0, 1260.0, 816.0, 1265.0, 819.0, 1277.0, 825.0, 1280.0, 833.0, 1285.0, 827.0, 1293.0, 819.0, 1297.0, 806.0, 1296.0, 797.0, 1297.0, 786.0, 1304.0, 779.0, 1297.0, 767.0, 1289.0, 763.0, 1284.0, 762.0, 1279.0, 762.0, 1273.0, 763.0, 1265.0, 767.0, 1263.0, 773.0, 1267.0, 777.0, 1266.0, 777.0, 1261.0, 769.0, 1253.0, 772.0, 1247.0, 785.0, 1236.0]], "capital": [807.0, 1280.0], "configuredProductions": {"2": 2900.0}, "id": 68, "locationType": "province", "name": "Turin", "neighbourIds": [61, 62, 72, 79, 80, 207], "ownerId": 4, "population": 100000, "productionType": 2}, {"borders": [[948.0, 1404.0, 943.0, 1399.0, 936.0, 1395.0, 927.0, 1388.0, 916.0, 1387.0, 908.0, 1388.0, 897.0, 1387.0, 890.0, 1377.0, 890.0, 1367.0, 890.0, 1356.0, 888.0, 1349.0, 876.0, 1342.0, 860.0, 1329.0, 863.0, 1326.0, 872.0, 1324.0, 880.0, 1321.0, 888.0, 1314.0, 902.0, 1313.0, 908.0, 1320.0, 928.0, 1335.0, 942.0, 1344.0, 947.0, 1355.0, 957.0, 1373.0, 956.0, 1382.0, 967.0, 1400.0]], "capital": [928.0, 1361.0], "configuredProductions": {"3": 2900.0}, "id": 69, "locationType": "province", "name": "Florence", "neighbourIds": [62, 63, 64, 193, 205, 206], "ownerId": 4, "population": 100000, "productionType": 3}, {"borders": [[849.0, 1406.0, 850.0, 1400.0, 852.0, 1397.0, 857.0, 1403.0, 855.0, 1416.0, 856.0, 1421.0, 855.0, 1430.0, 856.0, 1438.0, 855.0, 1443.0, 847.0, 1450.0, 847.0, 1460.0, 843.0, 1465.0, 844.0, 1472.0, 841.0, 1475.0, 837.0, 1477.0, 836.0, 1476.0, 832.0, 1476.0, 828.0, 1470.0, 823.0, 1471.0, 821.0, 1468.0, 826.0, 1461.0, 823.0, 1460.0, 823.0, 1455.0, 825.0, 1448.0, 819.0, 1448.0, 822.0, 1442.0, 819.0, 1435.0, 824.0, 1430.0, 822.0, 1423.0, 825.0, 1416.0, 829.0, 1419.0, 830.0, 1416.0, 835.0, 1414.0, 842.0, 1410.0, 848.0, 1415.0]], "capital": [828.0, 1445.0], "configuredProductions": {"2": 2900.0}, "id": 70, "locationType": "province", "name": "<PERSON><PERSON><PERSON><PERSON>", "neighbourIds": [205, 206, 207, 210, 328], "ownerId": 2, "population": 100000, "productionType": 2}, {"borders": [[888.0, 1179.0, 884.0, 1188.0, 886.0, 1197.0, 896.0, 1211.0, 903.0, 1209.0, 908.0, 1204.0, 916.0, 1211.0, 914.0, 1229.0, 905.0, 1223.0, 900.0, 1231.0, 902.0, 1238.0, 896.0, 1239.0, 892.0, 1235.0, 883.0, 1235.0, 880.0, 1233.0, 879.0, 1226.0, 875.0, 1221.0, 870.0, 1223.0, 869.0, 1228.0, 865.0, 1237.0, 862.0, 1247.0, 856.0, 1248.0, 851.0, 1242.0, 857.0, 1241.0, 856.0, 1236.0, 850.0, 1239.0, 845.0, 1234.0, 840.0, 1219.0, 832.0, 1217.0, 828.0, 1211.0, 832.0, 1203.0, 836.0, 1198.0, 840.0, 1189.0, 838.0, 1181.0, 834.0, 1174.0, 836.0, 1164.0, 840.0, 1161.0, 849.0, 1163.0, 848.0, 1156.0, 855.0, 1153.0, 859.0, 1159.0, 865.0, 1158.0, 868.0, 1162.0, 874.0, 1160.0, 875.0, 1157.0, 880.0, 1163.0, 884.0, 1163.0, 894.0, 1175.0]], "capital": [854.0, 1175.0], "configuredProductions": {"3": 2900.0}, "id": 71, "locationType": "province", "name": "Zurich", "neighbourIds": [9, 10, 12, 61, 72], "ownerId": 14, "population": 100000, "productionType": 3}, {"borders": [[784.0, 1232.0, 783.0, 1225.0, 776.0, 1219.0, 782.0, 1214.0, 786.0, 1215.0, 791.0, 1219.0, 790.0, 1213.0, 786.0, 1208.0, 780.0, 1206.0, 778.0, 1207.0, 772.0, 1207.0, 770.0, 1211.0, 766.0, 1211.0, 764.0, 1217.0, 761.0, 1219.0, 755.0, 1219.0, 754.0, 1209.0, 772.0, 1195.0, 776.0, 1189.0, 782.0, 1183.0, 788.0, 1179.0, 792.0, 1177.0, 802.0, 1169.0, 794.0, 1167.0, 798.0, 1161.0, 804.0, 1159.0, 806.0, 1163.0, 811.0, 1163.0, 814.0, 1160.0, 819.0, 1158.0, 826.0, 1158.0, 828.0, 1162.0, 836.0, 1164.0, 834.0, 1174.0, 838.0, 1181.0, 840.0, 1189.0, 836.0, 1198.0, 832.0, 1203.0, 828.0, 1211.0, 832.0, 1217.0, 840.0, 1219.0, 836.0, 1223.0, 830.0, 1231.0, 822.0, 1233.0, 809.0, 1237.0, 798.0, 1235.0, 791.0, 1236.0, 785.0, 1236.0]], "capital": [809.0, 1187.0], "configuredProductions": {"3": 2900.0}, "id": 72, "locationType": "province", "name": "Bern<PERSON>", "neighbourIds": [1, 9, 61, 68, 71, 80, 83], "ownerId": 14, "population": 100000, "productionType": 3}, {"borders": [[805.0, 1000.0, 798.0, 1007.0, 794.0, 1021.0, 792.0, 1033.0, 800.0, 1039.0, 798.0, 1045.0, 790.0, 1055.0, 781.0, 1057.0, 778.0, 1051.0, 770.0, 1053.0, 764.0, 1039.0, 757.0, 1033.0, 759.0, 1023.0, 752.0, 1015.0, 748.0, 1017.0, 741.0, 1025.0, 731.0, 1020.0, 736.0, 1007.0, 732.0, 1003.0, 722.0, 1007.0, 718.0, 997.0, 725.0, 983.0, 730.0, 975.0, 741.0, 973.0, 759.0, 973.0, 767.0, 981.0, 783.0, 983.0, 792.0, 985.0, 792.0, 989.0, 801.0, 989.0, 806.0, 991.0]], "capital": [745.0, 981.0], "configuredProductions": {"4": 2900.0}, "id": 73, "locationType": "province", "name": "Brussels", "neighbourIds": [1, 7, 74, 75, 83, 84, 85], "ownerId": 14, "population": 100000, "productionType": 4}, {"borders": [[783.0, 983.0, 767.0, 981.0, 759.0, 973.0, 741.0, 973.0, 730.0, 975.0, 725.0, 983.0, 718.0, 997.0, 706.0, 995.0, 704.0, 975.0, 690.0, 973.0, 686.0, 967.0, 694.0, 963.0, 693.0, 954.0, 710.0, 953.0, 719.0, 949.0, 728.0, 949.0, 734.0, 951.0, 739.0, 955.0, 742.0, 955.0, 748.0, 961.0, 757.0, 952.0, 760.0, 954.0, 769.0, 954.0, 766.0, 962.0, 775.0, 959.0, 776.0, 955.0, 781.0, 959.0, 781.0, 967.0, 789.0, 963.0, 792.0, 967.0, 798.0, 971.0, 797.0, 976.0, 792.0, 985.0], [740.0, 949.0, 735.0, 948.0, 738.0, 945.0, 744.0, 946.0, 748.0, 953.0, 748.0, 955.0]], "capital": [758.0, 959.0], "configuredProductions": {"0": 2900.0}, "id": 74, "locationType": "province", "name": "Antwerp", "neighbourIds": [73, 75, 84, 246], "ownerId": 14, "population": 100000}, {"borders": [[772.0, 913.0, 774.0, 923.0, 777.0, 930.0, 791.0, 931.0, 800.0, 934.0, 816.0, 946.0, 812.0, 948.0, 816.0, 955.0, 814.0, 966.0, 806.0, 977.0, 806.0, 991.0, 801.0, 989.0, 792.0, 989.0, 792.0, 985.0, 797.0, 976.0, 798.0, 971.0, 792.0, 967.0, 789.0, 963.0, 781.0, 967.0, 781.0, 959.0, 776.0, 955.0, 775.0, 959.0, 766.0, 962.0, 769.0, 954.0, 760.0, 954.0, 757.0, 952.0, 755.0, 950.0, 754.0, 945.0, 760.0, 943.0, 764.0, 946.0, 774.0, 943.0, 772.0, 940.0, 758.0, 937.0, 751.0, 928.0, 752.0, 923.0, 761.0, 915.0, 768.0, 905.0], [748.0, 937.0, 742.0, 933.0, 746.0, 931.0, 752.0, 936.0, 754.0, 941.0]], "capital": [764.0, 919.0], "configuredProductions": {"1": 2900.0}, "id": 75, "locationType": "province", "name": "TheHague", "neighbourIds": [7, 73, 74, 76, 246, 247], "ownerId": 14, "population": 100000, "productionType": 1}, {"borders": [[772.0, 899.0, 774.0, 893.0, 778.0, 883.0, 782.0, 883.0, 780.0, 886.0, 786.0, 888.0, 782.0, 891.0, 786.0, 895.0, 791.0, 893.0, 794.0, 894.0, 798.0, 897.0, 798.0, 900.0, 795.0, 901.0, 788.0, 898.0, 786.0, 902.0, 786.0, 905.0, 788.0, 909.0, 784.0, 913.0, 784.0, 918.0, 798.0, 920.0, 802.0, 917.0, 808.0, 917.0, 812.0, 913.0, 810.0, 908.0, 812.0, 905.0, 816.0, 906.0, 819.0, 902.0, 814.0, 895.0, 800.0, 890.0, 797.0, 886.0, 800.0, 881.0, 800.0, 873.0, 806.0, 869.0, 814.0, 870.0, 816.0, 872.0, 818.0, 870.0, 825.0, 869.0, 829.0, 872.0, 830.0, 876.0, 832.0, 876.0, 835.0, 872.0, 846.0, 873.0, 850.0, 875.0, 851.0, 879.0, 856.0, 885.0, 858.0, 888.0, 852.0, 899.0, 848.0, 904.0, 840.0, 905.0, 838.0, 908.0, 838.0, 913.0, 845.0, 914.0, 845.0, 919.0, 840.0, 923.0, 830.0, 936.0, 836.0, 941.0, 828.0, 945.0, 819.0, 941.0, 816.0, 946.0, 800.0, 934.0, 791.0, 931.0, 777.0, 930.0, 774.0, 923.0, 772.0, 913.0, 768.0, 905.0]], "capital": [779.0, 909.0], "configuredProductions": {"6": 2900.0}, "id": 76, "locationType": "province", "name": "Amsterdam", "neighbourIds": [6, 7, 75, 247, 251], "ownerId": 14, "population": 100000, "productionType": 6}, {"borders": [[604.0, 1023.0, 612.0, 1020.0, 615.0, 1025.0, 618.0, 1024.0, 622.0, 1024.0, 623.0, 1027.0, 618.0, 1033.0, 619.0, 1035.0, 626.0, 1035.0, 628.0, 1039.0, 633.0, 1044.0, 641.0, 1047.0, 646.0, 1044.0, 649.0, 1041.0, 660.0, 1039.0, 660.0, 1047.0, 674.0, 1052.0, 684.0, 1057.0, 684.0, 1066.0, 684.0, 1072.0, 686.0, 1081.0, 690.0, 1091.0, 698.0, 1089.0, 705.0, 1091.0, 708.0, 1095.0, 707.0, 1102.0, 710.0, 1104.0, 712.0, 1110.0, 715.0, 1113.0, 718.0, 1116.0, 718.0, 1123.0, 724.0, 1128.0, 723.0, 1132.0, 720.0, 1135.0, 724.0, 1146.0, 719.0, 1145.0, 706.0, 1157.0, 695.0, 1158.0, 692.0, 1164.0, 684.0, 1166.0, 678.0, 1161.0, 669.0, 1161.0, 665.0, 1160.0, 664.0, 1148.0, 660.0, 1141.0, 663.0, 1135.0, 662.0, 1131.0, 658.0, 1127.0, 658.0, 1123.0, 650.0, 1118.0, 646.0, 1111.0, 639.0, 1109.0, 628.0, 1108.0, 632.0, 1100.0, 628.0, 1095.0, 614.0, 1091.0, 602.0, 1084.0, 596.0, 1073.0, 573.0, 1073.0, 564.0, 1070.0, 563.0, 1063.0, 553.0, 1055.0, 564.0, 1043.0, 568.0, 1028.0, 563.0, 1022.0, 562.0, 1009.0, 570.0, 1016.0, 578.0, 1017.0, 582.0, 1021.0, 586.0, 1023.0, 592.0, 1022.0, 598.0, 1021.0]], "capital": [658.0, 1067.0], "configuredProductions": {"0": 5800.0}, "id": 77, "locationType": "province", "name": "Paris", "neighbourIds": [80, 82, 83, 84, 85, 86, 87, 244, 245], "ownerId": 2, "population": 100000}, {"borders": [[652.0, 1270.0, 659.0, 1275.0, 657.0, 1279.0, 651.0, 1284.0, 646.0, 1285.0, 636.0, 1285.0, 632.0, 1295.0, 636.0, 1304.0, 626.0, 1309.0, 624.0, 1317.0, 626.0, 1323.0, 636.0, 1325.0, 649.0, 1334.0, 646.0, 1341.0, 642.0, 1346.0, 636.0, 1343.0, 631.0, 1343.0, 627.0, 1349.0, 620.0, 1352.0, 618.0, 1359.0, 613.0, 1362.0, 616.0, 1365.0, 618.0, 1369.0, 618.0, 1375.0, 608.0, 1375.0, 601.0, 1380.0, 596.0, 1375.0, 588.0, 1369.0, 581.0, 1373.0, 576.0, 1369.0, 570.0, 1361.0, 565.0, 1354.0, 548.0, 1345.0, 541.0, 1337.0, 530.0, 1339.0, 529.0, 1347.0, 515.0, 1341.0, 510.0, 1345.0, 504.0, 1339.0, 506.0, 1335.0, 519.0, 1315.0, 537.0, 1311.0, 544.0, 1305.0, 541.0, 1301.0, 548.0, 1290.0, 550.0, 1284.0, 548.0, 1279.0, 544.0, 1279.0, 543.0, 1273.0, 536.0, 1268.0, 539.0, 1263.0, 536.0, 1257.0, 540.0, 1253.0, 553.0, 1252.0, 565.0, 1249.0, 579.0, 1243.0, 582.0, 1241.0, 587.0, 1240.0, 596.0, 1247.0, 590.0, 1259.0, 611.0, 1261.0, 624.0, 1257.0, 634.0, 1248.0, 640.0, 1253.0, 650.0, 1255.0]], "capital": [566.0, 1313.0], "configuredProductions": {"2": 2900.0}, "id": 78, "locationType": "province", "name": "Toulouse", "neighbourIds": [79, 80, 81, 86, 90, 92, 212], "ownerId": 2, "population": 100000, "productionType": 2}, {"borders": [[626.0, 1323.0, 624.0, 1317.0, 626.0, 1309.0, 636.0, 1304.0, 632.0, 1295.0, 636.0, 1285.0, 646.0, 1285.0, 651.0, 1284.0, 657.0, 1279.0, 659.0, 1275.0, 664.0, 1271.0, 671.0, 1270.0, 686.0, 1277.0, 700.0, 1281.0, 698.0, 1287.0, 693.0, 1294.0, 708.0, 1295.0, 721.0, 1290.0, 730.0, 1286.0, 751.0, 1271.0, 762.0, 1273.0, 762.0, 1279.0, 763.0, 1284.0, 767.0, 1289.0, 767.0, 1301.0, 770.0, 1321.0, 778.0, 1326.0, 791.0, 1331.0, 792.0, 1335.0, 786.0, 1338.0, 786.0, 1345.0, 780.0, 1346.0, 772.0, 1351.0, 772.0, 1357.0, 768.0, 1357.0, 756.0, 1362.0, 750.0, 1367.0, 752.0, 1371.0, 744.0, 1373.0, 735.0, 1371.0, 728.0, 1367.0, 722.0, 1371.0, 720.0, 1366.0, 717.0, 1362.0, 708.0, 1361.0, 710.0, 1355.0, 704.0, 1353.0, 704.0, 1346.0, 700.0, 1341.0, 698.0, 1341.0, 700.0, 1347.0, 696.0, 1347.0, 694.0, 1344.0, 692.0, 1343.0, 691.0, 1349.0, 686.0, 1351.0, 680.0, 1346.0, 672.0, 1345.0, 667.0, 1339.0, 658.0, 1338.0, 646.0, 1341.0, 649.0, 1334.0, 636.0, 1325.0]], "capital": [713.0, 1352.0], "configuredProductions": {"1": 2900.0}, "id": 79, "locationType": "province", "name": "Marseilles", "neighbourIds": [62, 68, 78, 80, 207, 212], "ownerId": 2, "population": 100000, "productionType": 1}, {"borders": [[719.0, 1145.0, 724.0, 1146.0, 736.0, 1155.0, 735.0, 1171.0, 744.0, 1170.0, 751.0, 1168.0, 748.0, 1162.0, 753.0, 1156.0, 765.0, 1169.0, 788.0, 1179.0, 782.0, 1183.0, 776.0, 1189.0, 772.0, 1195.0, 754.0, 1209.0, 755.0, 1219.0, 761.0, 1219.0, 764.0, 1217.0, 770.0, 1211.0, 774.0, 1215.0, 778.0, 1211.0, 782.0, 1214.0, 776.0, 1219.0, 783.0, 1225.0, 784.0, 1232.0, 785.0, 1236.0, 772.0, 1247.0, 769.0, 1253.0, 777.0, 1261.0, 777.0, 1266.0, 773.0, 1267.0, 767.0, 1263.0, 763.0, 1265.0, 762.0, 1273.0, 751.0, 1271.0, 730.0, 1286.0, 721.0, 1290.0, 708.0, 1295.0, 693.0, 1294.0, 698.0, 1287.0, 700.0, 1281.0, 686.0, 1277.0, 671.0, 1270.0, 664.0, 1271.0, 659.0, 1275.0, 652.0, 1270.0, 650.0, 1255.0, 658.0, 1241.0, 668.0, 1237.0, 676.0, 1227.0, 686.0, 1217.0, 692.0, 1217.0, 690.0, 1208.0, 692.0, 1205.0, 689.0, 1199.0, 688.0, 1190.0, 683.0, 1183.0, 684.0, 1176.0, 679.0, 1171.0, 670.0, 1169.0, 669.0, 1161.0, 678.0, 1161.0, 684.0, 1166.0, 692.0, 1164.0, 695.0, 1158.0, 706.0, 1157.0]], "capital": [711.0, 1229.0], "configuredProductions": {"4": 5800.0}, "id": 80, "locationType": "province", "name": "Lyons", "neighbourIds": [68, 72, 77, 78, 79, 83, 86], "ownerId": 2, "population": 100000, "productionType": 4}, {"borders": [[536.0, 1257.0, 539.0, 1263.0, 536.0, 1268.0, 543.0, 1273.0, 544.0, 1279.0, 548.0, 1279.0, 550.0, 1284.0, 548.0, 1290.0, 541.0, 1301.0, 544.0, 1305.0, 537.0, 1311.0, 519.0, 1315.0, 506.0, 1335.0, 500.0, 1331.0, 490.0, 1331.0, 487.0, 1323.0, 479.0, 1319.0, 474.0, 1313.0, 469.0, 1312.0, 464.0, 1314.0, 468.0, 1302.0, 456.0, 1295.0, 469.0, 1275.0, 476.0, 1262.0, 479.0, 1253.0, 483.0, 1242.0, 487.0, 1237.0, 498.0, 1232.0, 494.0, 1229.0, 489.0, 1230.0, 487.0, 1227.0, 492.0, 1216.0, 496.0, 1210.0, 499.0, 1197.0, 502.0, 1197.0, 504.0, 1203.0, 506.0, 1203.0, 508.0, 1205.0, 508.0, 1209.0, 510.0, 1214.0, 512.0, 1224.0, 516.0, 1227.0, 515.0, 1213.0, 517.0, 1207.0, 512.0, 1199.0, 505.0, 1191.0, 500.0, 1191.0, 502.0, 1184.0, 509.0, 1176.0, 508.0, 1172.0, 510.0, 1171.0, 515.0, 1179.0, 513.0, 1163.0, 515.0, 1156.0, 524.0, 1157.0, 534.0, 1157.0, 544.0, 1165.0, 543.0, 1181.0, 538.0, 1197.0, 536.0, 1209.0, 546.0, 1222.0, 544.0, 1236.0, 542.0, 1243.0, 540.0, 1253.0]], "capital": [511.0, 1235.0], "configuredProductions": {"0": 2900.0}, "id": 81, "locationType": "province", "name": "Bordeaux", "neighbourIds": [78, 82, 86, 92, 99, 235, 236], "ownerId": 2, "population": 100000}, {"borders": [[534.0, 1157.0, 524.0, 1157.0, 515.0, 1156.0, 510.0, 1155.0, 506.0, 1151.0, 501.0, 1151.0, 497.0, 1145.0, 491.0, 1139.0, 491.0, 1134.0, 487.0, 1127.0, 486.0, 1122.0, 489.0, 1117.0, 490.0, 1113.0, 496.0, 1111.0, 492.0, 1109.0, 487.0, 1108.0, 492.0, 1100.0, 498.0, 1099.0, 506.0, 1101.0, 501.0, 1097.0, 494.0, 1095.0, 484.0, 1095.0, 484.0, 1087.0, 490.0, 1085.0, 493.0, 1073.0, 506.0, 1073.0, 518.0, 1074.0, 528.0, 1068.0, 542.0, 1062.0, 553.0, 1055.0, 563.0, 1063.0, 564.0, 1070.0, 573.0, 1073.0, 596.0, 1073.0, 602.0, 1084.0, 614.0, 1091.0, 628.0, 1095.0, 632.0, 1100.0, 628.0, 1108.0, 619.0, 1111.0, 612.0, 1118.0, 606.0, 1119.0, 596.0, 1123.0, 592.0, 1128.0, 574.0, 1127.0, 568.0, 1134.0, 554.0, 1139.0, 548.0, 1143.0, 544.0, 1152.0, 544.0, 1165.0]], "capital": [514.0, 1099.0], "configuredProductions": {"4": 2900.0}, "id": 82, "locationType": "province", "name": "Nantes", "neighbourIds": [77, 81, 86, 87, 236, 243, 244], "ownerId": 2, "population": 100000, "productionType": 4}, {"borders": [[762.0, 1061.0, 768.0, 1057.0, 770.0, 1053.0, 778.0, 1051.0, 781.0, 1057.0, 782.0, 1065.0, 777.0, 1074.0, 783.0, 1081.0, 789.0, 1085.0, 792.0, 1096.0, 802.0, 1103.0, 807.0, 1109.0, 808.0, 1125.0, 799.0, 1139.0, 796.0, 1146.0, 802.0, 1151.0, 804.0, 1159.0, 798.0, 1161.0, 794.0, 1167.0, 802.0, 1169.0, 792.0, 1177.0, 788.0, 1179.0, 765.0, 1169.0, 753.0, 1156.0, 748.0, 1162.0, 751.0, 1168.0, 744.0, 1170.0, 735.0, 1171.0, 736.0, 1155.0, 724.0, 1146.0, 720.0, 1135.0, 723.0, 1132.0, 724.0, 1128.0, 718.0, 1123.0, 718.0, 1116.0, 715.0, 1113.0, 742.0, 1097.0, 748.0, 1089.0, 749.0, 1081.0, 748.0, 1073.0, 757.0, 1063.0]], "capital": [787.0, 1103.0], "configuredProductions": {"2": 2900.0}, "id": 83, "locationType": "province", "name": "Nancy", "neighbourIds": [1, 72, 73, 77, 80, 85], "ownerId": 2, "population": 100000, "productionType": 2}, {"borders": [[660.0, 1039.0, 649.0, 1041.0, 646.0, 1044.0, 641.0, 1047.0, 633.0, 1044.0, 628.0, 1039.0, 626.0, 1035.0, 619.0, 1035.0, 618.0, 1033.0, 623.0, 1027.0, 622.0, 1024.0, 618.0, 1024.0, 615.0, 1025.0, 612.0, 1020.0, 604.0, 1023.0, 598.0, 1021.0, 598.0, 1017.0, 601.0, 1007.0, 617.0, 1003.0, 629.0, 1004.0, 639.0, 1003.0, 646.0, 1000.0, 648.0, 993.0, 654.0, 995.0, 652.0, 989.0, 654.0, 985.0, 654.0, 981.0, 658.0, 979.0, 654.0, 973.0, 659.0, 965.0, 660.0, 959.0, 671.0, 955.0, 679.0, 954.0, 693.0, 954.0, 694.0, 963.0, 686.0, 967.0, 690.0, 973.0, 704.0, 975.0, 706.0, 995.0, 718.0, 997.0, 722.0, 1007.0, 715.0, 1011.0, 707.0, 1017.0, 688.0, 1029.0, 684.0, 1035.0]], "capital": [697.0, 979.0], "configuredProductions": {"3": 2900.0}, "id": 84, "locationType": "province", "name": "Lille", "neighbourIds": [73, 74, 77, 85, 245, 246, 323], "ownerId": 2, "population": 100000, "productionType": 3}, {"borders": [[684.0, 1066.0, 684.0, 1057.0, 674.0, 1052.0, 660.0, 1047.0, 660.0, 1039.0, 684.0, 1035.0, 688.0, 1029.0, 707.0, 1017.0, 715.0, 1011.0, 722.0, 1007.0, 732.0, 1003.0, 736.0, 1007.0, 731.0, 1020.0, 741.0, 1025.0, 748.0, 1017.0, 752.0, 1015.0, 759.0, 1023.0, 757.0, 1033.0, 764.0, 1039.0, 770.0, 1053.0, 768.0, 1057.0, 762.0, 1061.0, 757.0, 1063.0, 748.0, 1073.0, 749.0, 1081.0, 748.0, 1089.0, 742.0, 1097.0, 715.0, 1113.0, 712.0, 1110.0, 710.0, 1104.0, 707.0, 1102.0, 708.0, 1095.0, 705.0, 1091.0, 698.0, 1089.0, 690.0, 1091.0, 686.0, 1081.0, 684.0, 1072.0]], "capital": [726.0, 1055.0], "configuredProductions": {"0": 2900.0}, "id": 85, "locationType": "province", "name": "Reims", "neighbourIds": [73, 77, 83, 84], "ownerId": 2, "population": 100000}, {"borders": [[646.0, 1111.0, 650.0, 1118.0, 658.0, 1123.0, 658.0, 1127.0, 662.0, 1131.0, 663.0, 1135.0, 660.0, 1141.0, 664.0, 1148.0, 665.0, 1160.0, 669.0, 1161.0, 670.0, 1169.0, 679.0, 1171.0, 684.0, 1176.0, 683.0, 1183.0, 688.0, 1190.0, 689.0, 1199.0, 692.0, 1205.0, 690.0, 1208.0, 692.0, 1217.0, 686.0, 1217.0, 676.0, 1227.0, 668.0, 1237.0, 658.0, 1241.0, 650.0, 1255.0, 640.0, 1253.0, 634.0, 1248.0, 624.0, 1257.0, 611.0, 1261.0, 590.0, 1259.0, 596.0, 1247.0, 587.0, 1240.0, 582.0, 1241.0, 579.0, 1243.0, 565.0, 1249.0, 553.0, 1252.0, 540.0, 1253.0, 542.0, 1243.0, 544.0, 1236.0, 546.0, 1222.0, 536.0, 1209.0, 538.0, 1197.0, 543.0, 1181.0, 544.0, 1165.0, 544.0, 1152.0, 548.0, 1143.0, 554.0, 1139.0, 568.0, 1134.0, 574.0, 1127.0, 592.0, 1128.0, 596.0, 1123.0, 606.0, 1119.0, 612.0, 1118.0, 619.0, 1111.0, 628.0, 1108.0, 639.0, 1109.0]], "capital": [591.0, 1201.0], "configuredProductions": {"0": 5800.0}, "id": 86, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [77, 78, 80, 81, 82], "ownerId": 2, "population": 100000}, {"borders": [[542.0, 1062.0, 528.0, 1068.0, 518.0, 1074.0, 506.0, 1073.0, 493.0, 1073.0, 490.0, 1085.0, 486.0, 1082.0, 476.0, 1082.0, 479.0, 1076.0, 472.0, 1077.0, 466.0, 1076.0, 460.0, 1080.0, 460.0, 1073.0, 456.0, 1068.0, 453.0, 1071.0, 450.0, 1066.0, 440.0, 1056.0, 431.0, 1053.0, 424.0, 1055.0, 421.0, 1051.0, 424.0, 1049.0, 425.0, 1044.0, 418.0, 1041.0, 418.0, 1037.0, 425.0, 1040.0, 430.0, 1039.0, 431.0, 1035.0, 424.0, 1030.0, 427.0, 1028.0, 432.0, 1030.0, 434.0, 1029.0, 434.0, 1025.0, 426.0, 1022.0, 419.0, 1025.0, 418.0, 1021.0, 422.0, 1019.0, 423.0, 1013.0, 428.0, 1012.0, 440.0, 1012.0, 464.0, 1017.0, 472.0, 1013.0, 478.0, 1017.0, 486.0, 1015.0, 490.0, 1019.0, 490.0, 1028.0, 492.0, 1030.0, 493.0, 1033.0, 498.0, 1032.0, 505.0, 1028.0, 507.0, 1031.0, 510.0, 1031.0, 513.0, 1033.0, 515.0, 1038.0, 518.0, 1037.0, 518.0, 1032.0, 520.0, 1033.0, 521.0, 1035.0, 526.0, 1035.0, 529.0, 1037.0, 534.0, 1036.0, 532.0, 1029.0, 532.0, 1024.0, 534.0, 1021.0, 535.0, 1016.0, 538.0, 1014.0, 534.0, 1011.0, 531.0, 1005.0, 533.0, 999.0, 536.0, 995.0, 534.0, 989.0, 542.0, 993.0, 548.0, 993.0, 554.0, 996.0, 555.0, 999.0, 552.0, 1005.0, 552.0, 1013.0, 562.0, 1009.0, 563.0, 1022.0, 568.0, 1028.0, 564.0, 1043.0, 553.0, 1055.0]], "capital": [435.0, 1020.0], "configuredProductions": {"1": 2900.0}, "id": 87, "locationType": "province", "name": "Brest", "neighbourIds": [77, 82, 236, 237, 242, 243], "ownerId": 2, "population": 100000, "productionType": 1}, {"borders": [[583.0, 1534.0, 582.0, 1527.0, 575.0, 1527.0, 574.0, 1522.0, 571.0, 1518.0, 564.0, 1519.0, 563.0, 1515.0, 573.0, 1507.0, 589.0, 1503.0, 592.0, 1503.0, 592.0, 1509.0, 597.0, 1514.0, 604.0, 1512.0, 605.0, 1515.0, 604.0, 1518.0, 596.0, 1529.0], [643.0, 1520.0, 639.0, 1519.0, 637.0, 1515.0, 630.0, 1511.0, 621.0, 1510.0, 622.0, 1506.0, 626.0, 1503.0, 637.0, 1504.0, 645.0, 1509.0, 645.0, 1517.0], [523.0, 1539.0, 517.0, 1539.0, 513.0, 1537.0, 512.0, 1532.0, 516.0, 1531.0, 520.0, 1528.0, 526.0, 1529.0, 528.0, 1531.0], [519.0, 1547.0, 529.0, 1554.0, 521.0, 1555.0, 519.0, 1552.0]], "capital": [577.0, 1515.0], "configuredProductions": {"1": 2900.0}, "id": 88, "locationType": "province", "name": "Palma", "neighbourIds": [213, 214, 216, 218, 219], "ownerId": 9, "population": 100000, "productionType": 1}, {"borders": [[364.0, 1480.0, 355.0, 1474.0, 350.0, 1475.0, 345.0, 1470.0, 341.0, 1470.0, 330.0, 1473.0, 323.0, 1470.0, 317.0, 1473.0, 313.0, 1470.0, 306.0, 1475.0, 291.0, 1470.0, 291.0, 1465.0, 285.0, 1459.0, 285.0, 1452.0, 290.0, 1445.0, 290.0, 1429.0, 297.0, 1426.0, 302.0, 1420.0, 310.0, 1414.0, 314.0, 1402.0, 309.0, 1387.0, 310.0, 1381.0, 314.0, 1376.0, 329.0, 1370.0, 345.0, 1368.0, 363.0, 1371.0, 373.0, 1364.0, 386.0, 1373.0, 403.0, 1377.0, 406.0, 1374.0, 409.0, 1374.0, 417.0, 1381.0, 421.0, 1375.0, 430.0, 1390.0, 430.0, 1396.0, 428.0, 1400.0, 418.0, 1404.0, 412.0, 1405.0, 411.0, 1411.0, 419.0, 1420.0, 421.0, 1427.0, 419.0, 1436.0, 413.0, 1442.0, 418.0, 1449.0, 424.0, 1454.0, 425.0, 1464.0, 410.0, 1465.0, 401.0, 1474.0, 393.0, 1479.0, 387.0, 1478.0, 370.0, 1478.0]], "capital": [341.0, 1411.0], "configuredProductions": {"0": 2900.0}, "id": 89, "locationType": "province", "name": "Madrid", "neighbourIds": [92, 94, 96, 97, 98, 99], "ownerId": 9, "population": 100000}, {"borders": [[617.0, 1399.0, 618.0, 1410.0, 601.0, 1419.0, 588.0, 1420.0, 574.0, 1426.0, 560.0, 1430.0, 551.0, 1429.0, 536.0, 1433.0, 528.0, 1434.0, 518.0, 1438.0, 516.0, 1443.0, 520.0, 1444.0, 522.0, 1448.0, 518.0, 1449.0, 512.0, 1447.0, 511.0, 1440.0, 508.0, 1436.0, 511.0, 1432.0, 517.0, 1429.0, 517.0, 1424.0, 512.0, 1420.0, 506.0, 1419.0, 505.0, 1415.0, 509.0, 1413.0, 507.0, 1409.0, 502.0, 1402.0, 502.0, 1391.0, 505.0, 1380.0, 511.0, 1378.0, 515.0, 1379.0, 522.0, 1374.0, 519.0, 1371.0, 510.0, 1365.0, 515.0, 1362.0, 527.0, 1360.0, 529.0, 1347.0, 530.0, 1339.0, 541.0, 1337.0, 548.0, 1345.0, 565.0, 1354.0, 570.0, 1361.0, 576.0, 1369.0, 581.0, 1373.0, 588.0, 1369.0, 596.0, 1375.0, 601.0, 1380.0, 608.0, 1375.0, 618.0, 1375.0, 616.0, 1380.0, 622.0, 1387.0, 621.0, 1390.0, 615.0, 1387.0, 614.0, 1391.0]], "capital": [574.0, 1419.0], "configuredProductions": {"3": 2900.0}, "id": 90, "locationType": "province", "name": "Barcelona", "neighbourIds": [78, 92, 94, 212, 213, 219], "ownerId": 9, "population": 100000, "productionType": 3}, {"borders": [[387.0, 1562.0, 386.0, 1571.0, 387.0, 1577.0, 390.0, 1581.0, 385.0, 1585.0, 377.0, 1587.0, 375.0, 1590.0, 370.0, 1590.0, 367.0, 1594.0, 364.0, 1601.0, 355.0, 1605.0, 353.0, 1608.0, 351.0, 1609.0, 345.0, 1602.0, 336.0, 1602.0, 334.0, 1606.0, 327.0, 1604.0, 325.0, 1601.0, 311.0, 1596.0, 305.0, 1599.0, 293.0, 1596.0, 291.0, 1591.0, 284.0, 1587.0, 268.0, 1584.0, 264.0, 1586.0, 254.0, 1590.0, 248.0, 1590.0, 241.0, 1587.0, 237.0, 1589.0, 230.0, 1589.0, 225.0, 1594.0, 226.0, 1600.0, 223.0, 1601.0, 216.0, 1594.0, 214.0, 1599.0, 210.0, 1601.0, 204.0, 1597.0, 200.0, 1592.0, 195.0, 1589.0, 189.0, 1580.0, 188.0, 1573.0, 191.0, 1573.0, 191.0, 1569.0, 206.0, 1568.0, 211.0, 1562.0, 219.0, 1556.0, 224.0, 1542.0, 229.0, 1539.0, 239.0, 1537.0, 258.0, 1537.0, 283.0, 1539.0, 296.0, 1530.0, 299.0, 1522.0, 304.0, 1521.0, 308.0, 1523.0, 324.0, 1520.0, 341.0, 1525.0, 346.0, 1530.0, 352.0, 1534.0, 368.0, 1538.0, 369.0, 1545.0, 375.0, 1549.0, 381.0, 1555.0, 385.0, 1556.0]], "capital": [262.0, 1580.0], "configuredProductions": {"2": 2900.0}, "id": 91, "locationType": "province", "name": "Malaga", "neighbourIds": [95, 96, 98, 221, 222], "ownerId": 9, "population": 100000, "productionType": 2}, {"borders": [[442.0, 1353.0, 435.0, 1348.0, 427.0, 1337.0, 421.0, 1335.0, 425.0, 1329.0, 431.0, 1328.0, 442.0, 1324.0, 445.0, 1320.0, 464.0, 1314.0, 469.0, 1312.0, 474.0, 1313.0, 479.0, 1319.0, 487.0, 1323.0, 490.0, 1331.0, 500.0, 1331.0, 506.0, 1335.0, 504.0, 1339.0, 510.0, 1345.0, 515.0, 1341.0, 529.0, 1347.0, 527.0, 1360.0, 515.0, 1362.0, 510.0, 1365.0, 519.0, 1371.0, 522.0, 1374.0, 515.0, 1379.0, 511.0, 1378.0, 505.0, 1380.0, 502.0, 1391.0, 502.0, 1402.0, 507.0, 1409.0, 501.0, 1411.0, 497.0, 1411.0, 485.0, 1422.0, 480.0, 1424.0, 473.0, 1422.0, 456.0, 1429.0, 435.0, 1431.0, 430.0, 1426.0, 421.0, 1427.0, 419.0, 1420.0, 411.0, 1411.0, 412.0, 1405.0, 418.0, 1404.0, 428.0, 1400.0, 430.0, 1396.0, 430.0, 1390.0, 421.0, 1375.0, 433.0, 1370.0, 433.0, 1362.0]], "capital": [461.0, 1385.0], "configuredProductions": {"3": 2900.0}, "id": 92, "locationType": "province", "name": "Saragossa", "neighbourIds": [78, 81, 89, 90, 94, 99, 218, 219, 235], "ownerId": 9, "population": 100000, "productionType": 3}, {"borders": [[180.0, 1268.0, 187.0, 1265.0, 186.0, 1264.0, 180.0, 1262.0, 182.0, 1252.0, 188.0, 1248.0, 189.0, 1244.0, 182.0, 1245.0, 180.0, 1240.0, 190.0, 1236.0, 179.0, 1234.0, 183.0, 1231.0, 175.0, 1225.0, 180.0, 1223.0, 180.0, 1217.0, 186.0, 1214.0, 194.0, 1213.0, 211.0, 1214.0, 219.0, 1217.0, 224.0, 1210.0, 221.0, 1206.0, 235.0, 1202.0, 238.0, 1199.0, 243.0, 1200.0, 239.0, 1206.0, 243.0, 1205.0, 251.0, 1205.0, 258.0, 1210.0, 257.0, 1219.0, 286.0, 1227.0, 295.0, 1231.0, 307.0, 1235.0, 315.0, 1235.0, 319.0, 1241.0, 335.0, 1248.0, 338.0, 1252.0, 333.0, 1255.0, 324.0, 1260.0, 314.0, 1271.0, 304.0, 1272.0, 299.0, 1278.0, 303.0, 1288.0, 310.0, 1300.0, 310.0, 1306.0, 306.0, 1315.0, 295.0, 1320.0, 290.0, 1329.0, 286.0, 1341.0, 282.0, 1342.0, 277.0, 1339.0, 266.0, 1333.0, 263.0, 1326.0, 260.0, 1323.0, 255.0, 1324.0, 252.0, 1319.0, 258.0, 1309.0, 246.0, 1303.0, 243.0, 1300.0, 231.0, 1302.0, 224.0, 1298.0, 219.0, 1293.0, 208.0, 1295.0, 202.0, 1288.0, 210.0, 1279.0, 203.0, 1275.0, 201.0, 1278.0, 195.0, 1275.0, 185.0, 1273.0, 177.0, 1273.0]], "capital": [202.0, 1237.0], "configuredProductions": {"4": 5800.0}, "id": 93, "locationType": "province", "name": "Santiago", "neighbourIds": [97, 99, 100, 230, 232, 233], "ownerId": 9, "population": 100000, "productionType": 4}, {"borders": [[455.0, 1508.0, 463.0, 1526.0, 472.0, 1541.0, 465.0, 1544.0, 460.0, 1543.0, 456.0, 1547.0, 447.0, 1533.0, 435.0, 1524.0, 436.0, 1516.0, 433.0, 1507.0, 416.0, 1500.0, 404.0, 1500.0, 391.0, 1494.0, 390.0, 1488.0, 393.0, 1479.0, 401.0, 1474.0, 410.0, 1465.0, 425.0, 1464.0, 424.0, 1454.0, 418.0, 1449.0, 413.0, 1442.0, 419.0, 1436.0, 421.0, 1427.0, 430.0, 1426.0, 435.0, 1431.0, 456.0, 1429.0, 473.0, 1422.0, 480.0, 1424.0, 485.0, 1422.0, 497.0, 1411.0, 501.0, 1411.0, 507.0, 1409.0, 509.0, 1413.0, 505.0, 1415.0, 506.0, 1419.0, 512.0, 1420.0, 517.0, 1424.0, 517.0, 1429.0, 511.0, 1432.0, 508.0, 1436.0, 511.0, 1440.0, 512.0, 1447.0, 518.0, 1449.0, 513.0, 1453.0, 508.0, 1451.0, 501.0, 1461.0, 476.0, 1476.0, 469.0, 1482.0, 466.0, 1489.0, 458.0, 1500.0]], "capital": [453.0, 1495.0], "configuredProductions": {"0": 2900.0}, "id": 94, "locationType": "province", "name": "Valencia", "neighbourIds": [89, 90, 92, 98, 218], "ownerId": 9, "population": 100000}, {"borders": [[187.0, 1567.0, 185.0, 1560.0, 189.0, 1556.0, 183.0, 1551.0, 182.0, 1539.0, 180.0, 1536.0, 175.0, 1534.0, 175.0, 1525.0, 171.0, 1524.0, 169.0, 1527.0, 160.0, 1521.0, 152.0, 1520.0, 155.0, 1513.0, 156.0, 1503.0, 165.0, 1496.0, 169.0, 1496.0, 173.0, 1487.0, 186.0, 1487.0, 191.0, 1477.0, 211.0, 1482.0, 223.0, 1480.0, 242.0, 1480.0, 262.0, 1497.0, 273.0, 1502.0, 279.0, 1509.0, 288.0, 1516.0, 299.0, 1522.0, 296.0, 1530.0, 283.0, 1539.0, 258.0, 1537.0, 239.0, 1537.0, 229.0, 1539.0, 224.0, 1542.0, 219.0, 1556.0, 211.0, 1562.0, 206.0, 1568.0, 191.0, 1569.0]], "capital": [213.0, 1531.0], "configuredProductions": {"2": 2900.0}, "id": 95, "locationType": "province", "name": "Seville", "neighbourIds": [91, 96, 102, 222, 223], "ownerId": 9, "population": 100000, "productionType": 2}, {"borders": [[184.0, 1477.0, 177.0, 1463.0, 178.0, 1461.0, 179.0, 1456.0, 183.0, 1452.0, 193.0, 1445.0, 195.0, 1432.0, 193.0, 1428.0, 190.0, 1412.0, 185.0, 1399.0, 190.0, 1400.0, 196.0, 1403.0, 210.0, 1402.0, 233.0, 1413.0, 253.0, 1414.0, 258.0, 1423.0, 275.0, 1422.0, 278.0, 1427.0, 290.0, 1429.0, 290.0, 1445.0, 285.0, 1452.0, 285.0, 1459.0, 291.0, 1465.0, 291.0, 1470.0, 306.0, 1475.0, 313.0, 1470.0, 317.0, 1473.0, 323.0, 1470.0, 330.0, 1473.0, 332.0, 1485.0, 335.0, 1495.0, 327.0, 1502.0, 325.0, 1513.0, 324.0, 1520.0, 308.0, 1523.0, 304.0, 1521.0, 299.0, 1522.0, 288.0, 1516.0, 279.0, 1509.0, 273.0, 1502.0, 262.0, 1497.0, 242.0, 1480.0, 223.0, 1480.0, 211.0, 1482.0, 191.0, 1477.0]], "capital": [197.0, 1451.0], "configuredProductions": {"4": 2900.0}, "id": 96, "locationType": "province", "name": "Badajoz", "neighbourIds": [89, 91, 95, 97, 98, 101, 102, 103], "ownerId": 9, "population": 100000, "productionType": 4}, {"borders": [[309.0, 1387.0, 314.0, 1402.0, 310.0, 1414.0, 302.0, 1420.0, 297.0, 1426.0, 290.0, 1429.0, 278.0, 1427.0, 275.0, 1422.0, 258.0, 1423.0, 253.0, 1414.0, 233.0, 1413.0, 210.0, 1402.0, 216.0, 1389.0, 216.0, 1382.0, 224.0, 1379.0, 228.0, 1368.0, 232.0, 1359.0, 230.0, 1353.0, 229.0, 1345.0, 235.0, 1348.0, 238.0, 1348.0, 241.0, 1342.0, 243.0, 1341.0, 247.0, 1342.0, 254.0, 1337.0, 256.0, 1338.0, 263.0, 1336.0, 266.0, 1333.0, 277.0, 1339.0, 282.0, 1342.0, 286.0, 1341.0, 293.0, 1348.0, 299.0, 1347.0, 307.0, 1349.0, 316.0, 1347.0, 329.0, 1350.0, 337.0, 1348.0, 345.0, 1350.0, 349.0, 1347.0, 355.0, 1351.0, 359.0, 1351.0, 366.0, 1361.0, 373.0, 1364.0, 363.0, 1371.0, 345.0, 1368.0, 329.0, 1370.0, 314.0, 1376.0, 310.0, 1381.0]], "capital": [276.0, 1367.0], "configuredProductions": {"0": 5800.0}, "id": 97, "locationType": "province", "name": "Salamanca", "neighbourIds": [89, 93, 96, 99, 100, 103], "ownerId": 9, "population": 100000}, {"borders": [[444.0, 1550.0, 439.0, 1555.0, 432.0, 1557.0, 429.0, 1561.0, 423.0, 1564.0, 420.0, 1569.0, 414.0, 1573.0, 420.0, 1575.0, 419.0, 1580.0, 412.0, 1579.0, 410.0, 1577.0, 402.0, 1579.0, 398.0, 1578.0, 390.0, 1581.0, 387.0, 1577.0, 386.0, 1571.0, 387.0, 1562.0, 385.0, 1556.0, 381.0, 1555.0, 375.0, 1549.0, 369.0, 1545.0, 368.0, 1538.0, 352.0, 1534.0, 346.0, 1530.0, 341.0, 1525.0, 324.0, 1520.0, 325.0, 1513.0, 327.0, 1502.0, 335.0, 1495.0, 332.0, 1485.0, 330.0, 1473.0, 341.0, 1470.0, 345.0, 1470.0, 350.0, 1475.0, 355.0, 1474.0, 364.0, 1480.0, 370.0, 1478.0, 387.0, 1478.0, 393.0, 1479.0, 390.0, 1488.0, 391.0, 1494.0, 404.0, 1500.0, 416.0, 1500.0, 433.0, 1507.0, 436.0, 1516.0, 435.0, 1524.0, 447.0, 1533.0, 456.0, 1547.0]], "capital": [407.0, 1556.0], "configuredProductions": {"1": 2900.0}, "id": 98, "locationType": "province", "name": "Murcia", "neighbourIds": [89, 91, 94, 96, 217, 218, 220, 221], "ownerId": 9, "population": 100000, "productionType": 1}, {"borders": [[417.0, 1381.0, 409.0, 1374.0, 406.0, 1374.0, 403.0, 1377.0, 386.0, 1373.0, 373.0, 1364.0, 366.0, 1361.0, 359.0, 1351.0, 355.0, 1351.0, 349.0, 1347.0, 345.0, 1350.0, 337.0, 1348.0, 329.0, 1350.0, 316.0, 1347.0, 307.0, 1349.0, 299.0, 1347.0, 293.0, 1348.0, 286.0, 1341.0, 290.0, 1329.0, 295.0, 1320.0, 306.0, 1315.0, 310.0, 1306.0, 310.0, 1300.0, 303.0, 1288.0, 299.0, 1278.0, 304.0, 1272.0, 314.0, 1271.0, 324.0, 1260.0, 333.0, 1255.0, 338.0, 1252.0, 341.0, 1256.0, 362.0, 1265.0, 380.0, 1267.0, 380.0, 1271.0, 391.0, 1272.0, 401.0, 1279.0, 409.0, 1281.0, 412.0, 1279.0, 419.0, 1281.0, 418.0, 1285.0, 427.0, 1291.0, 438.0, 1294.0, 441.0, 1298.0, 456.0, 1295.0, 468.0, 1302.0, 464.0, 1314.0, 445.0, 1320.0, 442.0, 1324.0, 431.0, 1328.0, 425.0, 1329.0, 421.0, 1335.0, 427.0, 1337.0, 435.0, 1348.0, 442.0, 1353.0, 433.0, 1362.0, 433.0, 1370.0, 421.0, 1375.0]], "capital": [370.0, 1318.0], "configuredProductions": {"2": 5800.0}, "id": 99, "locationType": "province", "name": "Burgos", "neighbourIds": [81, 89, 92, 93, 97, 233, 235], "ownerId": 9, "population": 100000, "productionType": 2}, {"borders": [[185.0, 1273.0, 195.0, 1275.0, 201.0, 1278.0, 203.0, 1275.0, 210.0, 1279.0, 202.0, 1288.0, 208.0, 1295.0, 219.0, 1293.0, 224.0, 1298.0, 231.0, 1302.0, 243.0, 1300.0, 246.0, 1303.0, 258.0, 1309.0, 252.0, 1319.0, 255.0, 1324.0, 260.0, 1323.0, 263.0, 1326.0, 266.0, 1333.0, 263.0, 1336.0, 256.0, 1338.0, 254.0, 1337.0, 247.0, 1342.0, 243.0, 1341.0, 241.0, 1342.0, 238.0, 1348.0, 235.0, 1348.0, 229.0, 1345.0, 229.0, 1337.0, 224.0, 1333.0, 219.0, 1332.0, 214.0, 1333.0, 200.0, 1338.0, 191.0, 1334.0, 183.0, 1335.0, 177.0, 1338.0, 173.0, 1335.0, 170.0, 1330.0, 166.0, 1326.0, 169.0, 1316.0, 170.0, 1306.0, 171.0, 1296.0, 176.0, 1291.0, 175.0, 1285.0, 177.0, 1281.0, 177.0, 1273.0]], "capital": [176.0, 1313.0], "configuredProductions": {"2": 2835.0}, "id": 100, "locationType": "province", "name": "Oporto", "neighbourIds": [93, 97, 103, 228, 230], "ownerId": 18, "population": 100000, "productionType": 2}, {"borders": [[125.0, 1438.0, 119.0, 1434.0, 115.0, 1436.0, 108.0, 1431.0, 110.0, 1426.0, 110.0, 1421.0, 113.0, 1421.0, 114.0, 1424.0, 123.0, 1418.0, 122.0, 1414.0, 117.0, 1417.0, 113.0, 1415.0, 106.0, 1415.0, 104.0, 1414.0, 105.0, 1409.0, 108.0, 1409.0, 110.0, 1403.0, 113.0, 1401.0, 115.0, 1397.0, 118.0, 1388.0, 129.0, 1386.0, 133.0, 1378.0, 139.0, 1374.0, 148.0, 1377.0, 162.0, 1372.0, 169.0, 1370.0, 178.0, 1372.0, 183.0, 1379.0, 183.0, 1381.0, 179.0, 1389.0, 185.0, 1399.0, 190.0, 1412.0, 193.0, 1428.0, 195.0, 1432.0, 193.0, 1445.0, 183.0, 1452.0, 179.0, 1447.0, 178.0, 1441.0, 171.0, 1438.0, 160.0, 1440.0, 145.0, 1446.0, 133.0, 1442.0, 124.0, 1442.0]], "capital": [116.0, 1409.0], "configuredProductions": {"0": 2835.0}, "id": 101, "locationType": "province", "name": "Lisbon", "neighbourIds": [96, 102, 103, 226, 227, 228], "ownerId": 18, "population": 100000}, {"borders": [[92.0, 1510.0, 105.0, 1489.0, 106.0, 1483.0, 113.0, 1472.0, 110.0, 1467.0, 111.0, 1463.0, 115.0, 1460.0, 119.0, 1442.0, 124.0, 1442.0, 133.0, 1442.0, 145.0, 1446.0, 160.0, 1440.0, 171.0, 1438.0, 178.0, 1441.0, 179.0, 1447.0, 183.0, 1452.0, 179.0, 1456.0, 178.0, 1461.0, 177.0, 1463.0, 184.0, 1477.0, 191.0, 1477.0, 186.0, 1487.0, 173.0, 1487.0, 169.0, 1496.0, 165.0, 1496.0, 156.0, 1503.0, 155.0, 1513.0, 152.0, 1520.0, 148.0, 1521.0, 139.0, 1520.0, 127.0, 1517.0, 119.0, 1516.0, 116.0, 1510.0, 110.0, 1508.0, 107.0, 1510.0, 101.0, 1509.0]], "capital": [108.0, 1503.0], "configuredProductions": {"1": 2835.0}, "id": 102, "locationType": "province", "name": "Lagos", "neighbourIds": [95, 96, 101, 223, 225, 226], "ownerId": 18, "population": 100000, "productionType": 1}, {"borders": [[170.0, 1330.0, 173.0, 1335.0, 177.0, 1338.0, 183.0, 1335.0, 191.0, 1334.0, 200.0, 1338.0, 214.0, 1333.0, 219.0, 1332.0, 224.0, 1333.0, 229.0, 1337.0, 229.0, 1345.0, 230.0, 1353.0, 232.0, 1359.0, 228.0, 1368.0, 224.0, 1379.0, 216.0, 1382.0, 216.0, 1389.0, 210.0, 1402.0, 196.0, 1403.0, 190.0, 1400.0, 185.0, 1399.0, 179.0, 1389.0, 183.0, 1381.0, 183.0, 1379.0, 178.0, 1372.0, 169.0, 1370.0, 162.0, 1372.0, 148.0, 1377.0, 139.0, 1374.0, 147.0, 1360.0, 155.0, 1347.0, 158.0, 1348.0, 165.0, 1340.0, 164.0, 1336.0, 160.0, 1336.0, 166.0, 1326.0]], "capital": [166.0, 1357.0], "configuredProductions": {"1": 2835.0}, "id": 103, "locationType": "province", "name": "Coimbra", "neighbourIds": [96, 97, 100, 101, 228], "ownerId": 18, "population": 100000, "productionType": 1}, {"borders": [[8.0, 1745.0, 12.0, 1758.0, 24.0, 1759.0, 36.0, 1764.0, 55.0, 1761.0, 64.0, 1757.0, 76.0, 1758.0, 84.0, 1763.0, 104.0, 1768.0, 108.0, 1762.0, 120.0, 1750.0, 145.0, 1758.0, 161.0, 1758.0, 178.0, 1750.0, 194.0, 1749.0, 208.0, 1755.0, 217.0, 1768.0, 221.0, 1777.0, 217.0, 1783.0, 214.0, 1791.0, 216.0, 1801.0, 220.0, 1809.0, 217.0, 1820.0, 0.0, 1820.0, 1.0, 1750.0]], "capital": [73.0, 1783.0], "configuredProductions": {"2": 2900.0}, "id": 104, "locationType": "province", "name": "Morocco", "neighbourIds": [105, 107, 324, 329], "ownerId": 10, "population": 100000, "productionType": 2}, {"borders": [[123.0, 1710.0, 118.0, 1714.0, 111.0, 1727.0, 120.0, 1750.0, 108.0, 1762.0, 104.0, 1768.0, 84.0, 1763.0, 76.0, 1758.0, 64.0, 1757.0, 55.0, 1761.0, 36.0, 1764.0, 24.0, 1759.0, 12.0, 1758.0, 8.0, 1745.0, 16.0, 1740.0, 13.0, 1733.0, 20.0, 1727.0, 21.0, 1718.0, 34.0, 1709.0, 47.0, 1704.0, 48.0, 1697.0, 52.0, 1693.0, 58.0, 1695.0, 64.0, 1699.0, 70.0, 1695.0, 86.0, 1697.0, 92.0, 1691.0, 114.0, 1692.0, 125.0, 1689.0]], "capital": [96.0, 1700.0], "configuredProductions": {"5": 5800.0}, "id": 105, "locationType": "province", "name": "Casablanca", "neighbourIds": [104, 224, 324, 329], "ownerId": 10, "population": 100000, "productionType": 5}, {"borders": [[260.0, 1684.0, 254.0, 1685.0, 247.0, 1689.0, 227.0, 1681.0, 210.0, 1683.0, 200.0, 1683.0, 193.0, 1685.0, 184.0, 1675.0, 174.0, 1671.0, 164.0, 1672.0, 158.0, 1664.0, 174.0, 1643.0, 175.0, 1635.0, 180.0, 1627.0, 190.0, 1624.0, 193.0, 1616.0, 199.0, 1615.0, 204.0, 1614.0, 209.0, 1617.0, 212.0, 1613.0, 216.0, 1613.0, 218.0, 1619.0, 218.0, 1630.0, 222.0, 1636.0, 222.0, 1639.0, 235.0, 1650.0, 238.0, 1651.0, 244.0, 1655.0, 263.0, 1660.0, 269.0, 1664.0, 266.0, 1677.0]], "capital": [197.0, 1624.0], "configuredProductions": {"1": 2900.0}, "id": 106, "locationType": "province", "name": "Tangiers", "neighbourIds": [107, 115, 221, 222, 224, 329], "ownerId": 10, "population": 100000, "productionType": 1}, {"borders": [[188.0, 1697.0, 190.0, 1694.0, 193.0, 1685.0, 200.0, 1683.0, 210.0, 1683.0, 227.0, 1681.0, 247.0, 1689.0, 251.0, 1697.0, 273.0, 1700.0, 280.0, 1702.0, 284.0, 1706.0, 282.0, 1723.0, 279.0, 1735.0, 278.0, 1744.0, 286.0, 1750.0, 299.0, 1755.0, 314.0, 1750.0, 325.0, 1749.0, 342.0, 1759.0, 355.0, 1759.0, 363.0, 1758.0, 364.0, 1783.0, 369.0, 1790.0, 368.0, 1805.0, 362.0, 1813.0, 354.0, 1815.0, 350.0, 1820.0, 217.0, 1820.0, 220.0, 1809.0, 216.0, 1801.0, 214.0, 1791.0, 217.0, 1783.0, 221.0, 1777.0, 217.0, 1768.0, 208.0, 1755.0, 194.0, 1749.0, 186.0, 1727.0, 186.0, 1716.0, 183.0, 1711.0, 183.0, 1704.0]], "capital": [209.0, 1699.0], "configuredProductions": {"5": 2900.0}, "id": 107, "locationType": "province", "name": "Fez", "neighbourIds": [104, 106, 108, 115, 221, 329], "ownerId": 10, "population": 100000, "productionType": 5}, {"borders": [[439.0, 1668.0, 446.0, 1677.0, 455.0, 1689.0, 455.0, 1697.0, 460.0, 1711.0, 469.0, 1717.0, 468.0, 1725.0, 465.0, 1733.0, 472.0, 1770.0, 461.0, 1772.0, 454.0, 1787.0, 440.0, 1800.0, 432.0, 1820.0, 350.0, 1820.0, 354.0, 1815.0, 362.0, 1813.0, 368.0, 1805.0, 369.0, 1790.0, 364.0, 1783.0, 363.0, 1758.0, 355.0, 1759.0, 355.0, 1753.0, 350.0, 1745.0, 342.0, 1739.0, 342.0, 1721.0, 350.0, 1721.0, 351.0, 1715.0, 344.0, 1715.0, 352.0, 1706.0, 351.0, 1702.0, 345.0, 1705.0, 343.0, 1693.0, 340.0, 1685.0, 356.0, 1686.0, 362.0, 1684.0, 365.0, 1687.0, 369.0, 1682.0, 374.0, 1679.0, 377.0, 1671.0, 387.0, 1670.0, 398.0, 1669.0, 401.0, 1673.0, 410.0, 1673.0, 408.0, 1666.0, 416.0, 1669.0, 419.0, 1673.0, 431.0, 1673.0]], "capital": [400.0, 1677.0], "configuredProductions": {"2": 2900.0}, "id": 108, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [107, 109, 115, 217, 220, 330], "ownerId": 10, "population": 100000, "productionType": 2}, {"borders": [[587.0, 1661.0, 593.0, 1671.0, 588.0, 1677.0, 586.0, 1685.0, 582.0, 1692.0, 590.0, 1695.0, 588.0, 1701.0, 585.0, 1705.0, 585.0, 1713.0, 588.0, 1727.0, 609.0, 1728.0, 616.0, 1732.0, 604.0, 1732.0, 600.0, 1731.0, 597.0, 1734.0, 604.0, 1738.0, 607.0, 1743.0, 606.0, 1751.0, 597.0, 1753.0, 590.0, 1758.0, 584.0, 1761.0, 580.0, 1758.0, 572.0, 1740.0, 567.0, 1737.0, 559.0, 1739.0, 561.0, 1745.0, 557.0, 1750.0, 552.0, 1743.0, 549.0, 1739.0, 544.0, 1738.0, 536.0, 1744.0, 530.0, 1745.0, 524.0, 1745.0, 508.0, 1749.0, 504.0, 1757.0, 490.0, 1761.0, 478.0, 1769.0, 472.0, 1770.0, 465.0, 1733.0, 468.0, 1725.0, 469.0, 1717.0, 460.0, 1711.0, 455.0, 1697.0, 455.0, 1689.0, 446.0, 1677.0, 439.0, 1668.0, 441.0, 1663.0, 452.0, 1663.0, 456.0, 1659.0, 462.0, 1660.0, 469.0, 1655.0, 475.0, 1655.0, 482.0, 1650.0, 500.0, 1657.0, 514.0, 1656.0, 517.0, 1659.0, 530.0, 1657.0, 537.0, 1663.0, 557.0, 1655.0, 560.0, 1658.0, 566.0, 1657.0, 572.0, 1662.0]], "capital": [565.0, 1662.0], "configuredProductions": {"1": 2900.0}, "id": 109, "locationType": "province", "name": "Algiers", "neighbourIds": [108, 111, 215, 216, 217, 330], "ownerId": 10, "population": 100000, "productionType": 1}, {"borders": [[722.0, 1703.0, 714.0, 1706.0, 716.0, 1712.0, 726.0, 1713.0, 728.0, 1717.0, 725.0, 1727.0, 716.0, 1743.0, 722.0, 1749.0, 726.0, 1755.0, 722.0, 1763.0, 735.0, 1771.0, 741.0, 1773.0, 760.0, 1775.0, 764.0, 1774.0, 753.0, 1787.0, 757.0, 1793.0, 754.0, 1801.0, 746.0, 1805.0, 736.0, 1805.0, 727.0, 1818.0, 706.0, 1819.0, 705.0, 1815.0, 702.0, 1816.0, 696.0, 1814.0, 698.0, 1806.0, 695.0, 1802.0, 692.0, 1812.0, 692.0, 1818.0, 675.0, 1819.0, 670.0, 1815.0, 665.0, 1809.0, 682.0, 1813.0, 688.0, 1813.0, 689.0, 1803.0, 682.0, 1805.0, 682.0, 1795.0, 672.0, 1768.0, 672.0, 1750.0, 674.0, 1743.0, 681.0, 1741.0, 685.0, 1735.0, 679.0, 1730.0, 686.0, 1718.0, 687.0, 1711.0, 685.0, 1707.0, 692.0, 1696.0, 685.0, 1689.0, 680.0, 1679.0, 688.0, 1673.0, 693.0, 1671.0, 692.0, 1666.0, 702.0, 1668.0, 708.0, 1675.0, 714.0, 1677.0, 720.0, 1681.0, 725.0, 1681.0, 722.0, 1691.0]], "capital": [703.0, 1703.0], "configuredProductions": {"0": 2900.0}, "id": 110, "locationType": "province", "name": "Constantine", "neighbourIds": [111, 112, 114, 208], "ownerId": 10, "population": 100000}, {"borders": [[680.0, 1679.0, 685.0, 1689.0, 692.0, 1696.0, 685.0, 1707.0, 687.0, 1711.0, 686.0, 1718.0, 679.0, 1730.0, 685.0, 1735.0, 681.0, 1741.0, 674.0, 1743.0, 672.0, 1750.0, 672.0, 1768.0, 682.0, 1795.0, 682.0, 1805.0, 676.0, 1805.0, 675.0, 1800.0, 670.0, 1799.0, 666.0, 1800.0, 669.0, 1809.0, 665.0, 1809.0, 660.0, 1810.0, 664.0, 1819.0, 597.0, 1819.0, 593.0, 1789.0, 584.0, 1761.0, 590.0, 1758.0, 597.0, 1753.0, 606.0, 1751.0, 607.0, 1743.0, 604.0, 1738.0, 624.0, 1739.0, 632.0, 1741.0, 633.0, 1737.0, 628.0, 1732.0, 616.0, 1732.0, 609.0, 1728.0, 588.0, 1727.0, 585.0, 1713.0, 585.0, 1705.0, 588.0, 1701.0, 590.0, 1695.0, 582.0, 1692.0, 586.0, 1685.0, 588.0, 1677.0, 593.0, 1671.0, 587.0, 1661.0, 586.0, 1657.0, 598.0, 1659.0, 602.0, 1661.0, 606.0, 1659.0, 612.0, 1662.0, 616.0, 1661.0, 624.0, 1667.0, 627.0, 1665.0, 640.0, 1678.0, 648.0, 1680.0, 654.0, 1680.0, 660.0, 1682.0, 668.0, 1677.0]], "capital": [642.0, 1683.0], "configuredProductions": {"6": 2900.0}, "id": 111, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [109, 110, 208, 215, 330], "ownerId": 10, "population": 100000, "productionType": 6}, {"borders": [[766.0, 1688.0, 774.0, 1686.0, 780.0, 1689.0, 782.0, 1697.0, 773.0, 1698.0, 770.0, 1701.0, 774.0, 1706.0, 770.0, 1709.0, 760.0, 1709.0, 760.0, 1714.0, 766.0, 1713.0, 764.0, 1735.0, 769.0, 1743.0, 764.0, 1751.0, 765.0, 1759.0, 761.0, 1763.0, 761.0, 1769.0, 764.0, 1774.0, 760.0, 1775.0, 741.0, 1773.0, 735.0, 1771.0, 722.0, 1763.0, 726.0, 1755.0, 722.0, 1749.0, 716.0, 1743.0, 725.0, 1727.0, 728.0, 1717.0, 726.0, 1713.0, 716.0, 1712.0, 714.0, 1706.0, 722.0, 1703.0, 722.0, 1691.0, 725.0, 1681.0, 731.0, 1680.0, 731.0, 1677.0, 728.0, 1673.0, 730.0, 1672.0, 737.0, 1674.0, 744.0, 1675.0, 753.0, 1683.0, 759.0, 1688.0]], "capital": [754.0, 1692.0], "configuredProductions": {"2": 2900.0}, "id": 112, "locationType": "province", "name": "Bone", "neighbourIds": [110, 113, 114, 204, 208], "ownerId": 10, "population": 100000, "productionType": 2}, {"borders": [[791.0, 1689.0, 800.0, 1690.0, 807.0, 1683.0, 812.0, 1681.0, 818.0, 1683.0, 826.0, 1679.0, 834.0, 1681.0, 831.0, 1687.0, 836.0, 1689.0, 838.0, 1687.0, 836.0, 1683.0, 843.0, 1683.0, 856.0, 1687.0, 852.0, 1692.0, 855.0, 1700.0, 859.0, 1702.0, 858.0, 1703.0, 854.0, 1703.0, 857.0, 1707.0, 858.0, 1713.0, 868.0, 1709.0, 868.0, 1706.0, 874.0, 1702.0, 876.0, 1703.0, 881.0, 1699.0, 880.0, 1696.0, 886.0, 1695.0, 891.0, 1704.0, 888.0, 1705.0, 875.0, 1720.0, 876.0, 1723.0, 872.0, 1726.0, 861.0, 1728.0, 858.0, 1743.0, 846.0, 1736.0, 844.0, 1729.0, 843.0, 1723.0, 838.0, 1717.0, 828.0, 1716.0, 808.0, 1733.0, 790.0, 1735.0, 778.0, 1739.0, 769.0, 1743.0, 764.0, 1735.0, 766.0, 1713.0, 760.0, 1714.0, 760.0, 1709.0, 770.0, 1709.0, 774.0, 1706.0, 770.0, 1701.0, 773.0, 1698.0, 782.0, 1697.0, 780.0, 1689.0]], "capital": [848.0, 1705.0], "configuredProductions": {"3": 2835.0}, "id": 113, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [112, 114, 201, 202, 204], "ownerId": 18, "population": 100000, "productionType": 3}, {"borders": [[858.0, 1743.0, 862.0, 1749.0, 865.0, 1757.0, 870.0, 1757.0, 872.0, 1764.0, 878.0, 1765.0, 881.0, 1769.0, 881.0, 1782.0, 885.0, 1789.0, 876.0, 1797.0, 872.0, 1794.0, 868.0, 1799.0, 868.0, 1804.0, 865.0, 1808.0, 857.0, 1810.0, 849.0, 1819.0, 727.0, 1818.0, 736.0, 1805.0, 746.0, 1805.0, 754.0, 1801.0, 757.0, 1793.0, 753.0, 1787.0, 764.0, 1774.0, 761.0, 1769.0, 761.0, 1763.0, 765.0, 1759.0, 764.0, 1751.0, 769.0, 1743.0, 778.0, 1739.0, 790.0, 1735.0, 808.0, 1733.0, 828.0, 1716.0, 838.0, 1717.0, 843.0, 1723.0, 844.0, 1729.0, 846.0, 1736.0]], "capital": [841.0, 1757.0], "configuredProductions": {"5": 2835.0}, "id": 114, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [110, 112, 113, 200, 201], "ownerId": 18, "population": 100000, "productionType": 5}, {"borders": [[345.0, 1705.0, 351.0, 1702.0, 352.0, 1706.0, 344.0, 1715.0, 351.0, 1715.0, 350.0, 1721.0, 342.0, 1721.0, 342.0, 1739.0, 350.0, 1745.0, 355.0, 1753.0, 355.0, 1759.0, 342.0, 1759.0, 325.0, 1749.0, 314.0, 1750.0, 299.0, 1755.0, 286.0, 1750.0, 278.0, 1744.0, 279.0, 1735.0, 282.0, 1723.0, 284.0, 1706.0, 280.0, 1702.0, 273.0, 1700.0, 251.0, 1697.0, 247.0, 1689.0, 254.0, 1685.0, 260.0, 1684.0, 266.0, 1677.0, 269.0, 1664.0, 274.0, 1660.0, 288.0, 1664.0, 298.0, 1667.0, 303.0, 1663.0, 306.0, 1663.0, 306.0, 1667.0, 308.0, 1673.0, 304.0, 1671.0, 302.0, 1672.0, 308.0, 1678.0, 326.0, 1679.0, 328.0, 1681.0, 328.0, 1685.0, 330.0, 1687.0, 334.0, 1685.0, 340.0, 1685.0, 343.0, 1693.0]], "capital": [298.0, 1673.0], "configuredProductions": {"1": 2900.0}, "id": 115, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [106, 107, 108, 220, 221], "ownerId": 10, "population": 100000, "productionType": 1}, {"borders": [[618.0, 890.0, 633.0, 893.0, 646.0, 902.0, 656.0, 907.0, 660.0, 913.0, 654.0, 916.0, 648.0, 915.0, 640.0, 920.0, 648.0, 927.0, 644.0, 931.0, 638.0, 926.0, 632.0, 926.0, 626.0, 924.0, 621.0, 926.0, 635.0, 933.0, 637.0, 937.0, 652.0, 938.0, 655.0, 941.0, 651.0, 946.0, 642.0, 946.0, 638.0, 949.0, 639.0, 955.0, 632.0, 952.0, 628.0, 955.0, 621.0, 953.0, 612.0, 957.0, 606.0, 951.0, 601.0, 948.0, 606.0, 944.0, 604.0, 939.0, 592.0, 927.0, 584.0, 926.0, 570.0, 917.0, 562.0, 907.0, 576.0, 905.0, 583.0, 901.0, 595.0, 895.0, 611.0, 892.0]], "capital": [615.0, 920.0], "configuredProductions": {"0": 2900.0}, "id": 116, "locationType": "province", "name": "London", "neighbourIds": [120, 125, 126, 245, 246, 248, 323], "ownerId": 1, "population": 100000}, {"borders": [[518.0, 818.0, 518.0, 814.0, 511.0, 814.0, 507.0, 819.0, 502.0, 816.0, 498.0, 815.0, 497.0, 813.0, 514.0, 809.0, 516.0, 804.0, 520.0, 806.0, 525.0, 802.0, 529.0, 803.0, 535.0, 802.0, 544.0, 806.0, 546.0, 803.0, 551.0, 806.0, 554.0, 805.0, 554.0, 807.0, 561.0, 808.0, 553.0, 795.0, 561.0, 793.0, 565.0, 790.0, 570.0, 797.0, 574.0, 803.0, 597.0, 812.0, 600.0, 818.0, 598.0, 826.0, 603.0, 835.0, 601.0, 843.0, 590.0, 841.0, 581.0, 841.0, 571.0, 843.0, 562.0, 847.0, 552.0, 857.0, 542.0, 851.0, 540.0, 845.0, 532.0, 837.0, 521.0, 839.0, 514.0, 836.0], [518.0, 799.0, 517.0, 793.0, 514.0, 790.0, 515.0, 786.0, 520.0, 787.0, 520.0, 791.0, 522.0, 794.0, 521.0, 799.0]], "capital": [563.0, 801.0], "configuredProductions": {"2": 2900.0}, "id": 117, "locationType": "province", "name": "Liverpool", "neighbourIds": [118, 120, 126, 277, 278], "ownerId": 1, "population": 100000, "productionType": 2}, {"borders": [[573.0, 768.0, 582.0, 762.0, 590.0, 764.0, 596.0, 767.0, 606.0, 761.0, 623.0, 758.0, 624.0, 759.0, 639.0, 763.0, 643.0, 768.0, 652.0, 783.0, 650.0, 787.0, 650.0, 790.0, 654.0, 797.0, 649.0, 796.0, 654.0, 815.0, 653.0, 818.0, 642.0, 806.0, 640.0, 807.0, 630.0, 806.0, 641.0, 812.0, 644.0, 818.0, 647.0, 819.0, 646.0, 823.0, 649.0, 826.0, 650.0, 834.0, 653.0, 842.0, 651.0, 846.0, 647.0, 849.0, 642.0, 850.0, 634.0, 840.0, 634.0, 838.0, 623.0, 837.0, 611.0, 853.0, 604.0, 851.0, 601.0, 843.0, 603.0, 835.0, 598.0, 826.0, 600.0, 818.0, 597.0, 812.0, 574.0, 803.0, 570.0, 797.0, 565.0, 790.0, 559.0, 788.0, 559.0, 783.0, 562.0, 779.0, 567.0, 777.0]], "capital": [585.0, 797.0], "configuredProductions": {"4": 2900.0}, "id": 118, "locationType": "province", "name": "Manchester", "neighbourIds": [117, 120, 127, 248, 322], "ownerId": 1, "population": 100000, "productionType": 4}, {"borders": [[542.0, 693.0, 547.0, 693.0, 548.0, 690.0, 545.0, 683.0, 548.0, 675.0, 552.0, 669.0, 557.0, 669.0, 560.0, 671.0, 555.0, 664.0, 558.0, 655.0, 553.0, 655.0, 546.0, 667.0, 544.0, 661.0, 539.0, 662.0, 539.0, 659.0, 550.0, 651.0, 553.0, 647.0, 547.0, 649.0, 539.0, 653.0, 533.0, 653.0, 531.0, 673.0, 522.0, 679.0, 519.0, 683.0, 514.0, 687.0, 511.0, 688.0, 508.0, 686.0, 511.0, 683.0, 514.0, 683.0, 517.0, 676.0, 526.0, 671.0, 525.0, 667.0, 527.0, 660.0, 523.0, 656.0, 526.0, 651.0, 529.0, 651.0, 529.0, 646.0, 538.0, 640.0, 542.0, 640.0, 543.0, 639.0, 542.0, 634.0, 549.0, 630.0, 552.0, 631.0, 558.0, 630.0, 566.0, 643.0, 574.0, 648.0, 580.0, 644.0, 588.0, 637.0, 593.0, 639.0, 602.0, 639.0, 608.0, 638.0, 618.0, 633.0, 624.0, 639.0, 632.0, 637.0, 637.0, 643.0, 628.0, 648.0, 624.0, 657.0, 619.0, 657.0, 614.0, 659.0, 598.0, 657.0, 601.0, 663.0, 608.0, 663.0, 614.0, 667.0, 610.0, 670.0, 600.0, 669.0, 598.0, 671.0, 590.0, 669.0, 588.0, 666.0, 583.0, 667.0, 585.0, 671.0, 594.0, 675.0, 601.0, 680.0, 612.0, 681.0, 617.0, 688.0, 626.0, 692.0, 627.0, 699.0, 631.0, 709.0, 619.0, 711.0, 610.0, 718.0, 608.0, 725.0, 600.0, 729.0, 594.0, 725.0, 588.0, 723.0, 577.0, 723.0, 570.0, 722.0, 564.0, 719.0, 562.0, 727.0, 551.0, 728.0, 546.0, 722.0, 541.0, 721.0, 541.0, 729.0, 539.0, 731.0, 531.0, 719.0, 527.0, 720.0, 524.0, 726.0, 522.0, 719.0, 518.0, 716.0, 519.0, 711.0, 521.0, 707.0, 524.0, 711.0, 526.0, 709.0, 526.0, 705.0, 534.0, 699.0], [534.0, 683.0, 535.0, 686.0, 528.0, 685.0, 529.0, 681.0, 534.0, 676.0, 536.0, 677.0]], "capital": [568.0, 679.0], "configuredProductions": {"3": 2900.0}, "id": 119, "locationType": "province", "name": "Glasgow", "neighbourIds": [121, 127, 278, 279, 280, 284], "ownerId": 1, "population": 100000, "productionType": 3}, {"borders": [[552.0, 857.0, 562.0, 847.0, 571.0, 843.0, 581.0, 841.0, 590.0, 841.0, 601.0, 843.0, 604.0, 851.0, 611.0, 853.0, 623.0, 837.0, 634.0, 838.0, 634.0, 840.0, 642.0, 850.0, 642.0, 854.0, 647.0, 857.0, 648.0, 853.0, 653.0, 851.0, 663.0, 861.0, 664.0, 858.0, 674.0, 861.0, 676.0, 867.0, 681.0, 872.0, 680.0, 878.0, 681.0, 882.0, 678.0, 891.0, 674.0, 896.0, 672.0, 906.0, 668.0, 907.0, 665.0, 904.0, 656.0, 907.0, 646.0, 902.0, 633.0, 893.0, 618.0, 890.0, 611.0, 892.0, 595.0, 895.0, 583.0, 901.0, 584.0, 896.0, 580.0, 892.0, 576.0, 895.0, 569.0, 892.0, 567.0, 885.0, 557.0, 877.0, 553.0, 867.0]], "capital": [582.0, 855.0], "configuredProductions": {"4": 2900.0}, "id": 120, "locationType": "province", "name": "Birmingham", "neighbourIds": [116, 117, 118, 126, 248], "ownerId": 1, "population": 100000, "productionType": 4}, {"borders": [[556.0, 625.0, 543.0, 624.0, 537.0, 627.0, 534.0, 625.0, 538.0, 623.0, 534.0, 619.0, 527.0, 617.0, 525.0, 615.0, 528.0, 613.0, 540.0, 613.0, 544.0, 609.0, 548.0, 607.0, 550.0, 603.0, 556.0, 605.0, 557.0, 603.0, 551.0, 599.0, 556.0, 594.0, 551.0, 593.0, 552.0, 583.0, 556.0, 586.0, 560.0, 573.0, 566.0, 568.0, 574.0, 570.0, 575.0, 575.0, 578.0, 570.0, 578.0, 561.0, 582.0, 553.0, 584.0, 558.0, 586.0, 557.0, 590.0, 552.0, 593.0, 544.0, 601.0, 539.0, 604.0, 541.0, 606.0, 551.0, 610.0, 549.0, 619.0, 552.0, 622.0, 551.0, 627.0, 553.0, 635.0, 550.0, 638.0, 552.0, 644.0, 554.0, 642.0, 558.0, 639.0, 560.0, 642.0, 567.0, 636.0, 571.0, 632.0, 568.0, 615.0, 577.0, 614.0, 582.0, 606.0, 583.0, 602.0, 577.0, 598.0, 579.0, 605.0, 587.0, 614.0, 588.0, 608.0, 593.0, 600.0, 592.0, 595.0, 593.0, 593.0, 599.0, 594.0, 601.0, 600.0, 599.0, 609.0, 601.0, 619.0, 596.0, 623.0, 601.0, 636.0, 604.0, 640.0, 608.0, 644.0, 605.0, 649.0, 607.0, 652.0, 607.0, 658.0, 611.0, 659.0, 615.0, 654.0, 627.0, 646.0, 630.0, 644.0, 637.0, 637.0, 643.0, 632.0, 637.0, 624.0, 639.0, 618.0, 633.0, 608.0, 638.0, 602.0, 639.0, 593.0, 639.0, 588.0, 637.0, 580.0, 644.0, 574.0, 648.0, 566.0, 643.0, 558.0, 630.0], [518.0, 636.0, 518.0, 632.0, 525.0, 631.0, 524.0, 626.0, 519.0, 621.0, 522.0, 619.0, 529.0, 623.0, 530.0, 629.0, 533.0, 633.0, 528.0, 638.0], [512.0, 653.0, 509.0, 657.0, 510.0, 665.0, 506.0, 665.0, 500.0, 670.0, 503.0, 663.0, 503.0, 660.0, 498.0, 659.0, 500.0, 653.0, 504.0, 649.0], [534.0, 606.0, 539.0, 598.0, 537.0, 595.0, 533.0, 595.0, 532.0, 587.0, 523.0, 580.0, 530.0, 577.0, 535.0, 582.0, 536.0, 575.0, 540.0, 573.0, 541.0, 585.0, 540.0, 593.0, 547.0, 600.0, 542.0, 605.0], [558.0, 536.0, 555.0, 539.0, 556.0, 543.0, 546.0, 549.0, 548.0, 552.0, 544.0, 557.0, 542.0, 553.0, 538.0, 555.0, 538.0, 565.0, 525.0, 568.0, 527.0, 563.0, 532.0, 559.0, 527.0, 549.0, 528.0, 545.0, 534.0, 541.0, 538.0, 545.0, 540.0, 543.0, 540.0, 538.0, 550.0, 536.0, 554.0, 533.0, 559.0, 533.0], [650.0, 541.0, 646.0, 539.0, 644.0, 529.0, 649.0, 528.0, 652.0, 537.0, 660.0, 544.0, 657.0, 547.0], [712.0, 489.0, 704.0, 486.0, 706.0, 483.0, 712.0, 485.0, 715.0, 481.0, 713.0, 471.0, 718.0, 472.0, 720.0, 483.0, 720.0, 487.0, 716.0, 493.0, 716.0, 497.0, 710.0, 503.0]], "capital": [593.0, 607.0], "configuredProductions": {"6": 2900.0}, "id": 121, "locationType": "province", "name": "Inverness", "neighbourIds": [119, 280, 281, 282, 283, 284], "ownerId": 1, "population": 100000, "productionType": 6}, {"borders": [[464.0, 739.0, 458.0, 737.0, 454.0, 729.0, 438.0, 729.0, 428.0, 734.0, 417.0, 738.0, 416.0, 748.0, 409.0, 752.0, 399.0, 751.0, 392.0, 754.0, 384.0, 754.0, 384.0, 749.0, 366.0, 744.0, 373.0, 740.0, 371.0, 736.0, 362.0, 737.0, 354.0, 732.0, 348.0, 727.0, 354.0, 728.0, 352.0, 721.0, 359.0, 724.0, 362.0, 720.0, 369.0, 723.0, 366.0, 716.0, 370.0, 711.0, 376.0, 712.0, 376.0, 710.0, 372.0, 706.0, 377.0, 700.0, 372.0, 694.0, 366.0, 694.0, 370.0, 687.0, 375.0, 689.0, 376.0, 692.0, 382.0, 688.0, 386.0, 690.0, 395.0, 701.0, 395.0, 708.0, 398.0, 705.0, 399.0, 701.0, 408.0, 705.0, 412.0, 711.0, 415.0, 707.0, 420.0, 704.0, 414.0, 700.0, 422.0, 698.0, 428.0, 699.0, 435.0, 694.0, 428.0, 691.0, 414.0, 687.0, 412.0, 684.0, 418.0, 679.0, 426.0, 682.0, 424.0, 675.0, 432.0, 679.0, 431.0, 674.0, 428.0, 671.0, 436.0, 667.0, 444.0, 670.0, 447.0, 665.0, 450.0, 666.0, 452.0, 673.0, 456.0, 673.0, 454.0, 668.0, 458.0, 665.0, 461.0, 669.0, 458.0, 676.0, 458.0, 678.0, 464.0, 674.0, 465.0, 668.0, 468.0, 669.0, 472.0, 665.0, 476.0, 671.0, 472.0, 675.0, 467.0, 677.0, 467.0, 680.0, 485.0, 677.0, 496.0, 684.0, 498.0, 687.0, 498.0, 703.0, 502.0, 703.0, 499.0, 712.0, 494.0, 712.0, 491.0, 716.0, 502.0, 717.0, 506.0, 720.0, 504.0, 731.0, 502.0, 723.0, 500.0, 721.0, 498.0, 729.0, 499.0, 735.0, 496.0, 737.0, 492.0, 731.0, 486.0, 733.0, 484.0, 737.0, 480.0, 737.0, 480.0, 735.0, 471.0, 739.0]], "capital": [486.0, 713.0], "configuredProductions": {"0": 2900.0}, "id": 122, "locationType": "province", "name": "Belfast", "neighbourIds": [123, 124, 278, 279, 280, 290], "ownerId": 1, "population": 100000}, {"borders": [[417.0, 738.0, 428.0, 734.0, 438.0, 729.0, 454.0, 729.0, 458.0, 737.0, 464.0, 739.0, 471.0, 739.0, 470.0, 744.0, 473.0, 752.0, 468.0, 759.0, 470.0, 766.0, 467.0, 770.0, 466.0, 776.0, 461.0, 780.0, 462.0, 785.0, 455.0, 798.0, 450.0, 799.0, 451.0, 804.0, 447.0, 805.0, 444.0, 815.0, 437.0, 816.0, 441.0, 820.0, 440.0, 825.0, 431.0, 823.0, 433.0, 819.0, 425.0, 819.0, 424.0, 811.0, 419.0, 817.0, 404.0, 813.0, 400.0, 816.0, 396.0, 807.0, 399.0, 796.0, 406.0, 786.0, 400.0, 782.0, 398.0, 777.0, 408.0, 774.0, 416.0, 767.0, 416.0, 748.0]], "capital": [458.0, 777.0], "configuredProductions": {"1": 2900.0}, "id": 123, "locationType": "province", "name": "Dublin", "neighbourIds": [122, 124, 276, 277, 278], "ownerId": 1, "population": 100000, "productionType": 1}, {"borders": [[329.0, 789.0, 333.0, 785.0, 333.0, 784.0, 312.0, 779.0, 313.0, 775.0, 324.0, 774.0, 326.0, 778.0, 332.0, 775.0, 334.0, 779.0, 338.0, 779.0, 337.0, 776.0, 341.0, 775.0, 346.0, 777.0, 347.0, 773.0, 350.0, 773.0, 355.0, 776.0, 372.0, 777.0, 375.0, 779.0, 380.0, 776.0, 376.0, 773.0, 378.0, 765.0, 370.0, 773.0, 362.0, 772.0, 357.0, 768.0, 353.0, 768.0, 344.0, 771.0, 344.0, 767.0, 354.0, 761.0, 364.0, 759.0, 364.0, 753.0, 372.0, 752.0, 384.0, 754.0, 392.0, 754.0, 399.0, 751.0, 409.0, 752.0, 416.0, 748.0, 416.0, 767.0, 408.0, 774.0, 398.0, 777.0, 400.0, 782.0, 406.0, 786.0, 399.0, 796.0, 396.0, 807.0, 400.0, 816.0, 404.0, 819.0, 398.0, 821.0, 394.0, 816.0, 390.0, 820.0, 392.0, 826.0, 382.0, 826.0, 382.0, 821.0, 383.0, 817.0, 378.0, 815.0, 373.0, 817.0, 373.0, 824.0, 368.0, 829.0, 362.0, 832.0, 359.0, 829.0, 354.0, 831.0, 350.0, 828.0, 342.0, 829.0, 338.0, 825.0, 322.0, 827.0, 322.0, 820.0, 336.0, 819.0, 339.0, 815.0, 314.0, 816.0, 311.0, 811.0, 322.0, 809.0, 330.0, 810.0, 337.0, 809.0, 337.0, 805.0, 326.0, 803.0, 315.0, 805.0, 316.0, 797.0, 313.0, 794.0, 318.0, 789.0]], "capital": [381.0, 780.0], "configuredProductions": {"2": 2900.0}, "id": 124, "locationType": "province", "name": "Limerick", "neighbourIds": [122, 123, 274, 276, 290, 291], "ownerId": 1, "population": 100000, "productionType": 2}, {"borders": [[536.0, 943.0, 531.0, 941.0, 530.0, 937.0, 526.0, 935.0, 524.0, 930.0, 519.0, 931.0, 514.0, 933.0, 510.0, 929.0, 503.0, 938.0, 494.0, 940.0, 496.0, 947.0, 492.0, 946.0, 488.0, 951.0, 485.0, 950.0, 484.0, 941.0, 479.0, 941.0, 476.0, 938.0, 474.0, 933.0, 473.0, 937.0, 466.0, 937.0, 460.0, 931.0, 440.0, 933.0, 441.0, 939.0, 436.0, 940.0, 435.0, 935.0, 430.0, 932.0, 426.0, 934.0, 424.0, 934.0, 424.0, 930.0, 425.0, 927.0, 428.0, 930.0, 433.0, 927.0, 440.0, 926.0, 446.0, 921.0, 448.0, 923.0, 460.0, 914.0, 467.0, 916.0, 470.0, 913.0, 469.0, 910.0, 472.0, 906.0, 475.0, 899.0, 480.0, 898.0, 489.0, 904.0, 495.0, 902.0, 489.0, 895.0, 500.0, 896.0, 509.0, 901.0, 523.0, 907.0, 525.0, 902.0, 537.0, 899.0, 542.0, 896.0, 544.0, 898.0, 562.0, 907.0, 570.0, 917.0, 584.0, 926.0, 592.0, 927.0, 604.0, 939.0, 606.0, 944.0, 601.0, 948.0, 592.0, 947.0, 587.0, 947.0, 576.0, 945.0, 573.0, 939.0, 571.0, 938.0, 567.0, 942.0, 561.0, 943.0, 551.0, 942.0, 548.0, 940.0, 544.0, 945.0, 539.0, 941.0], [568.0, 954.0, 563.0, 947.0, 575.0, 949.0]], "capital": [579.0, 941.0], "configuredProductions": {"1": 2900.0}, "id": 125, "locationType": "province", "name": "Portsmouth", "neighbourIds": [116, 126, 243, 244, 245], "ownerId": 1, "population": 100000, "productionType": 1}, {"borders": [[542.0, 896.0, 545.0, 892.0, 538.0, 891.0, 533.0, 895.0, 525.0, 894.0, 519.0, 896.0, 506.0, 891.0, 505.0, 885.0, 508.0, 884.0, 503.0, 880.0, 499.0, 885.0, 495.0, 883.0, 493.0, 881.0, 490.0, 881.0, 497.0, 877.0, 490.0, 875.0, 493.0, 871.0, 490.0, 865.0, 486.0, 870.0, 482.0, 868.0, 478.0, 871.0, 468.0, 875.0, 463.0, 870.0, 469.0, 871.0, 476.0, 865.0, 474.0, 863.0, 462.0, 865.0, 459.0, 863.0, 460.0, 860.0, 467.0, 859.0, 469.0, 856.0, 462.0, 849.0, 467.0, 849.0, 472.0, 846.0, 481.0, 849.0, 484.0, 845.0, 490.0, 846.0, 489.0, 849.0, 508.0, 844.0, 508.0, 840.0, 514.0, 836.0, 521.0, 839.0, 532.0, 837.0, 540.0, 845.0, 542.0, 851.0, 552.0, 857.0, 553.0, 867.0, 557.0, 877.0, 567.0, 885.0, 569.0, 892.0, 576.0, 895.0, 580.0, 892.0, 584.0, 896.0, 583.0, 901.0, 576.0, 905.0, 562.0, 907.0, 544.0, 898.0]], "capital": [533.0, 889.0], "configuredProductions": {"2": 2900.0}, "id": 126, "locationType": "province", "name": "Cardiff", "neighbourIds": [116, 117, 120, 125, 243, 275, 276, 277], "ownerId": 1, "population": 100000, "productionType": 2}, {"borders": [[623.0, 758.0, 606.0, 761.0, 596.0, 767.0, 590.0, 764.0, 582.0, 762.0, 573.0, 768.0, 564.0, 763.0, 564.0, 766.0, 561.0, 770.0, 557.0, 767.0, 554.0, 763.0, 555.0, 755.0, 552.0, 753.0, 552.0, 745.0, 564.0, 732.0, 575.0, 726.0, 577.0, 723.0, 588.0, 723.0, 594.0, 725.0, 600.0, 729.0, 608.0, 725.0, 610.0, 718.0, 619.0, 711.0, 631.0, 709.0, 630.0, 715.0, 630.0, 722.0, 627.0, 727.0, 625.0, 737.0, 627.0, 743.0, 624.0, 747.0, 627.0, 752.0, 624.0, 759.0], [535.0, 749.0, 530.0, 753.0, 518.0, 754.0, 520.0, 750.0, 526.0, 746.0, 528.0, 741.0, 535.0, 739.0]], "capital": [616.0, 733.0], "configuredProductions": {"0": 5800.0}, "id": 127, "locationType": "province", "name": "Newcastle", "neighbourIds": [118, 119, 278, 284, 322], "ownerId": 1, "population": 100000}, {"borders": [[1149.0, 742.0, 1132.0, 765.0, 1127.0, 761.0, 1104.0, 761.0, 1101.0, 766.0, 1102.0, 769.0, 1101.0, 774.0, 1096.0, 767.0, 1088.0, 769.0, 1089.0, 777.0, 1083.0, 780.0, 1080.0, 785.0, 1085.0, 795.0, 1079.0, 803.0, 1074.0, 805.0, 1045.0, 798.0, 1043.0, 791.0, 1051.0, 787.0, 1050.0, 783.0, 1045.0, 773.0, 1042.0, 765.0, 1047.0, 755.0, 1039.0, 748.0, 1044.0, 745.0, 1050.0, 745.0, 1052.0, 743.0, 1048.0, 737.0, 1046.0, 728.0, 1054.0, 723.0, 1072.0, 716.0, 1077.0, 711.0, 1078.0, 699.0, 1090.0, 686.0, 1094.0, 684.0, 1096.0, 677.0, 1104.0, 663.0, 1107.0, 655.0, 1116.0, 647.0, 1128.0, 650.0, 1133.0, 647.0, 1141.0, 649.0, 1152.0, 653.0, 1164.0, 659.0, 1163.0, 673.0, 1160.0, 681.0, 1164.0, 686.0, 1160.0, 691.0, 1157.0, 699.0, 1160.0, 703.0, 1156.0, 706.0, 1155.0, 715.0, 1153.0, 721.0, 1157.0, 725.0, 1151.0, 731.0], [1166.0, 736.0, 1160.0, 747.0, 1160.0, 753.0, 1154.0, 761.0, 1151.0, 759.0, 1151.0, 754.0, 1155.0, 747.0, 1157.0, 741.0, 1162.0, 735.0, 1166.0, 719.0, 1169.0, 711.0, 1174.0, 710.0]], "capital": [1142.0, 739.0], "configuredProductions": {"4": 5800.0}, "id": 128, "locationType": "province", "name": "Calmar", "neighbourIds": [129, 130, 257, 263, 269, 270], "ownerId": 6, "population": 100000, "productionType": 4}, {"borders": [[1024.0, 683.0, 1031.0, 650.0, 1027.0, 647.0, 1022.0, 651.0, 1016.0, 647.0, 1019.0, 635.0, 1014.0, 629.0, 1016.0, 625.0, 1016.0, 616.0, 1020.0, 612.0, 1027.0, 609.0, 1034.0, 607.0, 1035.0, 589.0, 1042.0, 593.0, 1047.0, 600.0, 1056.0, 605.0, 1068.0, 607.0, 1071.0, 606.0, 1072.0, 618.0, 1068.0, 619.0, 1062.0, 613.0, 1058.0, 617.0, 1060.0, 625.0, 1056.0, 630.0, 1056.0, 639.0, 1051.0, 650.0, 1056.0, 651.0, 1062.0, 646.0, 1066.0, 640.0, 1070.0, 642.0, 1070.0, 645.0, 1082.0, 641.0, 1092.0, 627.0, 1096.0, 623.0, 1096.0, 609.0, 1107.0, 602.0, 1112.0, 602.0, 1119.0, 609.0, 1124.0, 607.0, 1134.0, 613.0, 1136.0, 617.0, 1140.0, 617.0, 1124.0, 623.0, 1120.0, 624.0, 1114.0, 630.0, 1106.0, 641.0, 1101.0, 646.0, 1097.0, 655.0, 1092.0, 661.0, 1090.0, 671.0, 1090.0, 679.0, 1096.0, 677.0, 1094.0, 684.0, 1090.0, 686.0, 1078.0, 699.0, 1077.0, 711.0, 1072.0, 716.0, 1054.0, 723.0, 1046.0, 728.0, 1038.0, 723.0, 1037.0, 718.0, 1032.0, 716.0, 1030.0, 707.0, 1031.0, 700.0, 1024.0, 697.0, 1029.0, 689.0, 1029.0, 685.0]], "capital": [1033.0, 687.0], "configuredProductions": {"3": 2900.0}, "id": 129, "locationType": "province", "name": "Gothenburg", "neighbourIds": [128, 130, 131, 137, 255, 256, 257, 331], "ownerId": 6, "population": 100000, "productionType": 3}, {"borders": [[1208.0, 605.0, 1206.0, 609.0, 1202.0, 613.0, 1196.0, 611.0, 1196.0, 599.0, 1192.0, 596.0, 1188.0, 587.0, 1186.0, 587.0, 1187.0, 595.0, 1192.0, 600.0, 1192.0, 606.0, 1183.0, 605.0, 1176.0, 599.0, 1164.0, 596.0, 1160.0, 601.0, 1151.0, 600.0, 1149.0, 602.0, 1161.0, 607.0, 1166.0, 607.0, 1176.0, 609.0, 1180.0, 617.0, 1183.0, 613.0, 1195.0, 617.0, 1208.0, 614.0, 1213.0, 619.0, 1204.0, 621.0, 1201.0, 629.0, 1197.0, 627.0, 1196.0, 621.0, 1191.0, 621.0, 1188.0, 628.0, 1183.0, 635.0, 1166.0, 636.0, 1173.0, 640.0, 1168.0, 642.0, 1161.0, 641.0, 1156.0, 638.0, 1150.0, 639.0, 1153.0, 643.0, 1159.0, 644.0, 1165.0, 647.0, 1168.0, 651.0, 1166.0, 653.0, 1152.0, 653.0, 1141.0, 649.0, 1133.0, 647.0, 1128.0, 650.0, 1116.0, 647.0, 1113.0, 642.0, 1115.0, 634.0, 1114.0, 630.0, 1120.0, 624.0, 1124.0, 623.0, 1140.0, 617.0, 1147.0, 613.0, 1155.0, 613.0, 1142.0, 610.0, 1136.0, 606.0, 1124.0, 607.0, 1119.0, 609.0, 1126.0, 597.0, 1135.0, 583.0, 1132.0, 574.0, 1141.0, 570.0, 1142.0, 562.0, 1140.0, 558.0, 1133.0, 552.0, 1132.0, 547.0, 1127.0, 533.0, 1128.0, 527.0, 1139.0, 505.0, 1146.0, 503.0, 1152.0, 503.0, 1159.0, 499.0, 1172.0, 497.0, 1184.0, 495.0, 1178.0, 512.0, 1182.0, 511.0, 1181.0, 517.0, 1182.0, 524.0, 1180.0, 530.0, 1184.0, 543.0, 1191.0, 547.0, 1192.0, 555.0, 1198.0, 550.0, 1201.0, 551.0, 1208.0, 559.0, 1210.0, 565.0, 1222.0, 572.0, 1222.0, 584.0, 1227.0, 587.0, 1220.0, 597.0, 1212.0, 605.0], [1234.0, 685.0, 1229.0, 689.0, 1225.0, 691.0, 1223.0, 697.0, 1226.0, 703.0, 1226.0, 707.0, 1218.0, 713.0, 1222.0, 714.0, 1220.0, 717.0, 1212.0, 721.0, 1209.0, 729.0, 1200.0, 732.0, 1207.0, 725.0, 1202.0, 721.0, 1202.0, 716.0, 1206.0, 709.0, 1204.0, 700.0, 1208.0, 695.0, 1213.0, 691.0, 1214.0, 686.0, 1218.0, 686.0, 1222.0, 682.0, 1229.0, 680.0]], "capital": [1202.0, 607.0], "configuredProductions": {"2": 2900.0}, "id": 130, "locationType": "province", "name": "Stockholm", "neighbourIds": [128, 129, 131, 132, 265, 270, 271], "ownerId": 6, "population": 100000, "productionType": 2}, {"borders": [[1152.0, 503.0, 1146.0, 503.0, 1139.0, 505.0, 1128.0, 527.0, 1127.0, 533.0, 1132.0, 547.0, 1133.0, 552.0, 1140.0, 558.0, 1142.0, 562.0, 1141.0, 570.0, 1132.0, 574.0, 1135.0, 583.0, 1126.0, 597.0, 1119.0, 609.0, 1112.0, 602.0, 1107.0, 602.0, 1096.0, 609.0, 1094.0, 604.0, 1091.0, 610.0, 1087.0, 604.0, 1082.0, 605.0, 1076.0, 603.0, 1073.0, 599.0, 1066.0, 601.0, 1071.0, 606.0, 1068.0, 607.0, 1056.0, 605.0, 1047.0, 600.0, 1042.0, 593.0, 1035.0, 589.0, 1040.0, 583.0, 1042.0, 574.0, 1056.0, 570.0, 1059.0, 559.0, 1059.0, 547.0, 1060.0, 540.0, 1054.0, 521.0, 1068.0, 520.0, 1070.0, 500.0, 1060.0, 482.0, 1059.0, 474.0, 1062.0, 467.0, 1067.0, 454.0, 1062.0, 443.0, 1068.0, 439.0, 1067.0, 426.0, 1072.0, 421.0, 1068.0, 407.0, 1071.0, 399.0, 1067.0, 392.0, 1072.0, 380.0, 1077.0, 371.0, 1092.0, 363.0, 1096.0, 371.0, 1097.0, 377.0, 1096.0, 387.0, 1108.0, 395.0, 1110.0, 405.0, 1117.0, 411.0, 1116.0, 419.0, 1114.0, 423.0, 1103.0, 429.0, 1101.0, 445.0, 1104.0, 463.0, 1114.0, 473.0, 1124.0, 471.0, 1134.0, 474.0, 1138.0, 481.0, 1146.0, 493.0]], "capital": [1083.0, 599.0], "configuredProductions": {"3": 2900.0}, "id": 131, "locationType": "province", "name": "Carl<PERSON>", "neighbourIds": [129, 130, 132, 137, 138, 139, 255, 270], "ownerId": 6, "population": 100000, "productionType": 3}, {"borders": [[1226.0, 414.0, 1218.0, 422.0, 1215.0, 424.0, 1216.0, 426.0, 1220.0, 427.0, 1222.0, 432.0, 1218.0, 432.0, 1213.0, 435.0, 1209.0, 432.0, 1205.0, 421.0, 1202.0, 421.0, 1202.0, 426.0, 1206.0, 439.0, 1203.0, 441.0, 1202.0, 448.0, 1197.0, 448.0, 1195.0, 447.0, 1190.0, 448.0, 1190.0, 453.0, 1192.0, 458.0, 1196.0, 462.0, 1192.0, 468.0, 1192.0, 474.0, 1188.0, 483.0, 1192.0, 490.0, 1186.0, 490.0, 1184.0, 495.0, 1172.0, 497.0, 1159.0, 499.0, 1152.0, 503.0, 1146.0, 493.0, 1138.0, 481.0, 1134.0, 474.0, 1124.0, 471.0, 1114.0, 473.0, 1104.0, 463.0, 1101.0, 445.0, 1103.0, 429.0, 1114.0, 423.0, 1121.0, 427.0, 1120.0, 421.0, 1122.0, 414.0, 1125.0, 411.0, 1130.0, 419.0, 1130.0, 413.0, 1124.0, 408.0, 1128.0, 401.0, 1122.0, 404.0, 1115.0, 402.0, 1116.0, 406.0, 1110.0, 405.0, 1108.0, 395.0, 1096.0, 387.0, 1097.0, 377.0, 1096.0, 371.0, 1092.0, 363.0, 1102.0, 363.0, 1109.0, 370.0, 1114.0, 367.0, 1114.0, 361.0, 1118.0, 355.0, 1110.0, 352.0, 1118.0, 345.0, 1124.0, 329.0, 1129.0, 322.0, 1131.0, 299.0, 1132.0, 267.0, 1137.0, 261.0, 1150.0, 261.0, 1156.0, 253.0, 1158.0, 240.0, 1168.0, 245.0, 1175.0, 253.0, 1177.0, 265.0, 1175.0, 273.0, 1191.0, 285.0, 1185.0, 291.0, 1190.0, 303.0, 1185.0, 310.0, 1172.0, 310.0, 1170.0, 317.0, 1178.0, 326.0, 1176.0, 341.0, 1171.0, 351.0, 1190.0, 370.0, 1206.0, 373.0, 1212.0, 388.0, 1222.0, 406.0]], "capital": [1184.0, 456.0], "configuredProductions": {"3": 2900.0}, "id": 132, "locationType": "province", "name": "Sundsvall", "neighbourIds": [130, 131, 133, 139, 271, 272], "ownerId": 6, "population": 100000, "productionType": 3}, {"borders": [[1208.0, 193.0, 1213.0, 199.0, 1218.0, 201.0, 1222.0, 207.0, 1232.0, 215.0, 1236.0, 221.0, 1240.0, 225.0, 1243.0, 232.0, 1247.0, 231.0, 1262.0, 248.0, 1264.0, 257.0, 1259.0, 265.0, 1256.0, 279.0, 1261.0, 295.0, 1268.0, 309.0, 1277.0, 313.0, 1280.0, 316.0, 1282.0, 321.0, 1280.0, 328.0, 1273.0, 333.0, 1278.0, 339.0, 1278.0, 343.0, 1282.0, 347.0, 1285.0, 357.0, 1282.0, 362.0, 1280.0, 360.0, 1270.0, 370.0, 1268.0, 382.0, 1262.0, 385.0, 1261.0, 389.0, 1252.0, 393.0, 1246.0, 400.0, 1242.0, 397.0, 1237.0, 404.0, 1236.0, 411.0, 1233.0, 411.0, 1228.0, 408.0, 1222.0, 406.0, 1212.0, 388.0, 1206.0, 373.0, 1190.0, 370.0, 1171.0, 351.0, 1176.0, 341.0, 1178.0, 326.0, 1170.0, 317.0, 1172.0, 310.0, 1185.0, 310.0, 1190.0, 303.0, 1185.0, 291.0, 1191.0, 285.0, 1175.0, 273.0, 1177.0, 265.0, 1175.0, 253.0, 1168.0, 245.0, 1158.0, 240.0, 1174.0, 224.0, 1174.0, 221.0, 1174.0, 198.0, 1180.0, 201.0, 1193.0, 199.0, 1199.0, 192.0]], "capital": [1256.0, 383.0], "configuredProductions": {"3": 5800.0}, "id": 133, "locationType": "province", "name": "Umea", "neighbourIds": [132, 134, 139, 272, 273, 311], "ownerId": 6, "population": 100000, "productionType": 3}, {"borders": [[1286.0, 304.0, 1280.0, 309.0, 1277.0, 313.0, 1268.0, 309.0, 1261.0, 295.0, 1256.0, 279.0, 1259.0, 265.0, 1264.0, 257.0, 1262.0, 248.0, 1247.0, 231.0, 1242.0, 219.0, 1233.0, 208.0, 1228.0, 205.0, 1222.0, 197.0, 1216.0, 195.0, 1208.0, 188.0, 1202.0, 187.0, 1199.0, 192.0, 1193.0, 199.0, 1180.0, 201.0, 1174.0, 198.0, 1175.0, 195.0, 1186.0, 182.0, 1192.0, 175.0, 1199.0, 170.0, 1202.0, 174.0, 1210.0, 167.0, 1212.0, 163.0, 1214.0, 147.0, 1223.0, 141.0, 1230.0, 148.0, 1236.0, 144.0, 1243.0, 151.0, 1252.0, 154.0, 1252.0, 149.0, 1247.0, 140.0, 1252.0, 137.0, 1252.0, 126.0, 1256.0, 125.0, 1266.0, 138.0, 1272.0, 142.0, 1285.0, 155.0, 1299.0, 162.0, 1305.0, 173.0, 1317.0, 180.0, 1314.0, 197.0, 1320.0, 199.0, 1321.0, 203.0, 1316.0, 208.0, 1318.0, 215.0, 1326.0, 227.0, 1329.0, 233.0, 1330.0, 243.0, 1324.0, 250.0, 1327.0, 264.0, 1330.0, 265.0, 1335.0, 281.0, 1318.0, 283.0, 1312.0, 273.0, 1310.0, 261.0, 1307.0, 262.0, 1306.0, 275.0, 1312.0, 283.0, 1301.0, 281.0, 1298.0, 283.0, 1300.0, 287.0, 1298.0, 293.0, 1294.0, 297.0, 1294.0, 307.0]], "capital": [1292.0, 292.0], "configuredProductions": {"2": 5800.0}, "id": 134, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [133, 135, 139, 140, 273, 311], "ownerId": 6, "population": 100000, "productionType": 2}, {"borders": [[1181.0, 178.0, 1186.0, 170.0, 1181.0, 161.0, 1187.0, 159.0, 1193.0, 161.0, 1197.0, 161.0, 1203.0, 157.0, 1209.0, 151.0, 1200.0, 154.0, 1196.0, 152.0, 1185.0, 157.0, 1188.0, 149.0, 1203.0, 143.0, 1208.0, 126.0, 1215.0, 120.0, 1219.0, 101.0, 1226.0, 107.0, 1227.0, 99.0, 1230.0, 100.0, 1234.0, 105.0, 1238.0, 107.0, 1242.0, 112.0, 1242.0, 105.0, 1237.0, 100.0, 1236.0, 89.0, 1243.0, 85.0, 1246.0, 89.0, 1247.0, 100.0, 1249.0, 89.0, 1256.0, 84.0, 1258.0, 89.0, 1255.0, 96.0, 1254.0, 109.0, 1259.0, 104.0, 1266.0, 103.0, 1266.0, 91.0, 1272.0, 84.0, 1271.0, 91.0, 1276.0, 85.0, 1276.0, 76.0, 1280.0, 76.0, 1283.0, 84.0, 1289.0, 86.0, 1289.0, 77.0, 1281.0, 73.0, 1273.0, 63.0, 1279.0, 64.0, 1282.0, 63.0, 1286.0, 68.0, 1288.0, 63.0, 1300.0, 71.0, 1302.0, 78.0, 1305.0, 79.0, 1308.0, 76.0, 1307.0, 71.0, 1317.0, 56.0, 1316.0, 52.0, 1320.0, 53.0, 1325.0, 46.0, 1332.0, 50.0, 1326.0, 41.0, 1324.0, 32.0, 1330.0, 34.0, 1333.0, 29.0, 1336.0, 29.0, 1344.0, 38.0, 1345.0, 33.0, 1352.0, 37.0, 1351.0, 43.0, 1342.0, 50.0, 1337.0, 68.0, 1342.0, 69.0, 1345.0, 62.0, 1356.0, 49.0, 1357.0, 43.0, 1362.0, 34.0, 1366.0, 37.0, 1364.0, 45.0, 1365.0, 56.0, 1373.0, 54.0, 1372.0, 44.0, 1379.0, 38.0, 1376.0, 34.0, 1375.0, 29.0, 1389.0, 24.0, 1389.0, 29.0, 1396.0, 31.0, 1395.0, 38.0, 1386.0, 37.0, 1384.0, 40.0, 1392.0, 42.0, 1393.0, 45.0, 1388.0, 55.0, 1399.0, 54.0, 1396.0, 48.0, 1400.0, 44.0, 1401.0, 37.0, 1409.0, 36.0, 1412.0, 40.0, 1418.0, 40.0, 1429.0, 45.0, 1435.0, 52.0, 1439.0, 53.0, 1439.0, 56.0, 1432.0, 56.0, 1433.0, 61.0, 1428.0, 65.0, 1404.0, 62.0, 1417.0, 75.0, 1415.0, 79.0, 1416.0, 84.0, 1422.0, 80.0, 1424.0, 86.0, 1427.0, 84.0, 1429.0, 75.0, 1440.0, 78.0, 1442.0, 91.0, 1431.0, 93.0, 1428.0, 89.0, 1426.0, 90.0, 1425.0, 96.0, 1418.0, 115.0, 1412.0, 117.0, 1406.0, 105.0, 1408.0, 89.0, 1388.0, 67.0, 1381.0, 66.0, 1375.0, 78.0, 1362.0, 79.0, 1354.0, 86.0, 1352.0, 103.0, 1353.0, 116.0, 1350.0, 123.0, 1355.0, 127.0, 1352.0, 131.0, 1347.0, 130.0, 1343.0, 147.0, 1328.0, 145.0, 1327.0, 141.0, 1321.0, 140.0, 1311.0, 146.0, 1301.0, 142.0, 1292.0, 140.0, 1291.0, 135.0, 1287.0, 136.0, 1281.0, 124.0, 1273.0, 113.0, 1266.0, 119.0, 1270.0, 127.0, 1265.0, 129.0, 1260.0, 122.0, 1256.0, 125.0, 1252.0, 126.0, 1252.0, 137.0, 1247.0, 140.0, 1252.0, 149.0, 1252.0, 154.0, 1243.0, 151.0, 1236.0, 144.0, 1230.0, 148.0, 1223.0, 141.0, 1214.0, 147.0, 1212.0, 163.0, 1210.0, 167.0, 1202.0, 174.0, 1199.0, 170.0, 1192.0, 175.0, 1186.0, 182.0], [1213.0, 97.0, 1214.0, 104.0, 1211.0, 116.0, 1202.0, 119.0, 1198.0, 124.0, 1194.0, 122.0, 1196.0, 112.0, 1193.0, 109.0, 1196.0, 105.0, 1199.0, 105.0, 1199.0, 100.0, 1205.0, 100.0, 1206.0, 95.0], [1190.0, 133.0, 1190.0, 140.0, 1177.0, 153.0, 1172.0, 154.0, 1171.0, 160.0, 1167.0, 160.0, 1167.0, 156.0, 1157.0, 164.0, 1162.0, 155.0, 1161.0, 152.0, 1166.0, 144.0, 1168.0, 138.0, 1174.0, 129.0, 1175.0, 135.0, 1177.0, 139.0, 1177.0, 144.0, 1181.0, 139.0, 1183.0, 132.0], [1170.0, 117.0, 1179.0, 107.0, 1182.0, 110.0, 1179.0, 115.0, 1179.0, 122.0, 1169.0, 126.0], [1163.0, 125.0, 1164.0, 133.0, 1162.0, 141.0, 1156.0, 148.0, 1152.0, 145.0, 1153.0, 142.0, 1149.0, 142.0, 1144.0, 145.0, 1144.0, 138.0, 1160.0, 133.0], [1144.0, 157.0, 1148.0, 157.0, 1152.0, 154.0, 1151.0, 162.0, 1143.0, 167.0], [1218.0, 91.0, 1225.0, 85.0, 1228.0, 87.0, 1219.0, 98.0], [1237.0, 84.0, 1232.0, 83.0, 1230.0, 79.0, 1237.0, 74.0, 1241.0, 77.0], [1348.0, 30.0, 1342.0, 24.0, 1347.0, 22.0, 1347.0, 17.0, 1351.0, 18.0, 1351.0, 23.0, 1357.0, 27.0], [1286.0, 50.0, 1296.0, 48.0, 1295.0, 42.0, 1299.0, 45.0, 1303.0, 39.0, 1309.0, 33.0, 1310.0, 36.0, 1298.0, 54.0, 1287.0, 54.0]], "capital": [1241.0, 93.0], "configuredProductions": {"1": 2900.0}, "id": 135, "locationType": "province", "name": "Tromsoe", "neighbourIds": [134, 139, 140, 160, 311, 312, 315], "ownerId": 6, "population": 100000, "productionType": 1}, {"borders": [[927.0, 646.0, 922.0, 651.0, 897.0, 652.0, 898.0, 647.0, 894.0, 642.0, 890.0, 646.0, 887.0, 642.0, 891.0, 637.0, 887.0, 636.0, 876.0, 630.0, 874.0, 622.0, 869.0, 617.0, 866.0, 617.0, 867.0, 608.0, 867.0, 592.0, 870.0, 591.0, 872.0, 597.0, 876.0, 595.0, 882.0, 599.0, 880.0, 587.0, 888.0, 583.0, 889.0, 578.0, 885.0, 577.0, 882.0, 570.0, 879.0, 575.0, 875.0, 578.0, 869.0, 573.0, 869.0, 565.0, 877.0, 558.0, 880.0, 562.0, 885.0, 560.0, 890.0, 554.0, 881.0, 556.0, 880.0, 554.0, 885.0, 545.0, 904.0, 528.0, 909.0, 530.0, 908.0, 539.0, 910.0, 542.0, 914.0, 532.0, 922.0, 525.0, 930.0, 526.0, 953.0, 516.0, 962.0, 511.0, 966.0, 501.0, 975.0, 490.0, 980.0, 496.0, 988.0, 501.0, 994.0, 503.0, 987.0, 515.0, 956.0, 529.0, 954.0, 538.0, 953.0, 554.0, 942.0, 566.0, 936.0, 591.0, 936.0, 597.0, 942.0, 604.0, 951.0, 615.0, 952.0, 629.0, 944.0, 639.0, 939.0, 642.0, 930.0, 648.0]], "capital": [882.0, 625.0], "configuredProductions": {"0": 2900.0}, "id": 136, "locationType": "province", "name": "Egersund", "neighbourIds": [137, 138, 253, 254, 255, 285, 334], "ownerId": 6, "population": 100000}, {"borders": [[1027.0, 609.0, 1017.0, 607.0, 1007.0, 595.0, 1011.0, 587.0, 1008.0, 579.0, 1008.0, 569.0, 1004.0, 571.0, 1004.0, 579.0, 999.0, 585.0, 998.0, 591.0, 1002.0, 595.0, 1002.0, 601.0, 998.0, 602.0, 997.0, 606.0, 992.0, 607.0, 991.0, 611.0, 986.0, 611.0, 988.0, 607.0, 986.0, 603.0, 981.0, 602.0, 982.0, 605.0, 981.0, 609.0, 972.0, 607.0, 969.0, 610.0, 976.0, 611.0, 973.0, 617.0, 963.0, 619.0, 952.0, 629.0, 951.0, 615.0, 942.0, 604.0, 936.0, 597.0, 936.0, 591.0, 942.0, 566.0, 953.0, 554.0, 954.0, 538.0, 956.0, 529.0, 987.0, 515.0, 994.0, 503.0, 1005.0, 491.0, 1009.0, 483.0, 1016.0, 477.0, 1032.0, 478.0, 1046.0, 467.0, 1050.0, 461.0, 1050.0, 467.0, 1048.0, 472.0, 1048.0, 477.0, 1054.0, 471.0, 1055.0, 465.0, 1062.0, 467.0, 1059.0, 474.0, 1060.0, 482.0, 1070.0, 500.0, 1068.0, 520.0, 1054.0, 521.0, 1060.0, 540.0, 1059.0, 547.0, 1059.0, 559.0, 1056.0, 570.0, 1042.0, 574.0, 1040.0, 583.0, 1035.0, 589.0, 1034.0, 607.0]], "capital": [1012.0, 565.0], "configuredProductions": {"3": 2900.0}, "id": 137, "locationType": "province", "name": "Christiania", "neighbourIds": [129, 131, 136, 138, 255], "ownerId": 6, "population": 100000, "productionType": 3}, {"borders": [[922.0, 525.0, 909.0, 524.0, 904.0, 528.0, 883.0, 540.0, 884.0, 532.0, 880.0, 535.0, 877.0, 532.0, 879.0, 523.0, 886.0, 521.0, 890.0, 519.0, 892.0, 511.0, 883.0, 518.0, 880.0, 514.0, 880.0, 504.0, 886.0, 509.0, 889.0, 504.0, 884.0, 495.0, 887.0, 489.0, 915.0, 495.0, 918.0, 492.0, 925.0, 493.0, 929.0, 500.0, 930.0, 495.0, 937.0, 494.0, 940.0, 491.0, 894.0, 486.0, 888.0, 483.0, 889.0, 474.0, 900.0, 474.0, 896.0, 467.0, 899.0, 464.0, 894.0, 461.0, 889.0, 454.0, 895.0, 456.0, 899.0, 452.0, 906.0, 452.0, 913.0, 457.0, 923.0, 457.0, 916.0, 452.0, 908.0, 448.0, 902.0, 448.0, 897.0, 445.0, 902.0, 444.0, 903.0, 440.0, 900.0, 436.0, 901.0, 434.0, 918.0, 442.0, 917.0, 432.0, 927.0, 430.0, 932.0, 430.0, 940.0, 436.0, 943.0, 435.0, 938.0, 428.0, 934.0, 425.0, 930.0, 420.0, 925.0, 418.0, 928.0, 414.0, 946.0, 418.0, 956.0, 428.0, 960.0, 427.0, 954.0, 416.0, 949.0, 416.0, 944.0, 404.0, 953.0, 403.0, 958.0, 406.0, 966.0, 407.0, 976.0, 419.0, 975.0, 409.0, 970.0, 406.0, 970.0, 402.0, 976.0, 402.0, 983.0, 415.0, 987.0, 408.0, 981.0, 405.0, 981.0, 399.0, 988.0, 399.0, 990.0, 390.0, 997.0, 386.0, 1006.0, 392.0, 1011.0, 402.0, 1020.0, 416.0, 1023.0, 427.0, 1028.0, 425.0, 1026.0, 418.0, 1028.0, 415.0, 1037.0, 426.0, 1050.0, 428.0, 1057.0, 430.0, 1061.0, 437.0, 1062.0, 443.0, 1067.0, 454.0, 1062.0, 467.0, 1055.0, 465.0, 1054.0, 471.0, 1048.0, 477.0, 1048.0, 472.0, 1050.0, 467.0, 1050.0, 461.0, 1046.0, 467.0, 1032.0, 478.0, 1016.0, 477.0, 1009.0, 483.0, 1005.0, 491.0, 994.0, 503.0, 988.0, 501.0, 980.0, 496.0, 975.0, 490.0, 966.0, 501.0, 962.0, 511.0, 953.0, 516.0, 930.0, 526.0]], "capital": [885.0, 526.0], "configuredProductions": {"5": 2900.0}, "id": 138, "locationType": "province", "name": "Bergen", "neighbourIds": [131, 136, 137, 139, 285, 286], "ownerId": 6, "population": 100000, "productionType": 5}, {"borders": [[1023.0, 393.0, 1028.0, 388.0, 1041.0, 391.0, 1057.0, 369.0, 1052.0, 370.0, 1054.0, 362.0, 1042.0, 368.0, 1046.0, 372.0, 1045.0, 375.0, 1029.0, 385.0, 1025.0, 382.0, 1024.0, 378.0, 1020.0, 373.0, 1021.0, 370.0, 1026.0, 369.0, 1031.0, 364.0, 1030.0, 361.0, 1034.0, 356.0, 1038.0, 355.0, 1043.0, 347.0, 1043.0, 339.0, 1054.0, 341.0, 1060.0, 349.0, 1064.0, 347.0, 1065.0, 333.0, 1076.0, 325.0, 1082.0, 322.0, 1083.0, 319.0, 1077.0, 320.0, 1069.0, 325.0, 1063.0, 324.0, 1074.0, 311.0, 1083.0, 308.0, 1082.0, 313.0, 1085.0, 314.0, 1094.0, 304.0, 1085.0, 303.0, 1088.0, 290.0, 1094.0, 298.0, 1096.0, 296.0, 1092.0, 286.0, 1097.0, 284.0, 1099.0, 276.0, 1105.0, 276.0, 1106.0, 266.0, 1111.0, 264.0, 1108.0, 251.0, 1110.0, 244.0, 1114.0, 242.0, 1114.0, 238.0, 1121.0, 237.0, 1124.0, 234.0, 1122.0, 226.0, 1138.0, 221.0, 1149.0, 216.0, 1162.0, 220.0, 1166.0, 213.0, 1157.0, 212.0, 1146.0, 213.0, 1144.0, 211.0, 1148.0, 202.0, 1148.0, 200.0, 1153.0, 196.0, 1166.0, 202.0, 1158.0, 193.0, 1161.0, 189.0, 1171.0, 192.0, 1167.0, 185.0, 1161.0, 184.0, 1155.0, 191.0, 1150.0, 188.0, 1159.0, 181.0, 1174.0, 180.0, 1173.0, 174.0, 1168.0, 173.0, 1166.0, 167.0, 1177.0, 165.0, 1179.0, 174.0, 1181.0, 178.0, 1186.0, 182.0, 1175.0, 195.0, 1174.0, 198.0, 1174.0, 221.0, 1174.0, 224.0, 1158.0, 240.0, 1156.0, 253.0, 1150.0, 261.0, 1137.0, 261.0, 1132.0, 267.0, 1131.0, 299.0, 1129.0, 322.0, 1124.0, 329.0, 1118.0, 345.0, 1110.0, 352.0, 1118.0, 355.0, 1114.0, 361.0, 1114.0, 367.0, 1109.0, 370.0, 1102.0, 363.0, 1092.0, 363.0, 1077.0, 371.0, 1072.0, 380.0, 1067.0, 392.0, 1071.0, 399.0, 1068.0, 407.0, 1072.0, 421.0, 1067.0, 426.0, 1068.0, 439.0, 1062.0, 443.0, 1061.0, 437.0, 1057.0, 430.0, 1050.0, 428.0, 1037.0, 426.0, 1028.0, 415.0, 1026.0, 418.0, 1028.0, 425.0, 1023.0, 427.0, 1020.0, 416.0, 1011.0, 402.0, 1006.0, 392.0, 1009.0, 389.0, 1003.0, 383.0, 1010.0, 383.0, 1014.0, 380.0]], "capital": [1034.0, 398.0], "configuredProductions": {"1": 2900.0}, "id": 139, "locationType": "province", "name": "Trondhjem", "neighbourIds": [131, 132, 133, 134, 135, 138, 286, 288, 311], "ownerId": 6, "population": 100000, "productionType": 1}, {"borders": [[1343.0, 282.0, 1335.0, 281.0, 1330.0, 265.0, 1327.0, 264.0, 1324.0, 250.0, 1330.0, 243.0, 1329.0, 233.0, 1326.0, 227.0, 1318.0, 215.0, 1316.0, 208.0, 1321.0, 203.0, 1320.0, 199.0, 1314.0, 197.0, 1317.0, 180.0, 1305.0, 173.0, 1299.0, 162.0, 1285.0, 155.0, 1272.0, 142.0, 1266.0, 138.0, 1256.0, 125.0, 1260.0, 122.0, 1265.0, 129.0, 1270.0, 127.0, 1266.0, 119.0, 1273.0, 113.0, 1281.0, 124.0, 1287.0, 136.0, 1291.0, 135.0, 1292.0, 140.0, 1301.0, 142.0, 1311.0, 146.0, 1321.0, 140.0, 1327.0, 141.0, 1328.0, 145.0, 1343.0, 147.0, 1347.0, 130.0, 1352.0, 131.0, 1355.0, 127.0, 1350.0, 123.0, 1353.0, 116.0, 1352.0, 103.0, 1354.0, 86.0, 1362.0, 79.0, 1375.0, 78.0, 1381.0, 66.0, 1388.0, 67.0, 1408.0, 89.0, 1406.0, 105.0, 1412.0, 117.0, 1407.0, 123.0, 1400.0, 119.0, 1404.0, 110.0, 1400.0, 104.0, 1392.0, 111.0, 1392.0, 115.0, 1380.0, 127.0, 1380.0, 131.0, 1386.0, 130.0, 1387.0, 135.0, 1390.0, 137.0, 1394.0, 131.0, 1398.0, 134.0, 1398.0, 127.0, 1406.0, 124.0, 1402.0, 139.0, 1404.0, 153.0, 1408.0, 158.0, 1418.0, 159.0, 1424.0, 165.0, 1429.0, 173.0, 1445.0, 183.0, 1460.0, 198.0, 1456.0, 211.0, 1453.0, 218.0, 1460.0, 225.0, 1456.0, 229.0, 1459.0, 243.0, 1456.0, 249.0, 1464.0, 257.0, 1468.0, 265.0, 1462.0, 274.0, 1454.0, 274.0, 1452.0, 279.0, 1458.0, 295.0, 1454.0, 298.0, 1452.0, 303.0, 1459.0, 304.0, 1460.0, 309.0, 1456.0, 314.0, 1443.0, 326.0, 1436.0, 342.0, 1428.0, 349.0, 1424.0, 353.0, 1419.0, 350.0, 1418.0, 355.0, 1410.0, 355.0, 1408.0, 347.0, 1398.0, 348.0, 1388.0, 353.0, 1375.0, 350.0, 1366.0, 343.0, 1359.0, 337.0, 1355.0, 332.0, 1360.0, 329.0, 1367.0, 329.0, 1367.0, 321.0, 1363.0, 314.0, 1366.0, 305.0, 1364.0, 297.0, 1358.0, 295.0]], "capital": [1341.0, 277.0], "configuredProductions": {"3": 2900.0}, "id": 140, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [134, 135, 141, 142, 160, 273], "ownerId": 15, "population": 100000, "productionType": 3}, {"borders": [[1410.0, 365.0, 1407.0, 375.0, 1411.0, 388.0, 1416.0, 403.0, 1416.0, 410.0, 1412.0, 412.0, 1400.0, 402.0, 1402.0, 406.0, 1402.0, 411.0, 1397.0, 411.0, 1398.0, 415.0, 1387.0, 417.0, 1379.0, 412.0, 1374.0, 416.0, 1371.0, 417.0, 1368.0, 408.0, 1364.0, 409.0, 1366.0, 416.0, 1366.0, 423.0, 1369.0, 424.0, 1370.0, 419.0, 1374.0, 417.0, 1375.0, 422.0, 1382.0, 421.0, 1388.0, 429.0, 1384.0, 430.0, 1384.0, 443.0, 1390.0, 438.0, 1390.0, 454.0, 1386.0, 457.0, 1388.0, 462.0, 1384.0, 467.0, 1384.0, 473.0, 1382.0, 473.0, 1376.0, 485.0, 1378.0, 491.0, 1376.0, 497.0, 1360.0, 503.0, 1358.0, 505.0, 1349.0, 505.0, 1350.0, 503.0, 1343.0, 497.0, 1344.0, 486.0, 1347.0, 479.0, 1352.0, 477.0, 1352.0, 472.0, 1356.0, 470.0, 1356.0, 468.0, 1352.0, 467.0, 1350.0, 462.0, 1348.0, 462.0, 1344.0, 465.0, 1340.0, 461.0, 1340.0, 467.0, 1348.0, 470.0, 1347.0, 473.0, 1340.0, 477.0, 1336.0, 492.0, 1340.0, 487.0, 1339.0, 497.0, 1338.0, 500.0, 1332.0, 503.0, 1327.0, 505.0, 1322.0, 507.0, 1320.0, 513.0, 1316.0, 513.0, 1310.0, 508.0, 1306.0, 511.0, 1297.0, 503.0, 1290.0, 493.0, 1290.0, 484.0, 1288.0, 479.0, 1284.0, 477.0, 1282.0, 471.0, 1286.0, 467.0, 1282.0, 462.0, 1281.0, 459.0, 1278.0, 455.0, 1278.0, 448.0, 1276.0, 445.0, 1278.0, 439.0, 1278.0, 432.0, 1283.0, 430.0, 1282.0, 425.0, 1286.0, 423.0, 1284.0, 413.0, 1298.0, 415.0, 1300.0, 412.0, 1299.0, 405.0, 1308.0, 400.0, 1309.0, 392.0, 1312.0, 391.0, 1316.0, 395.0, 1321.0, 386.0, 1326.0, 383.0, 1326.0, 372.0, 1332.0, 371.0, 1341.0, 353.0, 1348.0, 341.0, 1355.0, 332.0, 1359.0, 337.0, 1366.0, 343.0, 1375.0, 350.0, 1388.0, 353.0, 1398.0, 348.0, 1398.0, 357.0]], "capital": [1290.0, 419.0], "configuredProductions": {"3": 2900.0}, "id": 141, "locationType": "province", "name": "<PERSON><PERSON>", "neighbourIds": [140, 142, 143, 271, 272, 273], "ownerId": 15, "population": 100000, "productionType": 3}, {"borders": [[1424.0, 425.0, 1428.0, 426.0, 1429.0, 442.0, 1439.0, 453.0, 1442.0, 451.0, 1438.0, 447.0, 1435.0, 437.0, 1434.0, 430.0, 1444.0, 440.0, 1445.0, 447.0, 1448.0, 440.0, 1455.0, 445.0, 1460.0, 445.0, 1457.0, 441.0, 1450.0, 437.0, 1445.0, 429.0, 1439.0, 428.0, 1445.0, 425.0, 1435.0, 425.0, 1435.0, 417.0, 1440.0, 420.0, 1439.0, 419.0, 1445.0, 413.0, 1444.0, 411.0, 1439.0, 414.0, 1432.0, 413.0, 1432.0, 410.0, 1426.0, 419.0, 1420.0, 410.0, 1420.0, 403.0, 1418.0, 396.0, 1411.0, 388.0, 1407.0, 375.0, 1410.0, 365.0, 1414.0, 362.0, 1418.0, 355.0, 1424.0, 353.0, 1428.0, 349.0, 1436.0, 342.0, 1443.0, 326.0, 1456.0, 314.0, 1456.0, 318.0, 1458.0, 322.0, 1463.0, 327.0, 1472.0, 327.0, 1472.0, 342.0, 1474.0, 348.0, 1478.0, 345.0, 1486.0, 361.0, 1472.0, 372.0, 1484.0, 383.0, 1493.0, 385.0, 1500.0, 396.0, 1490.0, 407.0, 1486.0, 408.0, 1483.0, 399.0, 1476.0, 393.0, 1472.0, 397.0, 1464.0, 389.0, 1456.0, 388.0, 1456.0, 390.0, 1454.0, 394.0, 1462.0, 398.0, 1471.0, 407.0, 1474.0, 402.0, 1482.0, 410.0, 1480.0, 415.0, 1480.0, 424.0, 1483.0, 425.0, 1480.0, 429.0, 1478.0, 431.0, 1475.0, 435.0, 1478.0, 444.0, 1468.0, 435.0, 1468.0, 453.0, 1460.0, 455.0, 1458.0, 451.0, 1453.0, 451.0, 1451.0, 456.0, 1437.0, 456.0, 1439.0, 463.0, 1436.0, 473.0, 1437.0, 487.0, 1418.0, 485.0, 1407.0, 470.0, 1404.0, 473.0, 1391.0, 465.0, 1390.0, 454.0, 1390.0, 438.0, 1392.0, 432.0, 1396.0, 433.0, 1390.0, 425.0, 1391.0, 421.0, 1387.0, 417.0, 1398.0, 415.0, 1397.0, 411.0, 1402.0, 411.0, 1401.0, 422.0, 1406.0, 433.0, 1412.0, 435.0, 1409.0, 429.0, 1414.0, 427.0, 1410.0, 423.0, 1412.0, 419.0, 1412.0, 412.0, 1416.0, 410.0]], "capital": [1424.0, 429.0], "configuredProductions": {"2": 2900.0}, "id": 142, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [140, 141, 143, 144, 160, 273, 320], "ownerId": 15, "population": 100000, "productionType": 2}, {"borders": [[1288.0, 520.0, 1291.0, 515.0, 1288.0, 511.0, 1289.0, 505.0, 1288.0, 499.0, 1290.0, 493.0, 1297.0, 503.0, 1306.0, 511.0, 1310.0, 508.0, 1316.0, 513.0, 1320.0, 513.0, 1322.0, 507.0, 1327.0, 505.0, 1332.0, 503.0, 1334.0, 507.0, 1342.0, 511.0, 1340.0, 504.0, 1343.0, 497.0, 1350.0, 503.0, 1349.0, 505.0, 1348.0, 511.0, 1343.0, 515.0, 1355.0, 520.0, 1358.0, 517.0, 1350.0, 514.0, 1353.0, 511.0, 1359.0, 510.0, 1358.0, 505.0, 1360.0, 503.0, 1376.0, 497.0, 1381.0, 507.0, 1382.0, 512.0, 1381.0, 519.0, 1384.0, 517.0, 1388.0, 521.0, 1389.0, 512.0, 1387.0, 509.0, 1386.0, 499.0, 1390.0, 495.0, 1391.0, 485.0, 1387.0, 485.0, 1386.0, 477.0, 1388.0, 474.0, 1391.0, 465.0, 1404.0, 473.0, 1405.0, 478.0, 1412.0, 485.0, 1418.0, 485.0, 1437.0, 487.0, 1436.0, 473.0, 1439.0, 463.0, 1446.0, 459.0, 1452.0, 465.0, 1458.0, 469.0, 1460.0, 473.0, 1454.0, 473.0, 1456.0, 481.0, 1451.0, 491.0, 1442.0, 486.0, 1444.0, 495.0, 1436.0, 499.0, 1442.0, 503.0, 1446.0, 501.0, 1450.0, 505.0, 1455.0, 504.0, 1454.0, 507.0, 1445.0, 505.0, 1443.0, 509.0, 1441.0, 524.0, 1442.0, 535.0, 1445.0, 539.0, 1441.0, 545.0, 1434.0, 541.0, 1426.0, 540.0, 1426.0, 546.0, 1414.0, 548.0, 1410.0, 551.0, 1402.0, 548.0, 1400.0, 549.0, 1402.0, 553.0, 1400.0, 556.0, 1396.0, 555.0, 1388.0, 560.0, 1382.0, 560.0, 1378.0, 566.0, 1371.0, 563.0, 1366.0, 567.0, 1364.0, 571.0, 1359.0, 573.0, 1360.0, 567.0, 1349.0, 569.0, 1346.0, 576.0, 1340.0, 574.0, 1336.0, 576.0, 1334.0, 581.0, 1331.0, 584.0, 1328.0, 581.0, 1330.0, 577.0, 1324.0, 575.0, 1329.0, 573.0, 1327.0, 568.0, 1328.0, 558.0, 1318.0, 561.0, 1314.0, 559.0, 1319.0, 555.0, 1309.0, 555.0, 1302.0, 548.0, 1296.0, 548.0, 1296.0, 543.0, 1290.0, 545.0, 1286.0, 541.0, 1286.0, 535.0, 1284.0, 531.0, 1286.0, 526.0, 1282.0, 519.0], [1322.0, 572.0, 1318.0, 570.0, 1313.0, 573.0, 1313.0, 567.0, 1322.0, 563.0]], "capital": [1371.0, 556.0], "configuredProductions": {"0": 2900.0}, "id": 143, "locationType": "province", "name": "<PERSON><PERSON>ing<PERSON><PERSON>", "neighbourIds": [141, 142, 144, 266, 267, 268, 271], "ownerId": 15, "population": 100000}, {"borders": [[1464.0, 531.0, 1458.0, 535.0, 1454.0, 541.0, 1445.0, 539.0, 1442.0, 535.0, 1441.0, 524.0, 1443.0, 509.0, 1454.0, 513.0, 1466.0, 505.0, 1468.0, 501.0, 1468.0, 500.0, 1462.0, 500.0, 1462.0, 497.0, 1454.0, 497.0, 1451.0, 491.0, 1456.0, 481.0, 1460.0, 479.0, 1466.0, 481.0, 1470.0, 484.0, 1476.0, 481.0, 1479.0, 478.0, 1478.0, 472.0, 1480.0, 469.0, 1479.0, 461.0, 1474.0, 465.0, 1470.0, 479.0, 1466.0, 478.0, 1458.0, 463.0, 1461.0, 459.0, 1470.0, 457.0, 1476.0, 458.0, 1476.0, 454.0, 1480.0, 454.0, 1486.0, 465.0, 1486.0, 469.0, 1488.0, 473.0, 1490.0, 469.0, 1494.0, 475.0, 1493.0, 465.0, 1488.0, 461.0, 1488.0, 453.0, 1479.0, 450.0, 1482.0, 447.0, 1484.0, 438.0, 1478.0, 431.0, 1480.0, 429.0, 1483.0, 425.0, 1480.0, 424.0, 1480.0, 415.0, 1482.0, 410.0, 1490.0, 417.0, 1490.0, 413.0, 1486.0, 408.0, 1490.0, 407.0, 1500.0, 396.0, 1508.0, 397.0, 1512.0, 406.0, 1516.0, 405.0, 1520.0, 410.0, 1526.0, 410.0, 1531.0, 415.0, 1542.0, 415.0, 1547.0, 421.0, 1550.0, 431.0, 1544.0, 441.0, 1546.0, 445.0, 1554.0, 445.0, 1558.0, 462.0, 1553.0, 461.0, 1547.0, 452.0, 1534.0, 460.0, 1536.0, 469.0, 1542.0, 471.0, 1545.0, 481.0, 1542.0, 483.0, 1543.0, 487.0, 1534.0, 481.0, 1526.0, 474.0, 1522.0, 477.0, 1520.0, 472.0, 1510.0, 470.0, 1508.0, 483.0, 1502.0, 489.0, 1502.0, 496.0, 1495.0, 501.0, 1496.0, 505.0, 1500.0, 508.0, 1494.0, 516.0, 1494.0, 522.0, 1496.0, 525.0, 1490.0, 525.0, 1482.0, 522.0, 1489.0, 527.0, 1486.0, 533.0, 1490.0, 535.0, 1493.0, 529.0, 1500.0, 528.0, 1510.0, 529.0, 1499.0, 523.0, 1500.0, 517.0, 1506.0, 509.0, 1514.0, 518.0, 1517.0, 531.0, 1512.0, 535.0, 1507.0, 535.0, 1504.0, 545.0, 1496.0, 545.0, 1496.0, 554.0, 1477.0, 551.0, 1466.0, 547.0, 1463.0, 540.0, 1470.0, 539.0, 1467.0, 533.0, 1470.0, 529.0, 1467.0, 526.0]], "capital": [1472.0, 525.0], "configuredProductions": {"1": 2900.0}, "id": 144, "locationType": "province", "name": "Vib<PERSON>g", "neighbourIds": [142, 143, 155, 160, 268, 320], "ownerId": 15, "population": 100000, "productionType": 1}, {"borders": [[1218.0, 936.0, 1221.0, 935.0, 1233.0, 944.0, 1240.0, 944.0, 1250.0, 952.0, 1252.0, 963.0, 1252.0, 980.0, 1269.0, 997.0, 1280.0, 1000.0, 1293.0, 998.0, 1302.0, 1000.0, 1307.0, 1005.0, 1307.0, 1015.0, 1306.0, 1023.0, 1306.0, 1037.0, 1303.0, 1044.0, 1291.0, 1052.0, 1268.0, 1065.0, 1252.0, 1059.0, 1222.0, 1060.0, 1216.0, 1057.0, 1218.0, 1050.0, 1206.0, 1032.0, 1200.0, 1031.0, 1193.0, 1023.0, 1184.0, 1017.0, 1188.0, 1007.0, 1184.0, 1004.0, 1184.0, 990.0, 1176.0, 979.0, 1170.0, 968.0, 1181.0, 953.0, 1193.0, 951.0, 1199.0, 945.0, 1207.0, 944.0]], "capital": [1237.0, 983.0], "configuredProductions": {"2": 2900.0}, "id": 145, "locationType": "province", "name": "Lodz", "neighbourIds": [0, 3, 5, 16, 146, 148], "ownerId": 13, "population": 100000, "productionType": 2}, {"borders": [[1294.0, 993.0, 1293.0, 998.0, 1280.0, 1000.0, 1269.0, 997.0, 1252.0, 980.0, 1252.0, 963.0, 1250.0, 952.0, 1240.0, 944.0, 1233.0, 944.0, 1221.0, 935.0, 1223.0, 924.0, 1232.0, 924.0, 1238.0, 918.0, 1242.0, 923.0, 1252.0, 915.0, 1258.0, 919.0, 1262.0, 914.0, 1264.0, 905.0, 1274.0, 913.0, 1294.0, 901.0, 1302.0, 903.0, 1304.0, 896.0, 1310.0, 895.0, 1318.0, 909.0, 1323.0, 901.0, 1338.0, 902.0, 1336.0, 911.0, 1336.0, 918.0, 1353.0, 926.0, 1362.0, 920.0, 1371.0, 922.0, 1372.0, 933.0, 1379.0, 938.0, 1376.0, 963.0, 1369.0, 970.0, 1350.0, 963.0, 1337.0, 954.0, 1322.0, 953.0, 1317.0, 957.0, 1327.0, 969.0, 1326.0, 975.0, 1305.0, 974.0, 1299.0, 982.0]], "capital": [1274.0, 958.0], "configuredProductions": {"4": 2900.0}, "id": 146, "locationType": "province", "name": "Warsaw", "neighbourIds": [0, 145, 147, 148, 151, 153], "ownerId": 13, "population": 100000, "productionType": 4}, {"borders": [[1323.0, 901.0, 1318.0, 909.0, 1310.0, 895.0, 1319.0, 891.0, 1334.0, 886.0, 1327.0, 870.0, 1325.0, 861.0, 1334.0, 859.0, 1330.0, 844.0, 1336.0, 835.0, 1333.0, 823.0, 1325.0, 816.0, 1328.0, 813.0, 1326.0, 809.0, 1339.0, 801.0, 1357.0, 799.0, 1369.0, 807.0, 1392.0, 807.0, 1397.0, 812.0, 1401.0, 821.0, 1392.0, 837.0, 1389.0, 848.0, 1388.0, 855.0, 1386.0, 860.0, 1388.0, 866.0, 1394.0, 874.0, 1395.0, 880.0, 1387.0, 880.0, 1376.0, 883.0, 1367.0, 880.0, 1364.0, 877.0, 1359.0, 879.0, 1354.0, 891.0, 1349.0, 899.0, 1338.0, 902.0]], "capital": [1365.0, 828.0], "configuredProductions": {"0": 2900.0}, "id": 147, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [0, 146, 153, 156], "ownerId": 13, "population": 100000}, {"borders": [[1381.0, 1049.0, 1368.0, 1049.0, 1370.0, 1060.0, 1354.0, 1051.0, 1343.0, 1057.0, 1332.0, 1055.0, 1334.0, 1050.0, 1323.0, 1053.0, 1319.0, 1050.0, 1322.0, 1043.0, 1317.0, 1034.0, 1313.0, 1040.0, 1306.0, 1037.0, 1306.0, 1023.0, 1307.0, 1015.0, 1307.0, 1005.0, 1302.0, 1000.0, 1293.0, 998.0, 1294.0, 993.0, 1299.0, 982.0, 1305.0, 974.0, 1326.0, 975.0, 1327.0, 969.0, 1317.0, 957.0, 1322.0, 953.0, 1337.0, 954.0, 1350.0, 963.0, 1369.0, 970.0, 1374.0, 984.0, 1384.0, 998.0, 1392.0, 1010.0, 1391.0, 1013.0, 1380.0, 1010.0, 1379.0, 1015.0, 1383.0, 1018.0, 1392.0, 1028.0, 1390.0, 1040.0]], "capital": [1332.0, 1008.0], "configuredProductions": {"6": 2900.0}, "id": 148, "locationType": "province", "name": "Lublin", "neighbourIds": [15, 16, 145, 146, 151], "ownerId": 13, "population": 100000, "productionType": 6}, {"borders": [[1595.0, 1279.0, 1594.0, 1285.0, 1588.0, 1279.0, 1581.0, 1283.0, 1579.0, 1281.0, 1569.0, 1288.0, 1570.0, 1291.0, 1558.0, 1291.0, 1559.0, 1297.0, 1548.0, 1296.0, 1538.0, 1289.0, 1534.0, 1279.0, 1534.0, 1269.0, 1533.0, 1259.0, 1532.0, 1249.0, 1532.0, 1231.0, 1528.0, 1221.0, 1526.0, 1215.0, 1522.0, 1212.0, 1512.0, 1210.0, 1509.0, 1203.0, 1503.0, 1194.0, 1493.0, 1182.0, 1492.0, 1175.0, 1488.0, 1169.0, 1488.0, 1165.0, 1484.0, 1162.0, 1480.0, 1156.0, 1472.0, 1155.0, 1468.0, 1159.0, 1464.0, 1159.0, 1456.0, 1151.0, 1453.0, 1135.0, 1464.0, 1134.0, 1467.0, 1126.0, 1472.0, 1118.0, 1479.0, 1111.0, 1489.0, 1099.0, 1497.0, 1086.0, 1504.0, 1067.0, 1512.0, 1057.0, 1523.0, 1057.0, 1530.0, 1060.0, 1538.0, 1067.0, 1546.0, 1071.0, 1563.0, 1073.0, 1576.0, 1067.0, 1584.0, 1056.0, 1604.0, 1059.0, 1606.0, 1051.0, 1613.0, 1052.0, 1614.0, 1046.0, 1626.0, 1052.0, 1632.0, 1062.0, 1640.0, 1061.0, 1644.0, 1065.0, 1651.0, 1066.0, 1657.0, 1072.0, 1664.0, 1070.0, 1669.0, 1075.0, 1686.0, 1076.0, 1687.0, 1076.0, 1701.0, 1082.0, 1708.0, 1080.0, 1712.0, 1085.0, 1718.0, 1084.0, 1719.0, 1089.0, 1738.0, 1099.0, 1753.0, 1096.0, 1757.0, 1096.0, 1764.0, 1105.0, 1761.0, 1117.0, 1764.0, 1128.0, 1761.0, 1132.0, 1764.0, 1135.0, 1763.0, 1141.0, 1766.0, 1142.0, 1761.0, 1146.0, 1752.0, 1144.0, 1743.0, 1150.0, 1737.0, 1147.0, 1734.0, 1153.0, 1736.0, 1154.0, 1726.0, 1163.0, 1727.0, 1170.0, 1722.0, 1176.0, 1718.0, 1177.0, 1722.0, 1185.0, 1711.0, 1193.0, 1694.0, 1198.0, 1694.0, 1201.0, 1686.0, 1204.0, 1676.0, 1212.0, 1677.0, 1213.0, 1668.0, 1212.0, 1666.0, 1208.0, 1664.0, 1209.0, 1662.0, 1205.0, 1665.0, 1203.0, 1664.0, 1197.0, 1659.0, 1191.0, 1657.0, 1195.0, 1660.0, 1199.0, 1659.0, 1210.0, 1658.0, 1212.0, 1653.0, 1211.0, 1652.0, 1207.0, 1648.0, 1208.0, 1648.0, 1214.0, 1635.0, 1214.0, 1626.0, 1222.0, 1624.0, 1226.0, 1625.0, 1235.0, 1616.0, 1243.0, 1614.0, 1243.0, 1602.0, 1233.0, 1604.0, 1240.0, 1612.0, 1249.0, 1611.0, 1254.0, 1612.0, 1260.0, 1606.0, 1262.0, 1605.0, 1257.0, 1599.0, 1266.0, 1604.0, 1267.0, 1596.0, 1276.0]], "capital": [1547.0, 1205.0], "configuredProductions": {"0": 2900.0}, "id": 149, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [15, 50, 51, 52, 150, 151, 152, 162], "ownerId": 5, "population": 100000}, {"borders": [[1676.0, 1212.0, 1686.0, 1204.0, 1694.0, 1201.0, 1694.0, 1198.0, 1711.0, 1193.0, 1722.0, 1185.0, 1718.0, 1177.0, 1722.0, 1176.0, 1727.0, 1170.0, 1726.0, 1163.0, 1736.0, 1154.0, 1734.0, 1153.0, 1737.0, 1147.0, 1743.0, 1150.0, 1752.0, 1144.0, 1761.0, 1146.0, 1766.0, 1142.0, 1763.0, 1141.0, 1764.0, 1135.0, 1761.0, 1132.0, 1764.0, 1128.0, 1761.0, 1117.0, 1764.0, 1105.0, 1757.0, 1096.0, 1753.0, 1096.0, 1738.0, 1099.0, 1719.0, 1089.0, 1729.0, 1089.0, 1732.0, 1086.0, 1740.0, 1081.0, 1758.0, 1080.0, 1772.0, 1078.0, 1786.0, 1075.0, 1798.0, 1072.0, 1815.0, 1071.0, 1825.0, 1077.0, 1840.0, 1077.0, 1850.0, 1072.0, 1861.0, 1059.0, 1866.0, 1055.0, 1882.0, 1053.0, 1882.0, 1139.0, 1871.0, 1146.0, 1865.0, 1145.0, 1859.0, 1148.0, 1852.0, 1161.0, 1846.0, 1160.0, 1842.0, 1163.0, 1833.0, 1173.0, 1830.0, 1170.0, 1820.0, 1173.0, 1816.0, 1183.0, 1812.0, 1179.0, 1804.0, 1181.0, 1795.0, 1191.0, 1790.0, 1192.0, 1782.0, 1182.0, 1780.0, 1182.0, 1784.0, 1192.0, 1789.0, 1197.0, 1785.0, 1200.0, 1781.0, 1196.0, 1778.0, 1196.0, 1777.0, 1205.0, 1761.0, 1217.0, 1762.0, 1225.0, 1758.0, 1223.0, 1755.0, 1214.0, 1748.0, 1214.0, 1748.0, 1217.0, 1738.0, 1214.0, 1731.0, 1214.0, 1730.0, 1217.0, 1732.0, 1220.0, 1744.0, 1223.0, 1760.0, 1233.0, 1761.0, 1229.0, 1766.0, 1229.0, 1766.0, 1233.0, 1773.0, 1239.0, 1778.0, 1241.0, 1785.0, 1246.0, 1785.0, 1251.0, 1794.0, 1251.0, 1782.0, 1237.0, 1774.0, 1230.0, 1770.0, 1221.0, 1775.0, 1218.0, 1783.0, 1226.0, 1799.0, 1245.0, 1803.0, 1251.0, 1809.0, 1247.0, 1810.0, 1243.0, 1814.0, 1241.0, 1818.0, 1244.0, 1822.0, 1243.0, 1826.0, 1238.0, 1834.0, 1237.0, 1836.0, 1240.0, 1836.0, 1245.0, 1836.0, 1253.0, 1838.0, 1257.0, 1820.0, 1264.0, 1818.0, 1263.0, 1813.0, 1258.0, 1804.0, 1259.0, 1802.0, 1264.0, 1802.0, 1267.0, 1797.0, 1271.0, 1790.0, 1279.0, 1769.0, 1290.0, 1768.0, 1297.0, 1764.0, 1300.0, 1762.0, 1309.0, 1744.0, 1310.0, 1741.0, 1307.0, 1736.0, 1307.0, 1734.0, 1306.0, 1742.0, 1301.0, 1737.0, 1293.0, 1737.0, 1281.0, 1734.0, 1272.0, 1731.0, 1270.0, 1724.0, 1273.0, 1720.0, 1270.0, 1716.0, 1270.0, 1715.0, 1267.0, 1704.0, 1265.0, 1701.0, 1268.0, 1695.0, 1268.0, 1700.0, 1258.0, 1706.0, 1258.0, 1714.0, 1241.0, 1724.0, 1241.0, 1736.0, 1233.0, 1731.0, 1231.0, 1728.0, 1224.0, 1719.0, 1224.0, 1712.0, 1221.0, 1705.0, 1223.0, 1699.0, 1228.0, 1692.0, 1229.0, 1690.0, 1231.0, 1680.0, 1231.0, 1668.0, 1228.0, 1667.0, 1223.0, 1665.0, 1222.0, 1654.0, 1225.0, 1650.0, 1220.0, 1677.0, 1213.0], [1883.0, 1268.0, 1874.0, 1265.0, 1859.0, 1255.0, 1863.0, 1251.0, 1860.0, 1249.0, 1850.0, 1253.0, 1846.0, 1252.0, 1842.0, 1247.0, 1852.0, 1247.0, 1856.0, 1245.0, 1852.0, 1241.0, 1846.0, 1241.0, 1846.0, 1238.0, 1853.0, 1235.0, 1860.0, 1239.0, 1864.0, 1235.0, 1869.0, 1241.0, 1874.0, 1241.0, 1872.0, 1237.0, 1872.0, 1234.0, 1874.0, 1233.0, 1874.0, 1227.0, 1872.0, 1225.0, 1874.0, 1221.0, 1883.0, 1221.0], [1876.0, 1166.0, 1882.0, 1166.0, 1882.0, 1185.0, 1879.0, 1181.0, 1872.0, 1177.0, 1866.0, 1163.0]], "capital": [1757.0, 1281.0], "configuredProductions": {"4": 2900.0}, "id": 150, "locationType": "province", "name": "Sevastopol", "neighbourIds": [149, 152, 161, 162, 163, 183, 184], "ownerId": 5, "population": 100000, "productionType": 4}, {"borders": [[1393.0, 936.0, 1398.0, 943.0, 1408.0, 949.0, 1407.0, 956.0, 1412.0, 969.0, 1426.0, 970.0, 1436.0, 965.0, 1442.0, 965.0, 1447.0, 960.0, 1458.0, 954.0, 1466.0, 955.0, 1473.0, 951.0, 1484.0, 952.0, 1490.0, 955.0, 1523.0, 947.0, 1532.0, 949.0, 1538.0, 949.0, 1541.0, 952.0, 1542.0, 958.0, 1550.0, 965.0, 1556.0, 963.0, 1561.0, 973.0, 1577.0, 992.0, 1582.0, 993.0, 1583.0, 988.0, 1594.0, 971.0, 1602.0, 970.0, 1620.0, 970.0, 1630.0, 960.0, 1639.0, 945.0, 1649.0, 943.0, 1658.0, 944.0, 1670.0, 958.0, 1682.0, 965.0, 1692.0, 973.0, 1699.0, 985.0, 1696.0, 1013.0, 1700.0, 1030.0, 1702.0, 1043.0, 1709.0, 1047.0, 1702.0, 1055.0, 1696.0, 1056.0, 1687.0, 1071.0, 1686.0, 1076.0, 1669.0, 1075.0, 1664.0, 1070.0, 1657.0, 1072.0, 1651.0, 1066.0, 1644.0, 1065.0, 1640.0, 1061.0, 1632.0, 1062.0, 1626.0, 1052.0, 1614.0, 1046.0, 1613.0, 1052.0, 1606.0, 1051.0, 1604.0, 1059.0, 1584.0, 1056.0, 1576.0, 1067.0, 1563.0, 1073.0, 1546.0, 1071.0, 1538.0, 1067.0, 1530.0, 1060.0, 1523.0, 1057.0, 1512.0, 1057.0, 1504.0, 1067.0, 1497.0, 1086.0, 1489.0, 1099.0, 1479.0, 1111.0, 1472.0, 1118.0, 1467.0, 1126.0, 1464.0, 1134.0, 1458.0, 1126.0, 1454.0, 1115.0, 1450.0, 1077.0, 1438.0, 1075.0, 1424.0, 1076.0, 1424.0, 1063.0, 1417.0, 1055.0, 1404.0, 1048.0, 1402.0, 1055.0, 1394.0, 1053.0, 1398.0, 1046.0, 1390.0, 1040.0, 1392.0, 1028.0, 1383.0, 1018.0, 1379.0, 1015.0, 1380.0, 1010.0, 1391.0, 1013.0, 1392.0, 1010.0, 1384.0, 998.0, 1374.0, 984.0, 1369.0, 970.0, 1376.0, 963.0, 1379.0, 938.0]], "capital": [1584.0, 1027.0], "configuredProductions": {"0": 5800.0}, "id": 151, "locationType": "province", "name": "Kiev", "neighbourIds": [15, 146, 148, 149, 152, 153, 162], "ownerId": 5, "population": 100000}, {"borders": [[1666.0, 897.0, 1677.0, 893.0, 1688.0, 892.0, 1702.0, 899.0, 1727.0, 907.0, 1738.0, 899.0, 1732.0, 888.0, 1736.0, 875.0, 1742.0, 864.0, 1747.0, 861.0, 1746.0, 850.0, 1740.0, 843.0, 1735.0, 834.0, 1748.0, 825.0, 1752.0, 827.0, 1764.0, 829.0, 1785.0, 824.0, 1796.0, 819.0, 1804.0, 813.0, 1812.0, 818.0, 1814.0, 823.0, 1814.0, 829.0, 1820.0, 828.0, 1826.0, 831.0, 1826.0, 839.0, 1832.0, 847.0, 1833.0, 855.0, 1829.0, 855.0, 1829.0, 867.0, 1838.0, 871.0, 1852.0, 873.0, 1860.0, 873.0, 1868.0, 871.0, 1881.0, 870.0, 1882.0, 869.0, 1882.0, 1053.0, 1866.0, 1055.0, 1861.0, 1059.0, 1850.0, 1072.0, 1840.0, 1077.0, 1825.0, 1077.0, 1815.0, 1071.0, 1798.0, 1072.0, 1786.0, 1075.0, 1772.0, 1078.0, 1758.0, 1080.0, 1740.0, 1081.0, 1732.0, 1086.0, 1729.0, 1089.0, 1719.0, 1089.0, 1718.0, 1084.0, 1712.0, 1085.0, 1708.0, 1080.0, 1701.0, 1082.0, 1687.0, 1076.0, 1686.0, 1076.0, 1687.0, 1071.0, 1696.0, 1056.0, 1702.0, 1055.0, 1709.0, 1047.0, 1702.0, 1043.0, 1700.0, 1030.0, 1696.0, 1013.0, 1699.0, 985.0, 1692.0, 973.0, 1682.0, 965.0, 1670.0, 958.0, 1658.0, 944.0, 1649.0, 943.0, 1639.0, 945.0, 1635.0, 936.0, 1648.0, 919.0, 1652.0, 909.0, 1661.0, 908.0]], "capital": [1775.0, 1023.0], "configuredProductions": {"0": 2900.0}, "id": 152, "locationType": "province", "name": "Kharkov", "neighbourIds": [149, 150, 151, 153, 154, 158, 162, 184], "ownerId": 5, "population": 100000}, {"borders": [[1531.0, 823.0, 1536.0, 829.0, 1542.0, 829.0, 1543.0, 816.0, 1546.0, 813.0, 1554.0, 817.0, 1557.0, 827.0, 1556.0, 836.0, 1552.0, 840.0, 1559.0, 847.0, 1559.0, 854.0, 1556.0, 864.0, 1557.0, 873.0, 1566.0, 887.0, 1572.0, 899.0, 1582.0, 900.0, 1592.0, 893.0, 1602.0, 884.0, 1626.0, 879.0, 1658.0, 887.0, 1666.0, 897.0, 1661.0, 908.0, 1652.0, 909.0, 1648.0, 919.0, 1635.0, 936.0, 1639.0, 945.0, 1630.0, 960.0, 1620.0, 970.0, 1602.0, 970.0, 1594.0, 971.0, 1583.0, 988.0, 1582.0, 993.0, 1577.0, 992.0, 1561.0, 973.0, 1556.0, 963.0, 1550.0, 965.0, 1542.0, 958.0, 1541.0, 952.0, 1538.0, 949.0, 1532.0, 949.0, 1523.0, 947.0, 1490.0, 955.0, 1484.0, 952.0, 1473.0, 951.0, 1466.0, 955.0, 1458.0, 954.0, 1447.0, 960.0, 1442.0, 965.0, 1436.0, 965.0, 1426.0, 970.0, 1412.0, 969.0, 1407.0, 956.0, 1408.0, 949.0, 1398.0, 943.0, 1393.0, 936.0, 1379.0, 938.0, 1372.0, 933.0, 1371.0, 922.0, 1362.0, 920.0, 1353.0, 926.0, 1336.0, 918.0, 1336.0, 911.0, 1338.0, 902.0, 1349.0, 899.0, 1354.0, 891.0, 1359.0, 879.0, 1364.0, 877.0, 1367.0, 880.0, 1376.0, 883.0, 1387.0, 880.0, 1395.0, 880.0, 1394.0, 874.0, 1388.0, 866.0, 1386.0, 860.0, 1388.0, 855.0, 1389.0, 848.0, 1392.0, 837.0, 1401.0, 821.0, 1397.0, 812.0, 1392.0, 807.0, 1369.0, 807.0, 1357.0, 799.0, 1358.0, 787.0, 1365.0, 777.0, 1375.0, 773.0, 1385.0, 775.0, 1396.0, 765.0, 1404.0, 758.0, 1419.0, 761.0, 1423.0, 769.0, 1435.0, 773.0, 1443.0, 772.0, 1455.0, 767.0, 1464.0, 774.0, 1472.0, 772.0, 1476.0, 775.0, 1480.0, 780.0, 1500.0, 780.0, 1516.0, 775.0, 1524.0, 788.0, 1528.0, 797.0, 1528.0, 807.0]], "capital": [1410.0, 837.0], "configuredProductions": {"3": 2900.0}, "id": 153, "locationType": "province", "name": "Vilna", "neighbourIds": [146, 147, 151, 152, 156, 158], "ownerId": 5, "population": 100000, "productionType": 3}, {"borders": [[1586.0, 646.0, 1588.0, 638.0, 1595.0, 632.0, 1590.0, 625.0, 1596.0, 617.0, 1604.0, 614.0, 1613.0, 618.0, 1624.0, 612.0, 1635.0, 602.0, 1642.0, 592.0, 1652.0, 584.0, 1655.0, 578.0, 1649.0, 575.0, 1650.0, 566.0, 1659.0, 565.0, 1673.0, 565.0, 1681.0, 570.0, 1705.0, 567.0, 1727.0, 572.0, 1738.0, 568.0, 1746.0, 575.0, 1753.0, 571.0, 1764.0, 576.0, 1775.0, 578.0, 1782.0, 573.0, 1796.0, 585.0, 1808.0, 592.0, 1820.0, 597.0, 1831.0, 595.0, 1832.0, 587.0, 1837.0, 591.0, 1840.0, 591.0, 1857.0, 585.0, 1862.0, 575.0, 1871.0, 576.0, 1882.0, 573.0, 1882.0, 869.0, 1881.0, 870.0, 1868.0, 871.0, 1860.0, 873.0, 1852.0, 873.0, 1838.0, 871.0, 1829.0, 867.0, 1829.0, 855.0, 1833.0, 855.0, 1832.0, 847.0, 1826.0, 839.0, 1826.0, 831.0, 1820.0, 828.0, 1814.0, 829.0, 1814.0, 823.0, 1812.0, 818.0, 1804.0, 813.0, 1796.0, 819.0, 1785.0, 824.0, 1764.0, 829.0, 1752.0, 827.0, 1748.0, 825.0, 1735.0, 834.0, 1735.0, 827.0, 1734.0, 822.0, 1736.0, 814.0, 1728.0, 814.0, 1728.0, 809.0, 1724.0, 801.0, 1708.0, 793.0, 1698.0, 793.0, 1680.0, 790.0, 1675.0, 775.0, 1673.0, 761.0, 1663.0, 752.0, 1661.0, 731.0, 1651.0, 725.0, 1638.0, 723.0, 1636.0, 718.0, 1642.0, 717.0, 1630.0, 703.0, 1622.0, 699.0, 1616.0, 691.0, 1612.0, 680.0, 1609.0, 679.0, 1600.0, 669.0, 1592.0, 654.0]], "capital": [1746.0, 729.0], "configuredProductions": {"5": 2900.0}, "id": 154, "locationType": "province", "name": "Moscow", "neighbourIds": [152, 155, 157, 158], "ownerId": 5, "population": 100000, "productionType": 5}, {"borders": [[1552.0, 390.0, 1559.0, 397.0, 1566.0, 397.0, 1570.0, 395.0, 1584.0, 403.0, 1581.0, 406.0, 1586.0, 409.0, 1590.0, 417.0, 1596.0, 421.0, 1597.0, 417.0, 1614.0, 422.0, 1616.0, 428.0, 1615.0, 431.0, 1617.0, 435.0, 1614.0, 437.0, 1611.0, 435.0, 1606.0, 435.0, 1611.0, 442.0, 1611.0, 443.0, 1602.0, 441.0, 1599.0, 435.0, 1592.0, 430.0, 1584.0, 420.0, 1582.0, 421.0, 1588.0, 431.0, 1596.0, 437.0, 1598.0, 441.0, 1589.0, 440.0, 1598.0, 456.0, 1592.0, 453.0, 1594.0, 459.0, 1600.0, 461.0, 1610.0, 470.0, 1615.0, 471.0, 1625.0, 469.0, 1630.0, 475.0, 1634.0, 483.0, 1634.0, 488.0, 1638.0, 492.0, 1651.0, 489.0, 1658.0, 502.0, 1670.0, 499.0, 1684.0, 517.0, 1683.0, 523.0, 1690.0, 526.0, 1698.0, 526.0, 1701.0, 533.0, 1706.0, 543.0, 1706.0, 551.0, 1706.0, 560.0, 1705.0, 567.0, 1681.0, 570.0, 1673.0, 565.0, 1659.0, 565.0, 1650.0, 566.0, 1649.0, 575.0, 1655.0, 578.0, 1652.0, 584.0, 1642.0, 592.0, 1635.0, 602.0, 1624.0, 612.0, 1613.0, 618.0, 1604.0, 614.0, 1596.0, 617.0, 1590.0, 625.0, 1595.0, 632.0, 1588.0, 638.0, 1586.0, 646.0, 1576.0, 641.0, 1566.0, 643.0, 1558.0, 636.0, 1550.0, 634.0, 1534.0, 632.0, 1522.0, 636.0, 1518.0, 643.0, 1496.0, 649.0, 1483.0, 641.0, 1460.0, 641.0, 1458.0, 637.0, 1456.0, 629.0, 1458.0, 622.0, 1452.0, 615.0, 1446.0, 615.0, 1442.0, 608.0, 1443.0, 598.0, 1455.0, 601.0, 1460.0, 593.0, 1456.0, 585.0, 1459.0, 580.0, 1464.0, 584.0, 1466.0, 582.0, 1466.0, 575.0, 1482.0, 575.0, 1485.0, 573.0, 1482.0, 570.0, 1490.0, 563.0, 1498.0, 567.0, 1508.0, 561.0, 1505.0, 559.0, 1496.0, 554.0, 1496.0, 545.0, 1504.0, 545.0, 1507.0, 535.0, 1512.0, 535.0, 1517.0, 531.0, 1522.0, 532.0, 1524.0, 543.0, 1533.0, 554.0, 1533.0, 557.0, 1537.0, 557.0, 1544.0, 554.0, 1542.0, 550.0, 1542.0, 545.0, 1550.0, 542.0, 1559.0, 543.0, 1566.0, 548.0, 1570.0, 543.0, 1570.0, 533.0, 1568.0, 527.0, 1574.0, 523.0, 1565.0, 509.0, 1563.0, 501.0, 1556.0, 494.0, 1543.0, 487.0, 1542.0, 483.0, 1545.0, 481.0, 1542.0, 471.0, 1536.0, 469.0, 1534.0, 460.0, 1547.0, 452.0, 1553.0, 461.0, 1558.0, 462.0, 1554.0, 445.0, 1546.0, 445.0, 1544.0, 441.0, 1550.0, 431.0, 1547.0, 421.0, 1542.0, 415.0, 1531.0, 415.0, 1539.0, 396.0, 1546.0, 392.0, 1551.0, 384.0]], "capital": [1514.0, 563.0], "configuredProductions": {"2": 2900.0}, "id": 155, "locationType": "province", "name": "St. Petersburg", "neighbourIds": [144, 154, 156, 157, 158, 160, 268], "ownerId": 5, "population": 100000, "productionType": 2}, {"borders": [[1442.0, 608.0, 1446.0, 615.0, 1439.0, 617.0, 1434.0, 628.0, 1436.0, 633.0, 1442.0, 634.0, 1444.0, 643.0, 1448.0, 650.0, 1452.0, 651.0, 1450.0, 659.0, 1456.0, 667.0, 1462.0, 670.0, 1467.0, 675.0, 1473.0, 673.0, 1466.0, 664.0, 1459.0, 664.0, 1454.0, 658.0, 1458.0, 652.0, 1456.0, 647.0, 1460.0, 641.0, 1483.0, 641.0, 1496.0, 649.0, 1518.0, 643.0, 1522.0, 636.0, 1534.0, 632.0, 1550.0, 634.0, 1544.0, 640.0, 1541.0, 645.0, 1552.0, 650.0, 1564.0, 662.0, 1566.0, 668.0, 1561.0, 673.0, 1560.0, 684.0, 1562.0, 687.0, 1563.0, 695.0, 1550.0, 699.0, 1551.0, 709.0, 1542.0, 711.0, 1545.0, 733.0, 1544.0, 745.0, 1538.0, 751.0, 1535.0, 752.0, 1522.0, 750.0, 1516.0, 754.0, 1518.0, 763.0, 1516.0, 775.0, 1500.0, 780.0, 1480.0, 780.0, 1476.0, 775.0, 1472.0, 772.0, 1464.0, 774.0, 1455.0, 767.0, 1443.0, 772.0, 1435.0, 773.0, 1423.0, 769.0, 1419.0, 761.0, 1404.0, 758.0, 1396.0, 765.0, 1385.0, 775.0, 1375.0, 773.0, 1365.0, 777.0, 1358.0, 787.0, 1357.0, 799.0, 1339.0, 801.0, 1326.0, 809.0, 1328.0, 813.0, 1311.0, 810.0, 1313.0, 807.0, 1303.0, 807.0, 1299.0, 808.0, 1286.0, 781.0, 1280.0, 782.0, 1280.0, 769.0, 1277.0, 763.0, 1279.0, 735.0, 1284.0, 729.0, 1286.0, 725.0, 1290.0, 721.0, 1292.0, 711.0, 1298.0, 705.0, 1300.0, 697.0, 1312.0, 695.0, 1320.0, 689.0, 1324.0, 691.0, 1326.0, 699.0, 1329.0, 700.0, 1342.0, 711.0, 1342.0, 715.0, 1356.0, 723.0, 1362.0, 723.0, 1373.0, 707.0, 1372.0, 702.0, 1372.0, 695.0, 1369.0, 691.0, 1368.0, 679.0, 1372.0, 673.0, 1370.0, 663.0, 1373.0, 657.0, 1367.0, 649.0, 1364.0, 652.0, 1364.0, 657.0, 1360.0, 659.0, 1356.0, 653.0, 1352.0, 653.0, 1350.0, 648.0, 1345.0, 640.0, 1353.0, 639.0, 1355.0, 635.0, 1344.0, 635.0, 1343.0, 631.0, 1346.0, 627.0, 1347.0, 624.0, 1344.0, 625.0, 1340.0, 620.0, 1346.0, 619.0, 1342.0, 615.0, 1346.0, 611.0, 1360.0, 609.0, 1368.0, 604.0, 1384.0, 602.0, 1392.0, 603.0, 1396.0, 599.0, 1396.0, 591.0, 1399.0, 594.0, 1411.0, 591.0, 1418.0, 597.0, 1427.0, 596.0, 1434.0, 599.0, 1443.0, 598.0], [1328.0, 632.0, 1322.0, 632.0, 1322.0, 635.0, 1320.0, 639.0, 1316.0, 637.0, 1313.0, 632.0, 1308.0, 633.0, 1306.0, 628.0, 1314.0, 627.0, 1318.0, 623.0, 1319.0, 619.0, 1322.0, 619.0, 1324.0, 623.0, 1330.0, 627.0], [1338.0, 653.0, 1334.0, 655.0, 1332.0, 661.0, 1325.0, 667.0, 1319.0, 663.0, 1313.0, 667.0, 1312.0, 680.0, 1305.0, 681.0, 1308.0, 668.0, 1302.0, 662.0, 1304.0, 659.0, 1300.0, 655.0, 1303.0, 651.0, 1308.0, 652.0, 1308.0, 648.0, 1320.0, 645.0, 1324.0, 646.0, 1325.0, 643.0, 1330.0, 643.0], [1256.0, 561.0, 1256.0, 566.0, 1252.0, 565.0, 1250.0, 570.0, 1245.0, 571.0, 1242.0, 563.0, 1244.0, 561.0, 1248.0, 563.0, 1246.0, 553.0]], "capital": [1373.0, 724.0], "configuredProductions": {"1": 2900.0}, "id": 156, "locationType": "province", "name": "Riga", "neighbourIds": [0, 147, 153, 155, 158, 264, 267, 268], "ownerId": 5, "population": 100000, "productionType": 1}, {"borders": [[1706.0, 560.0, 1706.0, 551.0, 1706.0, 543.0, 1701.0, 533.0, 1698.0, 526.0, 1700.0, 526.0, 1700.0, 522.0, 1702.0, 518.0, 1698.0, 515.0, 1690.0, 517.0, 1684.0, 517.0, 1670.0, 499.0, 1658.0, 502.0, 1651.0, 489.0, 1652.0, 481.0, 1658.0, 479.0, 1654.0, 473.0, 1652.0, 467.0, 1642.0, 458.0, 1636.0, 451.0, 1635.0, 446.0, 1626.0, 436.0, 1623.0, 423.0, 1626.0, 420.0, 1621.0, 414.0, 1616.0, 412.0, 1611.0, 413.0, 1596.0, 406.0, 1584.0, 403.0, 1570.0, 395.0, 1566.0, 397.0, 1559.0, 397.0, 1562.0, 391.0, 1567.0, 388.0, 1564.0, 384.0, 1570.0, 380.0, 1577.0, 377.0, 1578.0, 381.0, 1582.0, 379.0, 1582.0, 374.0, 1592.0, 374.0, 1587.0, 367.0, 1586.0, 363.0, 1580.0, 362.0, 1577.0, 357.0, 1571.0, 353.0, 1574.0, 342.0, 1579.0, 339.0, 1582.0, 330.0, 1593.0, 334.0, 1595.0, 330.0, 1601.0, 331.0, 1605.0, 335.0, 1612.0, 336.0, 1615.0, 344.0, 1626.0, 347.0, 1635.0, 344.0, 1643.0, 349.0, 1646.0, 347.0, 1647.0, 343.0, 1652.0, 341.0, 1654.0, 335.0, 1649.0, 332.0, 1646.0, 325.0, 1648.0, 322.0, 1639.0, 315.0, 1636.0, 316.0, 1635.0, 319.0, 1629.0, 321.0, 1624.0, 316.0, 1619.0, 315.0, 1616.0, 309.0, 1610.0, 305.0, 1606.0, 299.0, 1607.0, 298.0, 1613.0, 301.0, 1615.0, 298.0, 1612.0, 287.0, 1615.0, 281.0, 1623.0, 281.0, 1642.0, 292.0, 1640.0, 295.0, 1648.0, 296.0, 1650.0, 292.0, 1664.0, 293.0, 1677.0, 299.0, 1684.0, 313.0, 1683.0, 320.0, 1692.0, 331.0, 1700.0, 333.0, 1708.0, 336.0, 1713.0, 334.0, 1723.0, 335.0, 1729.0, 346.0, 1730.0, 356.0, 1740.0, 369.0, 1751.0, 368.0, 1761.0, 353.0, 1763.0, 356.0, 1766.0, 354.0, 1777.0, 360.0, 1790.0, 359.0, 1797.0, 368.0, 1807.0, 369.0, 1809.0, 375.0, 1814.0, 374.0, 1818.0, 378.0, 1828.0, 374.0, 1839.0, 376.0, 1846.0, 387.0, 1852.0, 387.0, 1859.0, 392.0, 1860.0, 391.0, 1873.0, 398.0, 1886.0, 395.0, 1882.0, 573.0, 1871.0, 576.0, 1862.0, 575.0, 1857.0, 585.0, 1840.0, 591.0, 1837.0, 591.0, 1832.0, 587.0, 1831.0, 595.0, 1820.0, 597.0, 1808.0, 592.0, 1796.0, 585.0, 1782.0, 573.0, 1775.0, 578.0, 1764.0, 576.0, 1753.0, 571.0, 1746.0, 575.0, 1738.0, 568.0, 1727.0, 572.0, 1705.0, 567.0]], "capital": [1763.0, 549.0], "configuredProductions": {"5": 2900.0}, "id": 157, "locationType": "province", "name": "Vologda", "neighbourIds": [154, 155, 159, 160], "ownerId": 5, "population": 100000, "productionType": 5}, {"borders": [[1680.0, 790.0, 1698.0, 793.0, 1708.0, 793.0, 1724.0, 801.0, 1728.0, 809.0, 1728.0, 814.0, 1736.0, 814.0, 1734.0, 822.0, 1735.0, 827.0, 1735.0, 834.0, 1740.0, 843.0, 1746.0, 850.0, 1747.0, 861.0, 1742.0, 864.0, 1736.0, 875.0, 1732.0, 888.0, 1738.0, 899.0, 1727.0, 907.0, 1702.0, 899.0, 1688.0, 892.0, 1677.0, 893.0, 1666.0, 897.0, 1658.0, 887.0, 1626.0, 879.0, 1602.0, 884.0, 1592.0, 893.0, 1582.0, 900.0, 1572.0, 899.0, 1566.0, 887.0, 1557.0, 873.0, 1556.0, 864.0, 1559.0, 854.0, 1559.0, 847.0, 1552.0, 840.0, 1556.0, 836.0, 1557.0, 827.0, 1554.0, 817.0, 1546.0, 813.0, 1543.0, 816.0, 1542.0, 829.0, 1536.0, 829.0, 1531.0, 823.0, 1528.0, 807.0, 1528.0, 797.0, 1524.0, 788.0, 1516.0, 775.0, 1518.0, 763.0, 1516.0, 754.0, 1522.0, 750.0, 1535.0, 752.0, 1538.0, 751.0, 1544.0, 745.0, 1545.0, 733.0, 1542.0, 711.0, 1551.0, 709.0, 1550.0, 699.0, 1563.0, 695.0, 1562.0, 687.0, 1560.0, 684.0, 1561.0, 673.0, 1566.0, 668.0, 1564.0, 662.0, 1552.0, 650.0, 1559.0, 645.0, 1566.0, 643.0, 1576.0, 641.0, 1586.0, 646.0, 1592.0, 654.0, 1600.0, 669.0, 1600.0, 675.0, 1604.0, 683.0, 1597.0, 684.0, 1599.0, 687.0, 1604.0, 688.0, 1608.0, 691.0, 1616.0, 691.0, 1622.0, 699.0, 1630.0, 703.0, 1642.0, 717.0, 1636.0, 718.0, 1638.0, 723.0, 1651.0, 725.0, 1661.0, 731.0, 1663.0, 752.0, 1673.0, 761.0, 1675.0, 775.0]], "capital": [1607.0, 810.0], "configuredProductions": {"2": 5800.0}, "id": 158, "locationType": "province", "name": "Smolensk", "neighbourIds": [152, 153, 154, 155, 156], "ownerId": 5, "population": 100000, "productionType": 2}, {"borders": [[1686.0, 290.0, 1689.0, 283.0, 1672.0, 265.0, 1664.0, 263.0, 1662.0, 255.0, 1669.0, 248.0, 1676.0, 243.0, 1682.0, 230.0, 1696.0, 217.0, 1706.0, 191.0, 1710.0, 190.0, 1718.0, 193.0, 1722.0, 190.0, 1730.0, 191.0, 1735.0, 196.0, 1733.0, 201.0, 1736.0, 203.0, 1742.0, 199.0, 1752.0, 211.0, 1754.0, 203.0, 1747.0, 194.0, 1748.0, 180.0, 1746.0, 174.0, 1741.0, 172.0, 1738.0, 169.0, 1743.0, 169.0, 1742.0, 159.0, 1734.0, 151.0, 1726.0, 154.0, 1720.0, 149.0, 1720.0, 125.0, 1717.0, 123.0, 1722.0, 118.0, 1714.0, 111.0, 1714.0, 102.0, 1709.0, 94.0, 1696.0, 91.0, 1686.0, 83.0, 1702.0, 85.0, 1711.0, 87.0, 1724.0, 79.0, 1738.0, 82.0, 1754.0, 94.0, 1761.0, 106.0, 1755.0, 109.0, 1751.0, 113.0, 1742.0, 116.0, 1740.0, 121.0, 1744.0, 127.0, 1741.0, 137.0, 1753.0, 139.0, 1768.0, 137.0, 1772.0, 141.0, 1770.0, 144.0, 1780.0, 151.0, 1785.0, 148.0, 1798.0, 145.0, 1810.0, 135.0, 1806.0, 125.0, 1807.0, 120.0, 1801.0, 107.0, 1805.0, 99.0, 1808.0, 99.0, 1812.0, 90.0, 1802.0, 83.0, 1815.0, 81.0, 1836.0, 48.0, 1845.0, 47.0, 1848.0, 43.0, 1848.0, 37.0, 1858.0, 35.0, 1862.0, 44.0, 1866.0, 43.0, 1866.0, 38.0, 1857.0, 29.0, 1857.0, 27.0, 1866.0, 9.0, 1876.0, 0.0, 1887.0, 1.0, 1886.0, 395.0, 1873.0, 398.0, 1860.0, 391.0, 1859.0, 392.0, 1852.0, 387.0, 1846.0, 387.0, 1839.0, 376.0, 1828.0, 374.0, 1818.0, 378.0, 1814.0, 374.0, 1809.0, 375.0, 1807.0, 369.0, 1797.0, 368.0, 1790.0, 359.0, 1777.0, 360.0, 1766.0, 354.0, 1763.0, 356.0, 1761.0, 353.0, 1751.0, 368.0, 1740.0, 369.0, 1730.0, 356.0, 1729.0, 346.0, 1723.0, 335.0, 1713.0, 334.0, 1708.0, 336.0, 1700.0, 333.0, 1692.0, 331.0, 1683.0, 320.0, 1684.0, 313.0, 1677.0, 299.0, 1681.0, 298.0, 1677.0, 292.0], [1780.0, 51.0, 1774.0, 45.0, 1768.0, 45.0, 1762.0, 39.0, 1762.0, 23.0, 1767.0, 20.0, 1767.0, 14.0, 1770.0, 13.0, 1788.0, 21.0, 1791.0, 23.0, 1791.0, 31.0, 1790.0, 38.0, 1786.0, 43.0, 1786.0, 49.0]], "capital": [1702.0, 293.0], "configuredProductions": {"6": 2900.0}, "id": 159, "locationType": "province", "name": "<PERSON><PERSON><PERSON>", "neighbourIds": [157, 318, 319, 320], "ownerId": 5, "population": 100000, "productionType": 6}, {"borders": [[1579.0, 339.0, 1574.0, 342.0, 1571.0, 353.0, 1572.0, 362.0, 1577.0, 371.0, 1577.0, 377.0, 1570.0, 380.0, 1564.0, 384.0, 1559.0, 377.0, 1551.0, 384.0, 1546.0, 392.0, 1539.0, 396.0, 1531.0, 415.0, 1526.0, 410.0, 1520.0, 410.0, 1516.0, 405.0, 1512.0, 406.0, 1508.0, 397.0, 1500.0, 396.0, 1493.0, 385.0, 1484.0, 383.0, 1472.0, 372.0, 1486.0, 361.0, 1482.0, 339.0, 1482.0, 332.0, 1476.0, 337.0, 1478.0, 345.0, 1474.0, 348.0, 1472.0, 342.0, 1476.0, 321.0, 1486.0, 319.0, 1490.0, 312.0, 1487.0, 308.0, 1492.0, 307.0, 1494.0, 309.0, 1502.0, 309.0, 1506.0, 315.0, 1510.0, 313.0, 1511.0, 309.0, 1506.0, 307.0, 1502.0, 303.0, 1497.0, 303.0, 1494.0, 300.0, 1490.0, 300.0, 1486.0, 306.0, 1484.0, 311.0, 1479.0, 311.0, 1476.0, 308.0, 1468.0, 307.0, 1472.0, 312.0, 1478.0, 313.0, 1479.0, 315.0, 1472.0, 327.0, 1463.0, 327.0, 1458.0, 322.0, 1456.0, 318.0, 1456.0, 314.0, 1460.0, 309.0, 1459.0, 304.0, 1452.0, 303.0, 1454.0, 298.0, 1458.0, 295.0, 1452.0, 279.0, 1454.0, 274.0, 1462.0, 274.0, 1468.0, 265.0, 1476.0, 256.0, 1482.0, 265.0, 1488.0, 263.0, 1486.0, 256.0, 1486.0, 251.0, 1478.0, 246.0, 1477.0, 240.0, 1472.0, 241.0, 1474.0, 247.0, 1472.0, 249.0, 1472.0, 255.0, 1464.0, 257.0, 1456.0, 249.0, 1459.0, 243.0, 1456.0, 229.0, 1460.0, 225.0, 1453.0, 218.0, 1456.0, 211.0, 1460.0, 198.0, 1445.0, 183.0, 1429.0, 173.0, 1424.0, 165.0, 1418.0, 159.0, 1408.0, 158.0, 1404.0, 153.0, 1402.0, 139.0, 1406.0, 124.0, 1398.0, 127.0, 1398.0, 134.0, 1394.0, 131.0, 1390.0, 137.0, 1387.0, 135.0, 1386.0, 130.0, 1380.0, 131.0, 1380.0, 127.0, 1392.0, 115.0, 1392.0, 111.0, 1400.0, 104.0, 1404.0, 110.0, 1400.0, 119.0, 1407.0, 123.0, 1412.0, 117.0, 1418.0, 115.0, 1425.0, 96.0, 1426.0, 90.0, 1428.0, 89.0, 1431.0, 93.0, 1442.0, 91.0, 1440.0, 78.0, 1452.0, 79.0, 1458.0, 87.0, 1461.0, 81.0, 1459.0, 74.0, 1464.0, 77.0, 1465.0, 73.0, 1463.0, 68.0, 1465.0, 65.0, 1472.0, 69.0, 1481.0, 70.0, 1484.0, 73.0, 1481.0, 78.0, 1475.0, 78.0, 1471.0, 81.0, 1466.0, 79.0, 1464.0, 85.0, 1477.0, 87.0, 1482.0, 91.0, 1494.0, 88.0, 1494.0, 102.0, 1495.0, 105.0, 1498.0, 101.0, 1498.0, 96.0, 1524.0, 95.0, 1526.0, 93.0, 1524.0, 90.0, 1528.0, 89.0, 1532.0, 91.0, 1539.0, 91.0, 1550.0, 96.0, 1560.0, 97.0, 1569.0, 105.0, 1588.0, 112.0, 1600.0, 122.0, 1608.0, 121.0, 1617.0, 127.0, 1623.0, 130.0, 1626.0, 127.0, 1636.0, 133.0, 1642.0, 139.0, 1646.0, 141.0, 1651.0, 139.0, 1662.0, 148.0, 1667.0, 161.0, 1673.0, 167.0, 1675.0, 172.0, 1674.0, 177.0, 1676.0, 184.0, 1673.0, 195.0, 1670.0, 199.0, 1670.0, 204.0, 1664.0, 219.0, 1660.0, 219.0, 1654.0, 221.0, 1648.0, 227.0, 1622.0, 232.0, 1612.0, 232.0, 1605.0, 230.0, 1594.0, 232.0, 1568.0, 231.0, 1561.0, 227.0, 1551.0, 224.0, 1547.0, 228.0, 1542.0, 227.0, 1544.0, 223.0, 1544.0, 220.0, 1528.0, 213.0, 1526.0, 214.0, 1528.0, 216.0, 1527.0, 218.0, 1524.0, 216.0, 1514.0, 210.0, 1511.0, 209.0, 1511.0, 206.0, 1506.0, 203.0, 1490.0, 191.0, 1498.0, 189.0, 1507.0, 189.0, 1509.0, 186.0, 1503.0, 180.0, 1508.0, 176.0, 1509.0, 152.0, 1507.0, 150.0, 1504.0, 156.0, 1504.0, 163.0, 1500.0, 167.0, 1501.0, 175.0, 1498.0, 183.0, 1479.0, 192.0, 1482.0, 197.0, 1491.0, 193.0, 1497.0, 198.0, 1487.0, 205.0, 1490.0, 207.0, 1498.0, 206.0, 1504.0, 213.0, 1504.0, 219.0, 1507.0, 219.0, 1521.0, 229.0, 1511.0, 230.0, 1512.0, 235.0, 1520.0, 234.0, 1528.0, 239.0, 1529.0, 243.0, 1532.0, 244.0, 1540.0, 243.0, 1553.0, 252.0, 1552.0, 255.0, 1557.0, 255.0, 1564.0, 261.0, 1562.0, 265.0, 1556.0, 259.0, 1562.0, 279.0, 1558.0, 284.0, 1560.0, 292.0, 1564.0, 295.0, 1574.0, 317.0, 1574.0, 325.0, 1579.0, 330.0, 1582.0, 329.0, 1582.0, 330.0]], "capital": [1488.0, 200.0], "configuredProductions": {"1": 2900.0}, "id": 160, "locationType": "province", "name": "Kandalakskaya", "neighbourIds": [135, 140, 142, 144, 155, 157, 315, 316, 317, 320], "ownerId": 5, "population": 100000, "productionType": 1}, {"capital": [1783.0, 1341.0], "id": 161, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [39, 150, 163, 183]}, {"capital": [1661.0, 1285.0], "id": 162, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [50, 52, 149, 150, 151, 152, 163, 164]}, {"capital": [1710.0, 1360.0], "id": 163, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [39, 150, 161, 162, 164, 327]}, {"capital": [1622.0, 1347.0], "id": 164, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [32, 50, 162, 163, 165, 327]}, {"capital": [1606.0, 1447.0], "id": 165, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [32, 33, 41, 164, 166, 327]}, {"capital": [1595.0, 1500.0], "id": 166, "locationType": "sea-point", "name": "Bospor<PERSON>", "neighbourIds": [33, 41, 165, 167]}, {"capital": [1584.0, 1519.0], "id": 167, "locationType": "sea-point", "name": "Sea of Marmora", "neighbourIds": [33, 41, 42, 166, 168]}, {"capital": [1540.0, 1519.0], "id": 168, "locationType": "sea-point", "name": "Sea of Marmora", "neighbourIds": [31, 32, 33, 42, 43, 167, 169]}, {"capital": [1477.0, 1569.0], "id": 169, "locationType": "sea-point", "name": "Aegean Sea", "neighbourIds": [31, 34, 43, 168, 170]}, {"capital": [1437.0, 1605.0], "id": 170, "locationType": "sea-point", "name": "Aegean Sea", "neighbourIds": [34, 43, 54, 55, 169, 171]}, {"capital": [1458.0, 1675.0], "id": 171, "locationType": "sea-point", "name": "Aegean Sea", "neighbourIds": [43, 54, 170, 172, 173]}, {"capital": [1500.0, 1739.0], "id": 172, "locationType": "sea-point", "name": "Aegean Sea", "neighbourIds": [43, 56, 171, 173, 174, 180]}, {"capital": [1411.0, 1739.0], "id": 173, "locationType": "sea-point", "name": "Aegean Sea", "neighbourIds": [53, 54, 56, 171, 172, 182]}, {"capital": [1609.0, 1773.0], "id": 174, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [43, 44, 172, 175, 178, 180]}, {"capital": [1726.0, 1753.0], "id": 175, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [38, 44, 57, 174, 176, 178]}, {"capital": [1829.0, 1723.0], "id": 176, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [38, 57, 175, 179]}, {"capital": [1871.0, 1813.0], "id": 177, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [57, 178, 179]}, {"capital": [1761.0, 1815.0], "id": 178, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [57, 174, 175, 177]}, {"capital": [1871.0, 1731.0], "id": 179, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [57, 176, 177]}, {"capital": [1559.0, 1813.0], "id": 180, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [56, 172, 174, 181]}, {"capital": [1408.0, 1815.0], "id": 181, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [56, 180, 182, 185]}, {"capital": [1349.0, 1778.0], "id": 182, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [53, 56, 173, 181, 185, 186]}, {"capital": [1850.0, 1300.0], "id": 183, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [39, 150, 161, 184]}, {"capital": [1836.0, 1227.0], "id": 184, "locationType": "sea-point", "name": "Sea of Azov", "neighbourIds": [150, 152, 183]}, {"capital": [1303.0, 1813.0], "id": 185, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [181, 182, 186, 187]}, {"capital": [1255.0, 1718.0], "id": 186, "locationType": "sea-point", "name": "Ionian Sea", "neighbourIds": [53, 182, 185, 187, 188, 189]}, {"capital": [1176.0, 1812.0], "id": 187, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [185, 186, 188, 194]}, {"capital": [1125.0, 1700.0], "id": 188, "locationType": "sea-point", "name": "Ionian Sea", "neighbourIds": [66, 186, 187, 189, 194, 195, 196]}, {"capital": [1210.0, 1623.0], "id": 189, "locationType": "sea-point", "name": "Ionian Sea", "neighbourIds": [35, 53, 66, 67, 186, 188, 190, 197]}, {"capital": [1197.0, 1520.0], "id": 190, "locationType": "sea-point", "name": "Adriatic Sea", "neighbourIds": [35, 37, 46, 67, 189, 191]}, {"capital": [1149.0, 1468.0], "id": 191, "locationType": "sea-point", "name": "Adriatic Sea", "neighbourIds": [19, 46, 65, 67, 190, 192]}, {"capital": [1078.0, 1426.0], "id": 192, "locationType": "sea-point", "name": "Adriatic Sea", "neighbourIds": [19, 64, 65, 191, 193]}, {"capital": [1029.0, 1354.0], "id": 193, "locationType": "sea-point", "name": "Adriatic Sea", "neighbourIds": [18, 19, 60, 63, 64, 69, 192]}, {"capital": [1064.0, 1806.0], "id": 194, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [187, 188, 195, 200]}, {"capital": [1008.0, 1752.0], "id": 195, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [58, 188, 194, 196, 200, 201]}, {"capital": [1070.0, 1670.0], "id": 196, "locationType": "sea-point", "name": "Ionian Sea", "neighbourIds": [58, 66, 188, 195, 197]}, {"capital": [1082.0, 1645.0], "id": 197, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [58, 66, 67, 189, 196, 198]}, {"capital": [1041.0, 1567.0], "id": 198, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [58, 65, 67, 197, 199, 203]}, {"capital": [975.0, 1616.0], "id": 199, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [58, 198, 201, 202, 203]}, {"capital": [919.0, 1801.0], "id": 200, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [114, 194, 195, 201]}, {"capital": [908.0, 1687.0], "id": 201, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [58, 113, 114, 195, 199, 200, 202]}, {"capital": [887.0, 1613.0], "id": 202, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [59, 113, 199, 201, 203, 204]}, {"capital": [949.0, 1528.0], "id": 203, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [59, 64, 65, 198, 199, 202, 205]}, {"capital": [796.0, 1642.0], "id": 204, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [59, 112, 113, 202, 208, 209]}, {"capital": [876.0, 1473.0], "id": 205, "locationType": "sea-point", "name": "Tyrrhenian Sea", "neighbourIds": [59, 64, 69, 70, 203, 206, 328]}, {"capital": [863.0, 1360.0], "id": 206, "locationType": "sea-point", "name": "Gulf of Genoa", "neighbourIds": [62, 63, 69, 70, 205, 207]}, {"capital": [767.0, 1402.0], "id": 207, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [62, 68, 70, 79, 206, 210, 211, 212]}, {"capital": [705.0, 1615.0], "id": 208, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [110, 111, 112, 204, 209, 214, 215]}, {"capital": [750.0, 1540.0], "id": 209, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [59, 204, 208, 210, 211, 214, 328]}, {"capital": [784.0, 1469.0], "id": 210, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [70, 207, 209, 211, 328]}, {"capital": [708.0, 1457.0], "id": 211, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [207, 209, 210, 212, 213, 214]}, {"capital": [676.0, 1375.0], "id": 212, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [78, 79, 90, 207, 211, 213]}, {"capital": [626.0, 1453.0], "id": 213, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [88, 90, 211, 212, 214, 219]}, {"capital": [659.0, 1534.0], "id": 214, "locationType": "sea-point", "name": "Balearic Islands", "neighbourIds": [88, 208, 209, 211, 213, 215, 216]}, {"capital": [614.0, 1617.0], "id": 215, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [109, 111, 208, 214, 216]}, {"capital": [558.0, 1553.0], "id": 216, "locationType": "sea-point", "name": "Balearic Islands", "neighbourIds": [88, 109, 214, 215, 217, 218]}, {"capital": [489.0, 1614.0], "id": 217, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [98, 108, 109, 216, 218, 220, 330]}, {"capital": [496.0, 1501.0], "id": 218, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [88, 92, 94, 98, 216, 217, 219]}, {"capital": [540.0, 1465.0], "id": 219, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [88, 90, 92, 213, 218]}, {"capital": [379.0, 1633.0], "id": 220, "locationType": "sea-point", "name": "Strait of Gibraltar", "neighbourIds": [98, 108, 115, 217, 221]}, {"capital": [287.0, 1621.0], "id": 221, "locationType": "sea-point", "name": "Strait of Gibraltar", "neighbourIds": [91, 98, 106, 107, 115, 220, 222]}, {"capital": [203.0, 1603.0], "id": 222, "locationType": "sea-point", "name": "Strait of Gibraltar", "neighbourIds": [91, 95, 106, 221, 223, 224]}, {"capital": [105.0, 1554.0], "id": 223, "locationType": "sea-point", "name": "Strait of Gibraltar", "neighbourIds": [95, 102, 222, 224, 225]}, {"capital": [98.0, 1631.0], "id": 224, "locationType": "sea-point", "name": "Strait of Gibraltar", "neighbourIds": [105, 106, 222, 223, 225, 324, 329]}, {"capital": [14.0, 1557.0], "id": 225, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [102, 223, 224, 226, 324]}, {"capital": [58.0, 1461.0], "id": 226, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [101, 102, 225, 227]}, {"capital": [10.0, 1395.0], "id": 227, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [101, 226, 228, 229]}, {"capital": [96.0, 1342.0], "id": 228, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [100, 101, 103, 227, 229, 230]}, {"capital": [15.0, 1279.0], "id": 229, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [227, 228, 230, 231]}, {"capital": [110.0, 1257.0], "id": 230, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [93, 100, 228, 229, 231, 232]}, {"capital": [16.0, 1176.0], "id": 231, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [229, 230, 232, 234]}, {"capital": [155.0, 1160.0], "id": 232, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [93, 230, 231, 233, 234, 239, 240]}, {"capital": [265.0, 1177.0], "id": 233, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [93, 99, 232, 235, 238, 239]}, {"capital": [14.0, 1031.0], "id": 234, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [231, 232, 240, 293]}, {"capital": [389.0, 1243.0], "id": 235, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [81, 92, 99, 233, 236, 238]}, {"capital": [453.0, 1155.0], "id": 236, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [81, 82, 87, 235, 237, 238]}, {"capital": [380.0, 1063.0], "id": 237, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [87, 236, 238, 239, 241, 242]}, {"capital": [352.0, 1135.0], "id": 238, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [233, 235, 236, 237, 239]}, {"capital": [249.0, 1087.0], "id": 239, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [232, 233, 237, 238, 240, 241]}, {"capital": [162.0, 1005.0], "id": 240, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [232, 234, 239, 241, 292, 293]}, {"capital": [294.0, 983.0], "id": 241, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [237, 239, 240, 242, 274, 292]}, {"capital": [403.0, 969.0], "id": 242, "locationType": "sea-point", "name": "English Channel", "neighbourIds": [87, 237, 241, 243, 274, 275]}, {"capital": [495.0, 983.0], "id": 243, "locationType": "sea-point", "name": "English Channel", "neighbourIds": [82, 87, 125, 126, 242, 244, 275]}, {"capital": [580.0, 972.0], "id": 244, "locationType": "sea-point", "name": "English Channel", "neighbourIds": [77, 82, 125, 243, 245]}, {"capital": [646.0, 963.0], "id": 245, "locationType": "sea-point", "name": "Strait of Dover", "neighbourIds": [77, 84, 116, 125, 244, 323]}, {"capital": [706.0, 911.0], "id": 246, "locationType": "sea-point", "name": "English Channel", "neighbourIds": [74, 75, 84, 116, 247, 248, 323]}, {"capital": [774.0, 843.0], "id": 247, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [75, 76, 246, 248, 249, 250, 251]}, {"capital": [687.0, 825.0], "id": 248, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [116, 118, 120, 246, 247, 249, 322]}, {"capital": [741.0, 747.0], "id": 249, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [247, 248, 250, 253, 284, 322]}, {"capital": [842.0, 771.0], "id": 250, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [247, 249, 251, 252, 253]}, {"capital": [880.0, 833.0], "id": 251, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [2, 6, 76, 247, 250, 252, 258, 259]}, {"capital": [879.0, 754.0], "id": 252, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [25, 250, 251, 253, 258, 333, 334]}, {"capital": [828.0, 679.0], "id": 253, "locationType": "sea-point", "name": "Skagerrak", "neighbourIds": [136, 249, 250, 252, 283, 284, 285, 334]}, {"capital": [931.0, 679.0], "id": 254, "locationType": "sea-point", "name": "Skagerrak", "neighbourIds": [136, 255, 331, 333, 334]}, {"capital": [994.0, 644.0], "id": 255, "locationType": "sea-point", "name": "Skagerrak", "neighbourIds": [129, 131, 136, 137, 254, 331]}, {"capital": [1005.0, 721.0], "id": 256, "locationType": "sea-point", "name": "Kattegat", "neighbourIds": [24, 25, 26, 129, 257, 258, 331]}, {"capital": [1040.0, 774.0], "id": 257, "locationType": "sea-point", "name": "Kattegat", "neighbourIds": [24, 128, 129, 256, 260, 261, 263]}, {"capital": [980.0, 778.0], "id": 258, "locationType": "sea-point", "name": "Kattegat", "neighbourIds": [24, 25, 251, 252, 256, 259]}, {"capital": [991.0, 804.0], "id": 259, "locationType": "sea-point", "name": "Kattegat", "neighbourIds": [2, 24, 251, 258, 260]}, {"capital": [1045.0, 821.0], "id": 260, "locationType": "sea-point", "name": "Kattegat", "neighbourIds": [2, 4, 24, 257, 259, 261]}, {"capital": [1114.0, 831.0], "id": 261, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [4, 5, 257, 260, 262, 263]}, {"capital": [1216.0, 795.0], "id": 262, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [0, 5, 261, 263, 264, 269]}, {"capital": [1147.0, 771.0], "id": 263, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [128, 257, 261, 262, 269]}, {"capital": [1254.0, 705.0], "id": 264, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [0, 156, 262, 265, 266, 267, 269, 270]}, {"capital": [1237.0, 619.0], "id": 265, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [130, 264, 266, 270, 271]}, {"capital": [1318.0, 599.0], "id": 266, "locationType": "sea-point", "name": "Gulf of Finland", "neighbourIds": [143, 264, 265, 267, 271]}, {"capital": [1353.0, 670.0], "id": 267, "locationType": "sea-point", "name": "Gulf of Riga", "neighbourIds": [143, 156, 264, 266, 268]}, {"capital": [1444.0, 561.0], "id": 268, "locationType": "sea-point", "name": "Gulf of Finland", "neighbourIds": [143, 144, 155, 156, 267]}, {"capital": [1190.0, 751.0], "id": 269, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [128, 262, 263, 264, 270]}, {"capital": [1198.0, 663.0], "id": 270, "locationType": "sea-point", "name": "Baltic Sea", "neighbourIds": [128, 130, 131, 264, 265, 269]}, {"capital": [1237.0, 517.0], "id": 271, "locationType": "sea-point", "name": "Gulf of Bothnia", "neighbourIds": [130, 132, 141, 143, 265, 266, 272]}, {"capital": [1256.0, 437.0], "id": 272, "locationType": "sea-point", "name": "Gulf of Bothnia", "neighbourIds": [132, 133, 141, 271]}, {"capital": [1318.0, 337.0], "id": 273, "locationType": "sea-point", "name": "Gulf of Bothnia", "neighbourIds": [133, 134, 140, 141, 142]}, {"capital": [360.0, 863.0], "id": 274, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [124, 241, 242, 275, 276, 291, 292]}, {"capital": [430.0, 900.0], "id": 275, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [126, 242, 243, 274, 276]}, {"capital": [435.0, 840.0], "id": 276, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [123, 124, 126, 274, 275, 277]}, {"capital": [486.0, 798.0], "id": 277, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [117, 123, 126, 276, 278]}, {"capital": [512.0, 759.0], "id": 278, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [117, 119, 122, 123, 127, 277, 279]}, {"capital": [511.0, 699.0], "id": 279, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [119, 122, 278, 280]}, {"capital": [465.0, 628.0], "id": 280, "locationType": "sea-point", "name": "Irish Sea", "neighbourIds": [119, 121, 122, 279, 281, 290, 321]}, {"capital": [544.0, 496.0], "id": 281, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [121, 280, 282, 298, 306, 321]}, {"capital": [631.0, 477.0], "id": 282, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [121, 281, 283, 287, 306, 332]}, {"capital": [722.0, 584.0], "id": 283, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [121, 253, 282, 284, 285, 332]}, {"capital": [669.0, 676.0], "id": 284, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [119, 121, 127, 249, 253, 283, 322]}, {"capital": [821.0, 544.0], "id": 285, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [136, 138, 253, 283, 286, 332]}, {"capital": [844.0, 433.0], "id": 286, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [138, 139, 285, 287, 288, 289, 332]}, {"capital": [730.0, 415.0], "id": 287, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [282, 286, 289, 306, 307, 332]}, {"capital": [977.0, 342.0], "id": 288, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [139, 286, 289, 310, 311]}, {"capital": [810.0, 309.0], "id": 289, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [286, 287, 288, 307, 310]}, {"capital": [330.0, 669.0], "id": 290, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [122, 124, 280, 291, 295, 296, 321]}, {"capital": [262.0, 803.0], "id": 291, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [124, 274, 290, 292, 295]}, {"capital": [217.0, 893.0], "id": 292, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [240, 241, 274, 291, 293, 295]}, {"capital": [86.0, 862.0], "id": 293, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [234, 240, 292, 294, 295]}, {"capital": [9.0, 729.0], "id": 294, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [293, 295, 296, 297]}, {"capital": [161.0, 752.0], "id": 295, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [290, 291, 292, 293, 294, 296]}, {"capital": [220.0, 607.0], "id": 296, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [290, 294, 295, 297, 298, 299, 321]}, {"capital": [17.0, 473.0], "id": 297, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [294, 296, 299]}, {"capital": [363.0, 447.0], "id": 298, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [281, 296, 299, 302, 304, 306, 321]}, {"capital": [196.0, 278.0], "id": 299, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [296, 297, 298, 300, 302]}, {"capital": [13.0, 27.0], "id": 300, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [299, 301, 302]}, {"capital": [315.0, 24.0], "id": 301, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [27, 28, 300, 302, 303]}, {"capital": [311.0, 191.0], "id": 302, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [27, 298, 299, 300, 301, 304]}, {"capital": [496.0, 22.0], "id": 303, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [28, 301, 305, 308]}, {"capital": [467.0, 257.0], "id": 304, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [27, 28, 298, 302, 306, 308]}, {"capital": [663.0, 17.0], "id": 305, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [303, 308, 309, 314]}, {"capital": [579.0, 348.0], "id": 306, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [281, 282, 287, 298, 304, 307, 308]}, {"capital": [684.0, 232.0], "id": 307, "locationType": "sea-point", "name": "sea", "neighbourIds": [287, 289, 306, 308, 309, 310]}, {"capital": [596.0, 139.0], "id": 308, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [28, 303, 304, 305, 306, 307, 309]}, {"capital": [758.0, 112.0], "id": 309, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [305, 307, 308, 310, 314]}, {"capital": [883.0, 202.0], "id": 310, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [288, 289, 307, 309, 311, 314, 326]}, {"capital": [1079.0, 204.0], "id": 311, "locationType": "sea-point", "name": "West Fiord", "neighbourIds": [133, 134, 135, 139, 288, 310, 312, 326]}, {"capital": [1201.0, 70.0], "id": 312, "locationType": "sea-point", "name": "Lofoden Islands", "neighbourIds": [135, 311, 313, 315, 326]}, {"capital": [1047.0, 15.0], "id": 313, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [312, 314, 326]}, {"capital": [837.0, 15.0], "id": 314, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [305, 309, 310, 313, 326]}, {"capital": [1304.0, 6.0], "id": 315, "locationType": "sea-point", "name": "North Cape", "neighbourIds": [135, 160, 312, 316]}, {"capital": [1476.0, 9.0], "id": 316, "locationType": "sea-point", "name": "North Cape", "neighbourIds": [160, 315, 317]}, {"capital": [1583.0, 68.0], "id": 317, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [160, 316, 318, 320, 325]}, {"capital": [1701.0, 174.0], "id": 318, "locationType": "sea-point", "name": "White Sea", "neighbourIds": [159, 317, 319, 320, 325]}, {"capital": [1644.0, 257.0], "id": 319, "locationType": "sea-point", "name": "White Sea", "neighbourIds": [159, 318, 320]}, {"capital": [1585.0, 254.0], "id": 320, "locationType": "sea-point", "name": "White Sea", "neighbourIds": [142, 144, 159, 160, 317, 318, 319]}, {"capital": [411.0, 546.0], "id": 321, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [280, 281, 290, 296, 298]}, {"capital": [658.0, 742.0], "id": 322, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [118, 127, 248, 249, 284]}, {"capital": [666.0, 941.0], "id": 323, "locationType": "sea-point", "name": "Strait of Dover", "neighbourIds": [84, 116, 245, 246]}, {"capital": [18.0, 1669.0], "id": 324, "locationType": "sea-point", "name": "Atlantic Ocean", "neighbourIds": [104, 105, 224, 225]}, {"capital": [1663.0, 7.0], "id": 325, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [317, 318]}, {"capital": [1019.0, 113.0], "id": 326, "locationType": "sea-point", "name": "Arctic Basin", "neighbourIds": [310, 311, 312, 313, 314]}, {"capital": [1684.0, 1434.0], "id": 327, "locationType": "sea-point", "name": "Black Sea", "neighbourIds": [39, 40, 41, 163, 164, 165]}, {"capital": [831.0, 1481.0], "id": 328, "locationType": "sea-point", "name": "Mediterranean Sea", "neighbourIds": [59, 70, 205, 209, 210]}, {"borders": [[174.0, 1671.0, 184.0, 1675.0, 193.0, 1685.0, 190.0, 1694.0, 188.0, 1697.0, 183.0, 1704.0, 183.0, 1711.0, 186.0, 1716.0, 186.0, 1727.0, 194.0, 1749.0, 178.0, 1750.0, 161.0, 1758.0, 145.0, 1758.0, 120.0, 1750.0, 111.0, 1727.0, 118.0, 1714.0, 123.0, 1710.0, 125.0, 1689.0, 138.0, 1687.0, 148.0, 1677.0, 152.0, 1669.0, 158.0, 1664.0, 164.0, 1672.0]], "capital": [148.0, 1683.0], "configuredProductions": {"6": 2900.0}, "id": 329, "locationType": "province", "name": "Rabat", "neighbourIds": [104, 105, 106, 107, 224], "ownerId": 10, "population": 100000, "productionType": 6}, {"borders": [[597.0, 1819.0, 439.0, 1819.0, 432.0, 1820.0, 440.0, 1800.0, 454.0, 1787.0, 461.0, 1772.0, 472.0, 1770.0, 478.0, 1769.0, 490.0, 1761.0, 504.0, 1757.0, 508.0, 1749.0, 524.0, 1745.0, 530.0, 1752.0, 536.0, 1744.0, 544.0, 1738.0, 549.0, 1739.0, 552.0, 1743.0, 557.0, 1750.0, 561.0, 1745.0, 568.0, 1744.0, 572.0, 1740.0, 580.0, 1758.0, 584.0, 1761.0, 593.0, 1789.0]], "capital": [544.0, 1764.0], "configuredProductions": {"0": 2900.0}, "id": 330, "locationType": "province", "name": "<PERSON><PERSON><PERSON><PERSON>", "neighbourIds": [108, 109, 111, 217], "ownerId": 10, "population": 100000}, {"capital": [966.0, 708.0], "id": 331, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [26, 129, 254, 255, 256, 333]}, {"capital": [734.0, 511.0], "id": 332, "locationType": "sea-point", "name": "North Sea", "neighbourIds": [282, 283, 285, 286, 287]}, {"capital": [927.0, 725.0], "id": 333, "locationType": "sea-point", "name": "sea", "neighbourIds": [25, 26, 252, 254, 331, 334]}, {"capital": [894.0, 707.0], "id": 334, "locationType": "sea-point", "name": "sea", "neighbourIds": [136, 252, 253, 254, 333]}], "mapId": "8436_4", "modId": 1, "players": [{"capitalId": 116, "flagFileName": "1_flag.png", "fullTitle": "King von Great Britain", "id": 1, "name": "<PERSON>", "nationAdjective": "British", "nationDifficulty": "EASY", "nationName": "Great Britain", "nationality": 1, "portraitFileName": "1_portrait.png", "primaryColor": [246, 130, 77], "secondaryColor": [255, 150, 90], "teamId": 1, "title": "King"}, {"capitalId": 77, "flagFileName": "2_flag.png", "fullTitle": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "id": 2, "name": "<PERSON>", "nationAdjective": "French", "nationDifficulty": "EASY", "nationName": "France", "nationality": 2, "portraitFileName": "2_portrait.png", "primaryColor": [151, 36, 24], "secondaryColor": [164, 25, 10], "teamId": 1, "title": "Président"}, {"capitalId": 4, "flagFileName": "3_flag.png", "fullTitle": "<PERSON> von The German Empire", "id": 3, "name": "<PERSON>", "nationAdjective": "German", "nationDifficulty": "EASY", "nationName": "The German Empire", "nationality": 3, "portraitFileName": "3_portrait.png", "primaryColor": [70, 128, 118], "secondaryColor": [60, 122, 100], "teamId": 2, "title": "Kaiser"}, {"capitalId": 64, "flagFileName": "4_flag.png", "fullTitle": "<PERSON>", "id": 4, "name": "<PERSON>", "nationAdjective": "Italian", "nationDifficulty": "EASY", "nationName": "Italy", "nationality": 4, "portraitFileName": "4_portrait.png", "primaryColor": [241, 221, 81], "secondaryColor": [250, 225, 55], "teamId": 1, "title": "<PERSON>"}, {"capitalId": 155, "flagFileName": "5_flag.png", "fullTitle": "<PERSON><PERSON> von <PERSON>", "id": 5, "name": "<PERSON>", "nationAdjective": "Russian", "nationDifficulty": "EASY", "nationName": "Russia", "nationality": 5, "portraitFileName": "5_portrait.png", "primaryColor": [23, 84, 21], "secondaryColor": [33, 150, 31], "teamId": 1, "title": "<PERSON><PERSON>"}, {"capitalId": 130, "flagFileName": "6_flag.png", "fullTitle": "<PERSON>", "id": 6, "name": "<PERSON>", "nationAdjective": "Swedish", "nationDifficulty": "EASY", "nationName": "Sweden", "nationality": 6, "portraitFileName": "6_portrait.png", "primaryColor": [120, 102, 205], "secondaryColor": [130, 110, 220], "teamId": 3, "title": "<PERSON>"}, {"capitalId": 33, "flagFileName": "7_flag.png", "fullTitle": "<PERSON> von The Ottoman Empire", "id": 7, "name": "<PERSON><PERSON><PERSON>", "nationAdjective": "Ottoman", "nationDifficulty": "EASY", "nationName": "The Ottoman Empire", "nationality": 7, "portraitFileName": "7_portrait.png", "primaryColor": [169, 130, 89], "secondaryColor": [189, 150, 109], "teamId": 2, "title": "<PERSON>"}, {"capitalId": 17, "flagFileName": "8_flag.png", "fullTitle": "<PERSON> von Austria-Hungary", "id": 8, "name": "<PERSON>", "nationAdjective": "Austro-Hungarian", "nationDifficulty": "EASY", "nationName": "Austria-Hungary", "nationality": 8, "portraitFileName": "8_portrait.png", "primaryColor": [176, 176, 176], "secondaryColor": [197, 197, 197], "teamId": 2, "title": "Kaiser"}, {"capitalId": 89, "flagFileName": "9_flag.png", "fullTitle": "<PERSON>", "id": 9, "name": "Alfonso XIII", "nationAdjective": "Spanish", "nationDifficulty": "EASY", "nationName": "Spain", "nationality": 9, "portraitFileName": "9_portrait.png", "primaryColor": [11, 11, 154], "secondaryColor": [40, 40, 154], "teamId": 3, "title": "Rey"}, {"capitalId": 107, "flagFileName": "10_flag.png", "fullTitle": "<PERSON><PERSON><PERSON> von <PERSON>", "id": 10, "name": "<PERSON><PERSON><PERSON>", "nationAdjective": "Moroccan", "nationDifficulty": "EASY", "nationName": "Morocco", "nationality": 10, "portraitFileName": "10_portrait.png", "primaryColor": [80, 180, 80], "secondaryColor": [100, 195, 100], "teamId": 3, "title": "Sultân"}, {"capitalId": 29, "computerPlayer": true, "flagFileName": "11_flag.png", "fullTitle": "<PERSON> von Bulgaria", "id": 11, "name": "<PERSON>", "nationAdjective": "Bulgarian", "nationDifficulty": "EASY", "nationName": "Bulgaria", "nationality": 11, "portraitFileName": "11_portrait.png", "primaryColor": [235, 150, 130], "secondaryColor": [250, 160, 140], "teamId": 2, "title": "Tsar"}, {"capitalId": 54, "computerPlayer": true, "flagFileName": "12_flag.png", "fullTitle": "<PERSON><PERSON><PERSON><PERSON>", "id": 12, "name": "<PERSON>", "nationAdjective": "Greek", "nationDifficulty": "EASY", "nationName": "Greece", "nationality": 12, "portraitFileName": "12_portrait.png", "primaryColor": [120, 170, 224], "secondaryColor": [143, 180, 224], "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"capitalId": 146, "computerPlayer": true, "flagFileName": "13_flag.png", "fullTitle": "<PERSON><PERSON><PERSON> von <PERSON>", "id": 13, "name": "<PERSON><PERSON><PERSON>", "nationAdjective": "Polish", "nationDifficulty": "EASY", "nationName": "Poland", "nationality": 13, "portraitFileName": "13_portrait.png", "primaryColor": [190, 102, 135], "secondaryColor": [200, 112, 145], "title": "<PERSON><PERSON><PERSON>"}, {"capitalId": 73, "computerPlayer": true, "flagFileName": "14_flag.png", "fullTitle": "<PERSON><PERSON> von <PERSON>", "id": 14, "name": "<PERSON>", "nationAdjective": "Belgian", "nationDifficulty": "EASY", "nationName": "Belgium", "nationality": 14, "portraitFileName": "14_portrait.png", "primaryColor": [255, 197, 187], "secondaryColor": [255, 217, 207], "title": "Koning"}, {"capitalId": 24, "computerPlayer": true, "flagFileName": "15_flag.png", "fullTitle": "<PERSON><PERSON>", "id": 15, "name": "<PERSON>", "nationAdjective": "Danish", "nationDifficulty": "EASY", "nationName": "Denmark", "nationality": 15, "portraitFileName": "15_portrait.png", "primaryColor": [239, 190, 80], "secondaryColor": [252, 210, 95], "teamId": 3, "title": "<PERSON><PERSON>"}, {"capitalId": 45, "computerPlayer": true, "flagFileName": "16_flag.png", "fullTitle": "<PERSON><PERSON><PERSON>", "id": 16, "name": "<PERSON>", "nationAdjective": "Serb", "nationDifficulty": "EASY", "nationName": "Serbia", "nationality": 16, "portraitFileName": "16_portrait.png", "primaryColor": [95, 106, 153], "secondaryColor": [105, 118, 166], "title": "<PERSON><PERSON><PERSON>"}, {"capitalId": 48, "computerPlayer": true, "flagFileName": "17_flag.png", "fullTitle": "<PERSON><PERSON> von Rumania", "id": 17, "name": "<PERSON>", "nationAdjective": "Romanian", "nationDifficulty": "EASY", "nationName": "Romania", "nationality": 17, "portraitFileName": "17_portrait.png", "primaryColor": [250, 190, 130], "secondaryColor": [255, 200, 140], "title": "Rege"}, {"capitalId": 101, "computerPlayer": true, "flagFileName": "18_flag.png", "fullTitle": "<PERSON><PERSON>", "id": 18, "name": "<PERSON>", "nationAdjective": "Portuguese", "nationDifficulty": "EASY", "nationName": "Portugal", "nationality": 18, "portraitFileName": "18_portrait.png", "primaryColor": [205, 60, 58], "secondaryColor": [220, 68, 64], "title": "Presidente"}], "teams": [{"id": 1, "name": "Entente", "teamColor": [50, 40, 120]}, {"id": 2, "name": "Central Powers", "teamColor": [120, 40, 50]}, {"id": 3, "name": "Neutral", "teamColor": [50, 120, 40]}], "version": 60, "width": 1887}