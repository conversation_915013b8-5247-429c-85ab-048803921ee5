using Stratkit.Entities.HybridBaker;
using Unity.Entities;
using UnityEngine;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Exposes the ui component to ECS.
    /// Manually attach this on <see cref="EndGameUI"/> prefab.
    /// </summary>
    [DisallowMultipleComponent, RequireComponent(typeof(ConvertGameObjectToEntity))]
    public class EndGameUIAuthoring : AGameObjectToEntityAuthoring {
        public override void ConvertToEcsComponent(Entity entity, EntityManager entityManager) =>
            entityManager.AddComponentData(entity, GetComponent<EndGameUI>());
    }
}
