using S1914.Ui.Requests;
using Unity.Entities;

namespace S1914.Ui.EndGame.InitializationRequests {
    /// <summary>
    /// Initialization request for <see cref="EndGameRankingUI"/>.
    /// </summary>
    public class EndGameRankingInitializationRequest : InitializationRequestBase {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public EndGameRankingInitializationRequest() : base(null!) {}

        /// <summary>
        /// Constructor.
        /// </summary>
        /// <param name="ui">the ui</param>
        private EndGameRankingInitializationRequest(EndGameRankingUI ui) : base(ui.gameObject) {}

        /// <summary>
        /// Triggers the initialization request.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="ui">the ui</param>
        public static void Trigger(EntityManager em, EndGameRankingUI ui) {
            EndGameRankingInitializationRequest request = new(ui);
            em.AddComponentData(em.CreateEntity(), request);
        }
    }
}
