using S1914.Ui.Models;
using S1914.Ui.Requests;
using Unity.Entities;

namespace S1914.Ui.EndGame.InitializationRequests {
    /// <summary>
    /// Initialization request for <see cref="EndGameSummaryUI"/>.
    /// </summary>
    public class EndGameSummaryInitializationRequest : InitializationRequestBase {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public EndGameSummaryInitializationRequest() : base(null!) {
        }

        /// <summary>
        /// Constructor.
        /// </summary>
        /// <param name="ui">the ui</param>
        private EndGameSummaryInitializationRequest(EndGameSummaryUI ui) : base(ui.gameObject) {
        }

        /// <summary>
        /// Triggers the initialization request.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="ui">the ui</param>
        public static void Trigger(EntityManager em, EndGameSummaryUI ui) {
            EndGameSummaryInitializationRequest request = new(ui);
            em.AddComponentData(em.CreateEntity(), request);
        }
    }
}
