using S1914.Ui.Models;
using S1914.Ui.Models.Rank;
using S1914.Ui.Models.User;
using Stratkit.ContentItemsModel;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Model for the end game summary UI tab
    /// This model contains the user information, end game stats, and rank progress.
    /// </summary>
    public class EndGameSummaryModel : ModelBase {
        public UserModel User { get; }
        public EndGameSummaryStatsModel Stats { get; }
        public UserRankProgressModel RankProgress { get; }

        /// <summary>
        /// Constructor for the EndGameSummaryModel
        /// Initializes the user, rank progress, and stats models.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <param name="persistentEm">Persistent Entity Manager</param>
        /// <param name="em">Entity Manager of system's world</param>
        /// <param name="user">User entity</param>
        /// <param name="userStats">User stats entity</param>
        public EndGameSummaryModel(IContentItemsDbManaged db, EntityManager persistentEm, EntityManager em, Entity user,
            Entity userStats) {
            User = new UserModel(db, persistentEm, user);
            RankProgress = new UserRankProgressModel(db, persistentEm, user);
            Stats = new EndGameSummaryStatsModel(db, em, userStats);
        }
    }
}
