using S1914.Ui.Models;
using Stillfront.Logging;
using Stratkit.ContentItemsModel.Data;
using Stratkit.Entities;
using Stratkit.GameEnd;
using Stratkit.GameInfoStateLoader;
using Unity.Entities;
using Unity.Mathematics;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Model for <see cref="EndGameUI"/>
    /// </summary>
    public class EndGameModel : ModelBase {
        public int DayOfGame { get; private set; }
        public PlayerModel Player { get; }
        public bool IsDefeat { get; private set; }
        private int[] VictoryRewardBoosts { get; set; } = null!;
        private int[] TeamVictoryRewardBoosts { get; set; } = null!;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="em"></param>
        /// <param name="gameInfoStateEntity"></param>
        /// <param name="gameEndDataEntity"></param>
        /// <param name="currentDayRefEntity"></param>
        public EndGameModel(
            EntityManager em,
            Entity gameInfoStateEntity,
            Entity gameEndDataEntity,
            Entity currentDayRefEntity) {
            GameInfoComponent gameInfo = em.GetComponentData<GameInfoComponent>(gameInfoStateEntity);
            GameEndData gameEndData = em.GetComponentData<GameEndData>(gameEndDataEntity);

            DayOfGame = (int)gameInfo.DayOfGame;

            Entity playerEntity = gameEndData.PlayerEntity;
            Player = new PlayerModelBuilder(em, playerEntity).WithRankingInfo(currentDayRefEntity).Build();

            IsDefeat = gameEndData.Defeat;

            SetRewards(em, gameInfo);
        }

        /// <summary>
        /// Sets the rewards based on the scenario's reward boosts.
        /// </summary>
        /// <param name="em">Entity Manager</param>
        /// <param name="gameInfo">GameInfoComponent</param>
        private void SetRewards(EntityManager em, GameInfoComponent gameInfo) {
            Entity scenarioEntity = gameInfo.ScenarioRef;
            if (!em.TryGetComponent(scenarioEntity, out ScenarioRewardBoostsComponent? scenarioRewardBoostsComponent)) {
                Log.Warning(
                    $"{nameof(EndGameModel)} can not setup rewards. Scenario {nameof(scenarioEntity)} has no {nameof(ScenarioRewardBoostsComponent)} attached to it");
                // ReSharper disable once UseArrayEmptyMethod
                VictoryRewardBoosts = new int[0];
                // ReSharper disable once UseArrayEmptyMethod
                TeamVictoryRewardBoosts = new int[0];
                return;
            }

            VictoryRewardBoosts = scenarioRewardBoostsComponent.VictoryRewardBoosts;
            TeamVictoryRewardBoosts = scenarioRewardBoostsComponent.TeamVictoryRewardBoosts;
        }

        /// <summary>
        /// Get the reward for a given place.
        /// If the place is not defined in the reward boosts, it means player got no reward.
        /// </summary>
        /// <returns>Amount of reward</returns>
        public int GetReward() {
            int[] victoryRewardBoosts = Player.RankingEntry!.IsTeam() ? TeamVictoryRewardBoosts : VictoryRewardBoosts;

            // -1 because position is 1-based
            int rewardIndex = math.max(0, Player.RankingEntry.Position - 1);
            return rewardIndex < victoryRewardBoosts.Length
                ? victoryRewardBoosts[Player.RankingEntry.Position - 1]
                : 0;
        }
    }
}
