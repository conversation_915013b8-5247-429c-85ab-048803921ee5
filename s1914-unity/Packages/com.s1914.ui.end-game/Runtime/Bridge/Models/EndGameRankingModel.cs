using S1914.Ui.Models;
using Stratkit.PlayerAndTeamRanking.Data;
using System.Collections.Generic;
using System.Linq;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Model for ranking in the end game UI
    /// This model contains all the ranking entries that are displayed in the end game UI.
    /// </summary>
    public class EndGameRankingModel : ModelBase {
        public List<RankingEntryEffectiveModel> RankingModels { get; }

        /// <summary>
        /// constructs a new thing
        /// </summary>
        /// <param name="em">all the ranking entries</param>
        /// <param name="entries"></param>
        public EndGameRankingModel(EntityManager em, List<RankingEntryEffective> entries) {
            RankingModels = GetRankingEntryModels(em, entries);
        }

        /// <summary>
        /// Retrieves the ranking entry models from the given entries
        /// </summary>
        /// <param name="em">Entity Manager</param>
        /// <param name="entries">List of RankingEntriesEffective</param>
        /// <returns>List of RankingEntryEffective Models</returns>
        private List<RankingEntryEffectiveModel> GetRankingEntryModels(EntityManager em, List<RankingEntryEffective> entries) =>
            entries.Select(entry => RankingEntryEffectiveModel.CreateModel(entry, em)).ToList();
    }
}
