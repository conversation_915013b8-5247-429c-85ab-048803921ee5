using S1914.Ui.Models;
using Stillfront.Logging;
using Stratkit.ContentItemsModel;
using Stratkit.ContentItemsModel.Generated;
using Stratkit.StatisticsStateLoader;
using System;
using System.Collections.Generic;
using System.Linq;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Model for the end game summary stats UI.
    /// This model contains all the end game stats that are displayed in the end game summary stats
    /// </summary>
    public class EndGameSummaryStatsModel : ModelBase {
        public Dictionary<EndGameStatsType, EndGameStatsEntryModel> EndGameStats = new();
        private readonly Dictionary<int, int> _unitStatsColumnIdMap = new();

        /// <summary>
        /// Constructs a new EndGameSummaryStatsModel.
        /// This model is used to display the end game stats in the end game summary UI.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <param name="em">Entity Manager</param>
        /// <param name="userStats">Users stats entity</param>
        public EndGameSummaryStatsModel(IContentItemsDbManaged db, EntityManager em, Entity userStats) {
            if (!em.HasComponent<PlayerStatsTag>(userStats)) {
                Log.Error("EndGameSummaryStatsModel: Entity does not have PlayerStatsTag component.");
                return;
            }
            Dictionary<int, int> unitStatsColumnIdMap = GetUnitStatsColumnIdMap(db);
            ConstructDestroyStats(db, em, userStats, unitStatsColumnIdMap);
            ConstructCreatedStats(db, em, userStats, unitStatsColumnIdMap);
        }

        /// <summary>
        /// Gets a map of unit stats column IDs to content item IDs.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <returns>Dictionary(map) from StatsColumnId to ContentItemId</returns>
        private Dictionary<int, int> GetUnitStatsColumnIdMap(IContentItemsDbManaged db) {
            if (_unitStatsColumnIdMap.Count != 0) {
                return _unitStatsColumnIdMap;
            }

            ModContent mod = db.GetActiveMod();
            // iterate over each unit type and create map of StatsColumnId to ContentItemId.
            foreach (ContentItemId unitId in mod.Units) {
                UnitContent unit = db.GetContent<UnitContent>(unitId);
                _unitStatsColumnIdMap[unit.StatsColumnID] = unit.ContentItemId;
            }

            return _unitStatsColumnIdMap;
        }

        /// <summary>
        /// Constructs the destroy stats for the end game summary.
        /// This includes total units destroyed, epic units destroyed, and unit most destroyed.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <param name="em">Entity Manager</param>
        /// <param name="userStats">Users stats entity</param>
        /// <param name="unitStatsColumnIdMap">StatsColumnId to ContentItemId map</param>
        private void ConstructDestroyStats(IContentItemsDbManaged db, EntityManager em, Entity userStats,
            Dictionary<int, int> unitStatsColumnIdMap) {
            if (em.HasBuffer<PlayerStatEntry<PlayerStatProvincesCaptured>>(userStats)) {
                EndGameStats[EndGameStatsType.ProvincesCaptured] = new EndGameStatsEntryModel(EndGameStatsType.ProvincesCaptured,
                    em.GetBuffer<PlayerStatEntry<PlayerStatProvincesCaptured>>(userStats).Sum());
            }

            if (!em.HasBuffer<PlayerStatEntry<PlayerStatUnitsKilled>>(userStats)) {
                return;
            }

            (int total, int epic, int mostTypeId, int mostCount) = CalculateUnitStats<PlayerStatUnitsKilled>(
                db, em, userStats, unitStatsColumnIdMap,
                unitContent => unitContent.RatingConfig is { EpicUnit: true },
                statEntry => statEntry.StatColumnId
            );
            EndGameStats[EndGameStatsType.UnitsDestroyed] = new EndGameStatsEntryModel(EndGameStatsType.UnitsDestroyed, total);
            EndGameStats[EndGameStatsType.EpicUnitsDestroyed] = new EndGameStatsEntryModel(EndGameStatsType.EpicUnitsDestroyed, epic);
            UnitTypeModel unitTypeModel = new(db, new ContentItemId(ContentItemsIdSpace.BackEnd, mostTypeId));
            EndGameStats[EndGameStatsType.UnitMostDestroyed] = new EndGameStatsEntryModel(EndGameStatsType.UnitMostDestroyed, mostCount, unitTypeModel);
        }

        /// <summary>
        /// Constructs the created stats for the end game summary.
        /// This includes total buildings constructed, total units produced, epic units produced, and unit most produced.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <param name="em">Entity Manager</param>
        /// <param name="userStats">Users stats entity</param>
        /// <param name="unitStatsColumnIdMap">StatsColumnId to ContentItemId map</param>
        private void ConstructCreatedStats(IContentItemsDbManaged db, EntityManager em, Entity userStats,
            Dictionary<int, int> unitStatsColumnIdMap) {
            // Total buildings constructed
            if (em.HasBuffer<PlayerStatEntry<PlayerStatModdableUpgradeConstructed>>(userStats)) {
                EndGameStats[EndGameStatsType.BuildingsConstructed] = new EndGameStatsEntryModel(
                    EndGameStatsType.BuildingsConstructed,
                    em.GetBuffer<PlayerStatEntry<PlayerStatModdableUpgradeConstructed>>(userStats).Sum());
            }

            if (!em.HasBuffer<PlayerStatEntry<PlayerStatUnitsProduced>>(userStats)) {
                return;
            }

            ContentItemId modId = db[ContentItemsTableId.Mod].Rows.First();
            ModContent mod = db.GetContent<ModContent>(modId);

            (int total, int epic, int mostTypeId, int mostCount) = CalculateUnitStats<PlayerStatUnitsProduced>(
                db, em, userStats, unitStatsColumnIdMap,
                unitContent => unitContent.RatingConfig is { EpicUnit: true },
                statEntry => statEntry.StatColumnId,
                unitId => unitId == mod.DefaultInfantryUnitId
            );
            EndGameStats[EndGameStatsType.UnitsProduced] =
                new EndGameStatsEntryModel(EndGameStatsType.UnitsProduced, total);
            EndGameStats[EndGameStatsType.EpicUnitsProduced] =
                new EndGameStatsEntryModel(EndGameStatsType.EpicUnitsProduced, epic);
            if (mostTypeId == 0) {
                return;
            }

            UnitTypeModel unitTypeModel = new(db, new ContentItemId(ContentItemsIdSpace.BackEnd, mostTypeId));
            EndGameStats[EndGameStatsType.UnitMostProduced] =
                new EndGameStatsEntryModel(EndGameStatsType.UnitMostProduced, mostCount, unitTypeModel);
        }

        /// <summary>
        /// Static generic method to calculate unit stats.
        /// </summary>
        /// <param name="db">IContentItemsDbManaged</param>
        /// <param name="em">Entity Manager</param>
        /// <param name="userStats">Users stats entity</param>
        /// <param name="unitStatsColumnIdMap">StatsColumnId to ContentItemId map</param>
        /// <param name="epicPredicate">Predicate for epic unit</param>
        /// <param name="getUnitStatColumnId">Predicate for getting unit id</param>
        /// <param name="skipUnitIdPredicate">Predicate for skipping unit of specific id</param>
        /// <typeparam name="TStatEntry">Type of IPlayerStatEntry</typeparam>
        /// <returns>Total amount, epic, most killed/produced type id, most count</returns>
        private static (int total, int epic, int mostTypeId, int mostCount) CalculateUnitStats<TStatEntry>(
            IContentItemsDbManaged db,
            EntityManager em,
            Entity userStats,
            Dictionary<int, int> unitStatsColumnIdMap,
            Func<UnitContent, bool> epicPredicate,
            Func<TStatEntry, int> getUnitStatColumnId,
            Func<int, bool>? skipUnitIdPredicate = null
        ) where TStatEntry : unmanaged, IPlayerStatEntry
        {
            int total = 0;
            int epic = 0;
            Dictionary<int, int> unitTypeCount = new();

            foreach (PlayerStatEntry<TStatEntry> stat in em.GetBuffer<PlayerStatEntry<TStatEntry>>(userStats)) {
                int statColumnId = getUnitStatColumnId(stat.Data);
                if (!unitStatsColumnIdMap.TryGetValue(statColumnId, out int unitTypeId)) {
                    Log.Error("EndGameSummaryStatsModel: Could not find unit for stat column id: " + statColumnId);
                    continue;
                }

                ContentItemId unitTypeContentId = new(ContentItemsIdSpace.BackEnd, unitTypeId);
                UnitContent unitContent = db.GetContent<UnitContent>(unitTypeContentId);

                total += stat.Count;
                if (epicPredicate(unitContent)) {
                    epic += stat.Count;
                }

                if (skipUnitIdPredicate != null && skipUnitIdPredicate(unitTypeId)) {
                    continue;
                }

                if (!unitTypeCount.TryAdd(unitTypeId, stat.Count)) {
                    unitTypeCount[unitTypeId] += stat.Count;
                }
            }

            KeyValuePair<int, int> most = unitTypeCount.OrderByDescending(kv => kv.Value).FirstOrDefault();
            return (total, epic, most.Key, most.Value);
        }
    }
}
