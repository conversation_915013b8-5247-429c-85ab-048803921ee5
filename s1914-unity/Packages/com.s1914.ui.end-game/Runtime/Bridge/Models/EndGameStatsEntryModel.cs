using S1914.Ui.Models;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Class representing the data for a single end game statistic.
    /// This class is used to store the amount of a specific statistic and optionally the unit type
    /// associated with that statistic.
    /// </summary>
    public class EndGameStatsEntryModel : ModelBase{
        public EndGameStatsType StatType { get; }
        public int Amount { get; }
        public UnitTypeModel? UnitType { get; }

        /// <summary>
        /// Constructs a new instance of <see cref="EndGameStatsEntryModel"/> with the specified amount.
        /// </summary>
        /// <param name="statType">Stat type</param>
        /// <param name="amount">Amount value of statistic</param>
        public EndGameStatsEntryModel(EndGameStatsType statType, int amount) {
            StatType = statType;
            Amount = amount;
        }

        /// <summary>
        /// Constructs a new instance of <see cref="EndGameStatsEntryModel"/> with the specified amount and unit type.
        /// </summary>
        /// <param name="statType">Stat type</param>
        /// <param name="amount">Amount value of statistic</param>
        /// <param name="unitType">Unit type related to statistic</param>
        public EndGameStatsEntryModel(EndGameStatsType statType, int amount, UnitTypeModel? unitType) {
            StatType = statType;
            Amount = amount;
            UnitType = unitType;
        }
    }
}
