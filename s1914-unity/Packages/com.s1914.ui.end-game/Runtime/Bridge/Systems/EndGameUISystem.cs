using S1914.Ui.EndGame.InitializationRequests;
using S1914.Ui.MUS;
using S1914.Ui.Requests;
using S1914.Ui.Requests.ToggleVisibilityRequests;
using S1914.Ui.UberGamesList;
using Stratkit.Entities.Bridge;
using Stratkit.Entities.Managed;
using Stratkit.GameEnd;
using Stratkit.GameInfoStateLoader;
using Stratkit.NewspaperStateLoader;
using Stratkit.WorldReference;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// System responsible for managing <see cref="EndGameUI"/>.
    /// </summary>
    public sealed partial class EndGameUISystem : UIMainSystem<
        EndGameUI,
        EndGameOpenRequest,
        EndGameModel,
        EndGameUIModuleConfig> {
        /// <inheritdoc/>
        protected override void OnCreate() {
            base.OnCreate();

            RequireForUpdate<GameInfoComponent>();
            RequireForUpdate<GameEndData>();
            RequireForUpdate<NewspaperCurrentDayRef>();
        }

        /// <inheritdoc/>
        protected override EndGameModel CreateModel(EndGameOpenRequest request) {
            return new EndGameModel(
                EntityManager,
                SystemAPI.GetSingletonEntity<GameInfoComponent>(),
                SystemAPI.GetSingletonEntity<GameEndData>(),
                SystemAPI.GetSingleton<NewspaperCurrentDayRef>().Value);
        }

        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(EndGameUI ui) {
            ui.OnUICreatedEvent += BridgeDelegate.Create<BaseHybridComponent>(this, (tabUI) => OnTabUICreated(ui, tabUI));
            ui.OnGoToGameListEvent += BridgeDelegate.Create(this, OnOpenGameList);
        }

        /// <summary>
        /// Method called when the user wants to display game list.
        /// </summary>
        private void OnOpenGameList() {
            UberShowHideRequest.TriggerShowGamesList(WorldReferenceUtils.PersistentWorld.EntityManager, (int)GameListMode.NewGames);
        }

        /// <summary>
        /// Called when a tab UI instance gets created for the first time.
        /// It creates a new initialization request.
        /// </summary>
        /// <param name="parentUI">owner of the tab</param>
        /// <param name="tabUI">tab UI</param>
        private void OnTabUICreated(EndGameUI parentUI, BaseHybridComponent tabUI) {
            if (tabUI.TryGetComponent(out EndGameSummaryUI summaryUI)) {
                EndGameSummaryInitializationRequest.Trigger(EntityManager, summaryUI);
            } else if (tabUI.TryGetComponent(out EndGameRankingUI rankingUI)) {
                EndGameRankingInitializationRequest.Trigger(EntityManager, rankingUI);
            }
        }
    }
}
