using S1914.Ui.EndGame.InitializationRequests;
using S1914.Ui.MUS;
using Stratkit.ContentItemsModel.Data;
using Stratkit.PlayerStateLoader;
using Stratkit.Properties.Loader;
using Stratkit.StatisticsStateLoader;
using Stratkit.WebApi.GetUser;
using Stratkit.WorldReference;
using Unity.Collections;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// System for the end game summary UI.
    /// This system is responsible for creating the end game summary UI tab and its model.
    /// It initializes the UI with the necessary data from the persistent world and the current user.
    /// </summary>
    public partial class EndGameSummaryUISystem : UISubSystem<
        EndGameSummaryUI,
        EndGameSummaryInitializationRequest,
        EndGameSummaryModel> {
        private EntityQuery _currentUserQuery;
        private EntityQuery _persistentContentItemDbQuery;
        private EntityQuery _playerStatsQuery;

        private EntityManager _persistentEntityManager;

        /// <inheritdoc/>
        protected override void OnCreate() {
            base.OnCreate();

            _persistentEntityManager = WorldReferenceUtils.PersistentWorld.EntityManager;
            _currentUserQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<UserDataTag>()
                .WithAll<MyUserTag>()
                .Build(_persistentEntityManager);
            _persistentContentItemDbQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<ContentItemsVersioned>()
                .Build(_persistentEntityManager);
            _playerStatsQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<MyPlayerTag>()
                .WithAll<PlayerStatsTag>()
                .Build(this);

            RequireForUpdate(_playerStatsQuery);
        }

        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(EndGameSummaryUI ui) {}

        /// <inheritdoc/>
        protected override EndGameSummaryModel CreateModel(EndGameSummaryInitializationRequest openRequest) =>
            new(
                _persistentContentItemDbQuery.GetSingleton<ContentItemsVersioned>(),
                _persistentEntityManager,
                EntityManager,
                _currentUserQuery.GetSingletonEntity(),
                _playerStatsQuery.GetSingletonEntity());
    }
}
