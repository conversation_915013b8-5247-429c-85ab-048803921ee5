using S1914.Ui.EndGame.InitializationRequests;
using S1914.Ui.MUS;
using Stratkit.NewspaperStateLoader;
using Stratkit.PlayerAndTeamRanking;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// System for the end game ranking UI.
    /// This system is responsible for creating the end game ranking UI tab and its model.
    /// </summary>
    public partial class EndGameRankingUISystem : UISubSystem<
        EndGameRankingUI,
        EndGameRankingInitializationRequest,
        EndGameRankingModel> {
        /// <inheritdoc/>
        protected override void OnCreate() {
            base.OnCreate();
            RequireForUpdate<NewspaperCurrentDayRef>();
        }

        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(EndGameRankingUI ui) {}

        /// <inheritdoc/>
        protected override EndGameRankingModel CreateModel(EndGameRankingInitializationRequest openRequest) {
            Entity day = SystemAPI.GetSingleton<NewspaperCurrentDayRef>().Value;
            return new EndGameRankingModel(EntityManager,
                PlayerAndTeamRanking.GetEffectiveRankingEntries(day, EntityManager));
        }
    }
}
