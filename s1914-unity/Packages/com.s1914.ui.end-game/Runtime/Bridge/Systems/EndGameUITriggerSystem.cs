using S1914.Ui.Requests;
using S1914.Ui.Requests.ArmyBar;
using S1914.Ui.Requests.ProvinceBar;
using Stratkit.GameEnd;
using Unity.Entities;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// System responsible for triggering the end game UI.
    /// Checks for the presence of end game data and end game UI, and initiates opening the UI when required.
    /// </summary>
    public partial struct EndGameUITriggerSystem : ISystem {
        private EntityQuery _gameEndDataQuery;
        private EntityQuery _gameEndUIQuery;

        private bool _isRequested;

        /// <summary>
        /// Creates queries for end game data and end game UI, and sets system update requirements.
        /// </summary>
        /// <param name="state">The ECS system state.</param>
        public void OnCreate(ref SystemState state) {
            _gameEndDataQuery = SystemAPI
                .QueryBuilder()
                .WithAll<GameEndData>()
                .Build();
            _gameEndUIQuery = SystemAPI
                .QueryBuilder()
                .WithAll<EndGameUI>()
                .Build();

            _gameEndDataQuery.SetChangedVersionFilter(ComponentType.ReadOnly<GameEndData>());
            state.RequireForUpdate(_gameEndDataQuery);
        }

        /// <summary>
        /// Checks if the end game UI should be opened and triggers the appropriate action if necessary.
        /// </summary>
        /// <param name="state">The ECS system state.</param>
        public void OnUpdate(ref SystemState state) {
            if (!ShouldRequestEndGame()) {
                return;
            }

            TriggerHUDVisibility(ref state);

            EndGameOpenRequest.Trigger(state.EntityManager);
            _isRequested = true;
        }

        private bool ShouldRequestEndGame() => !SystemAPI.ManagedAPI.HasSingleton<EndGameOpenRequest>() &&
                                       _gameEndUIQuery.IsEmpty && !_isRequested;

        /// <summary>
        /// Invoke triggers to show or hide the HUD elements based on the end game state.
        /// </summary>
        /// <param name="state">The ECS system state.</param>
        private void TriggerHUDVisibility(ref SystemState state) {
            ArmyBarShowHideRequest.Trigger(state.EntityManager, false);
            ProvinceBarShowHideRequest.Trigger(state.EntityManager, false);
            HudTopToggleRequest.Trigger(state.EntityManager, false);
            HudBottomToggleRequest.Trigger(state.EntityManager, false);
            DayOfGameToggleRequest.Trigger(state.EntityManager, false);
            InGameNotificationsContainerToggleRequest.Trigger(state.EntityManager, false);
        }
    }
}
