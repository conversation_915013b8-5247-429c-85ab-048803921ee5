using Stillfront.Validators;
using Stratkit.Localization;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Configuration for the end game summary statistics.
    /// This class holds a list of configuration elements that define the statistics to be displayed at the end of the game,
    /// including their type, icon, and localized name.
    /// It provides a method to retrieve the configuration for a specific statistic type.
    /// </summary>
    public class EndGameSummaryStatConfig : ScriptableObject {
        [SerializeField, NotNull]
        public List<EndGameSummaryStatConfigElement> Configs = new();

        /// <summary>
        /// Internal structure representing a configuration element for an end game summary statistic.
        /// </summary>
        [Serializable]
        public struct EndGameSummaryStatConfigElement {
            public EndGameStatsType Type;
            public Sprite Icon;
            public LocalizationKey Name;
        }

        /// <summary>
        /// Retrieves the configuration element for a specific statistic type.
        /// </summary>
        /// <param name="statsType">EndGameStat type</param>
        /// <returns>EndGameSummaryStatConfigElement</returns>
        public EndGameSummaryStatConfigElement GetForStatType(EndGameStatsType statsType) {
            return Configs.Find(c => c.Type == statsType);
        }
    }
}
