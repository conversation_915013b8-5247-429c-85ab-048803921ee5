using S1914.Ui.MUS;
using Stratkit.Entities.Managed;
using Stratkit.Ui.Core;
using S1914.Ui.Common;
using S1914.Ui.Models;
using Stillfront.Validators;
using System;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// MonoBehaviour for the end game ranking UI.
    /// </summary>
    public class EndGameRankingUI : BaseHybridComponent, ICommonSubUI<EndGameRankingModel> {
        public EndGameRankingModel? Model { get; set; }

        [SerializeField, NotNull]
        private RectTransform? _entriesContainer;

        [SerializeField, NotNull]
        private GameRankingEntryPlayerUi? _playerEntryPrefab;

        [SerializeField, NotNull]
        private GameRankingEntryTeamUi? _teamEntryPrefab;

        private AssetContext? _context;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">asset context</param>
        public void Initialize(EndGameRankingModel model, AssetContext context) {
            Model = model;
            _context = context;

            InitializeRankingEntries();
            UpdateLayoutAsync();
        }

        /// <summary>
        /// Initializes the ranking entries based on the provided model.
        /// It instantiates player or team entries based on the type of each ranking model.
        /// </summary>
        private void InitializeRankingEntries() {
            foreach (RankingEntryEffectiveModel rankingModel in Model!.RankingModels) {
                if (!rankingModel.IsTeam()) {
                    GameRankingEntryPlayerUi component = Instantiate(_playerEntryPrefab!, _entriesContainer!);
                    component.Initialize(rankingModel, (AssetContext)_context!);
                } else {
                    GameRankingEntryTeamUi component = Instantiate(_teamEntryPrefab!, _entriesContainer!);
                    component.Initialize(rankingModel, (AssetContext)_context!);
                }
            }
        }

        /// <summary>
        /// Waits for around 2 frames and forces a layout rebuild scroll rect container.
        /// </summary>
        private async void UpdateLayoutAsync() {
            await Task.Delay(TimeSpan.FromSeconds(Time.deltaTime * 2f));
            LayoutRebuilder.ForceRebuildLayoutImmediate(_entriesContainer!.parent.GetComponent<RectTransform>());
        }
    }
}
