using S1914.Ui.Common.Managed;
using S1914.Ui.Common.Rank;
using S1914.Ui.MUS;
using Stillfront.Validators;
using Stratkit.Entities.Managed;
using Stratkit.Ui.Core;
using System.Collections.Generic;
using System.Threading.Tasks;
using TMPro;
using UnityEngine;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// UI component for displaying the end game summary.
    /// This component shows the end game stats, user profile summary, and collapsable entries.
    /// </summary>
    public class EndGameSummaryUI : BaseHybridComponent, ICommonSubUI<EndGameSummaryModel> {
        public EndGameSummaryModel? Model { get; set; }

        [Header("Stats")]
        [SerializeField, NotNull]
        private EndGameSummaryStatsEntryUI _statsEntryUIPrefab = null!;
        [SerializeField, NotNull]
        private Transform _statContainerDestroyed = null!;
        [SerializeField, NotNull]
        private Transform _statContainerCreated = null!;

        [Header("Profile Summary")]
        [Serial<PERSON><PERSON><PERSON>, NotNull]
        private TMP_Text _userNameText = null!;
        [SerializeField, NotNull]
        private UserRankProgressUI _rankProgressUI = null!;

        [SerializeField, NotNull]
        private List<CollapsableEntry> _collapsableEntries = null!;

        private AssetContext? _context;

        /// <summary>
        /// Initializes the end game summary UI with the provided model and context.
        /// </summary>
        /// <param name="model">EndGameSummaryModel</param>
        /// <param name="context">Asset context</param>
        public void Initialize(EndGameSummaryModel model, AssetContext context) {
            Model = model;
            _context = context;

            InitializeSummaryStats();
            InitializeUserProfile();
        }

        /// <summary>
        /// Initializes the summary statistics by creating UI entries for each end game stat.
        /// </summary>
        private void InitializeSummaryStats() {
            foreach (KeyValuePair<EndGameStatsType, EndGameStatsEntryModel> stat in Model!.Stats.EndGameStats) {
                EndGameSummaryStatsEntryUI statsEntryUI = Instantiate(_statsEntryUIPrefab,
                    IsDestructionStat(stat.Key) ? _statContainerDestroyed : _statContainerCreated);
                statsEntryUI.Initialize(stat.Value, (AssetContext)_context!);
            }
        }

        /// <summary>
        /// Initializes the user profile section of the end game summary.
        /// </summary>
        private void InitializeUserProfile() {
            _userNameText.text = Model!.User.Name;
            _rankProgressUI.Initialize(Model!.RankProgress, (AssetContext)_context!);
        }

        /// <summary>
        /// Returns true if the given stats type is related to destruction (e.g., units destroyed, provinces captured).
        /// </summary>
        /// <param name="stats">EndGameStatsType</param>
        /// <returns>True if stats belongs to destruction category</returns>
        private bool IsDestructionStat(EndGameStatsType stats) {
            return stats is EndGameStatsType.ProvincesCaptured or EndGameStatsType.UnitsDestroyed
                or EndGameStatsType.EpicUnitsDestroyed or EndGameStatsType.UnitMostDestroyed;
        }

        /// <summary>
        /// Updates all collapsable entries when the component is enabled.
        /// </summary>
        private void OnEnable() {
            UpdateCollapsablesAsync();
        }

        /// <summary>
        /// Updates all collapsable entries asynchronously.
        /// </summary>
        private async void UpdateCollapsablesAsync() {
            await Task.Yield();
            foreach (CollapsableEntry collapsable in _collapsableEntries) {
                collapsable.RefreshAsync();
            }
        }
    }
}
