using S1914.Ui.Common.Tab.Content;
using S1914.Ui.Common.TabBar;
using S1914.Ui.Common.Util;
using S1914.Ui.MUS;
using Stillfront.Validators;
using Stratkit.Localization;
using Stratkit.Ui.Commons.Utils;
using Stratkit.Ui.Core;
using Stratkit.Ui.Core.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UI;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// A UI is responsible for visualizing <see cref="EndGameModel"/>
    /// Controlled by <see cref="EndGameUISystem"/>
    /// </summary>
    public class EndGameUI : ACloseableDialog, ICommonMainUI<EndGameModel> {
        public event Action<TabbedContentUI>? OnUICreatedEvent;
        public event Action? OnGoToGameListEvent;
        public event Action? OnCloseEvent;
        public EndGameModel? Model { get; set; }
        private AssetContext? _assetContext;

        [Header("Overview Info")]
        [SerializeField, NotNull]
        private TMP_Text _titleText = null!;
        [SerializeField, NotNull]
        private TMP_Text _descriptionText = null!;
        [SerializeField, NotNull]
        private TMP_Text _placeText = null!;
        [SerializeField, NotNull]
        private TMP_Text _victoryPointText = null!;
        [SerializeField, NotNull]
        private TMP_Text _dayOfGameText = null!;
        [SerializeField, NotNull]
        private TMP_Text _typeOfGameText = null!;
        [SerializeField, NotNull]
        private TMP_Text _nationNameText = null!;
        [SerializeField, NotNull]
        private Image _nationFlagImage = null!;

        [Header("Rewards")]
        [SerializeField, NotNull]
        private TMP_Text _rewardText = null!;
        [SerializeField, NotNull]
        private GameObject _rewardContainer = null!;

        [Header("Medals")]
        [SerializeField, NotNull]
        private Image _medalImage = null!;
        [SerializeField, NotNull]
        private Sprite _bronzeMedalSprite = null!;
        [SerializeField, NotNull]
        private Sprite _silverMedalSprite = null!;
        [SerializeField, NotNull]
        private Sprite _goldMedalSprite = null!;
        [SerializeField, NotNull]
        private Sprite _participationMedalSprite = null!;
        [SerializeField, NotNull]
        private Sprite _defeatMedalSprite = null!;

        [Header("Localization Keys")]
        [SerializeField, NotNull]
        private LocalizationKey _titleWinLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _descriptionWinLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _titleDefeatLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _descriptionDefeatLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _placeLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _soloPlayLocalizationKey = null!;
        [SerializeField, NotNull]
        private LocalizationKey _teamPlayLocalizationKey = null!;

        [SerializeField, NotNull]
        private Button _openSummaryButton = null!;
        [SerializeField, NotNull]
        private Button _openUberButton = null!;

        [SerializeField, NotNull]
        private TabbedContentHandlerUI _tabbedContentHandler = null!;
        [SerializeField, NotNull]
        private TabBarUI _tabBar = null!;

        [SerializeField, NotNull]
        private PlayableDirector _director = null!;
        [SerializeField, NotNull]
        private PlayableAsset _transitionPlayableAsset = null!;

        private void Awake() {
            _tabbedContentHandler.OnTabCreated += content => OnUICreatedEvent?.Invoke(content);
        }

        /// <summary>
        /// Initializes the UI with the given model
        /// </summary>
        /// <param name="model">the model</param>
        /// <param name="context">asset context</param>
        public void Initialize(EndGameModel model, AssetContext context) {
            Model = model;
            _assetContext = context;

            _openUberButton.onClick.AddListener(OnGoToGameList);
            _openSummaryButton.onClick.AddListener(OnOpenSummaryButtonClicked);
            _tabbedContentHandler.Initialize();

            UpdateTitleTexts();
            UpdateMedal();
            UpdateOverviewInfo();
            UpdateRewards();
        }

        /// <summary>
        /// Invoked when the user clicks anything on initial EndGame Overview screen.
        /// Starts the transition animation by playing the director with the transition playable asset.
        /// </summary>
        private void OnOpenSummaryButtonClicked() {
            _director.Play(_transitionPlayableAsset);
            _openSummaryButton.onClick.RemoveListener(OnOpenSummaryButtonClicked);
            UpdateTab();
        }

        /// <summary>
        /// Waits around 2 frames for activation of the screen gameObject and then sets the active tab button to the first one.
        /// </summary>
        private async void UpdateTab() {
            await Task.Delay(TimeSpan.FromSeconds(Time.deltaTime * 2f));
            _tabBar.SetActive(_tabBar.GetTabConfigs().First().tabId);
        }

        /// <summary>
        /// Updates the title and description texts based on the game outcome.
        /// </summary>
        private void UpdateTitleTexts() {
            _titleText.text = LocalizationUtils.GetLocalizedString(Model!.IsDefeat ?
                _titleDefeatLocalizationKey : _titleWinLocalizationKey);
            _descriptionText.text = LocalizationUtils.GetLocalizedString(Model.IsDefeat ?
                _descriptionDefeatLocalizationKey : _descriptionWinLocalizationKey);
        }

        /// <summary>
        /// Updates the medal image based on the player's ranking position.
        /// If the player is defeated, it shows the defeat medal.
        /// </summary>
        private void UpdateMedal() {
            _medalImage.sprite = Model!.Player.RankingEntry!.Position switch {
                1 => _goldMedalSprite,
                2 => _silverMedalSprite,
                3 => _bronzeMedalSprite,
                _ => Model.IsDefeat ? _defeatMedalSprite : _participationMedalSprite
            };
        }

        /// <summary>
        /// Updates the overview info displayed in the UI.
        /// </summary>
        private void UpdateOverviewInfo() {
            _placeText.text = LocalizationUtils.GetLocalizedString(_placeLocalizationKey,
                NumberFormatter.FormatOrdinal(Model!.Player.RankingEntry!.Position));
            _victoryPointText.text = NumberFormatter.FormatNumber(Model!.Player.Points);
            _dayOfGameText.text = NumberFormatter.FormatNumber(Model!.DayOfGame);
            _typeOfGameText.text = LocalizationUtils.GetLocalizedString(Model!.Player.RankingEntry!.IsTeam() ?
                _teamPlayLocalizationKey : _soloPlayLocalizationKey);
            _nationNameText.text = Model!.Player.NationName;

            UiCommonUtils.SetSpriteInBackground((AssetContext)_assetContext!, _nationFlagImage, Model!.Player.NationFlagIcon);
        }

        /// <summary>
        /// Updates the rewards section of the UI.
        /// </summary>
        private void UpdateRewards() {
            int rewardAmount = Model!.GetReward();
            bool shouldShowReward = rewardAmount > 0 && !Model.IsDefeat;
            _rewardContainer.SetActive(shouldShowReward);

            if (shouldShowReward) {
                _rewardText.text = NumberFormatter.FormatNumber(rewardAmount);
            }
        }

        /// <summary>
        /// Called when the user wants to go to Uber's Game List.
        /// </summary>
        private void OnGoToGameList() => OnGoToGameListEvent?.Invoke();

        /// <summary>
        /// Called when the user wants to close the UI.
        /// Triggers the close event.
        /// </summary>
        public void OnClose() => OnCloseEvent?.Invoke();
    }
}
