using S1914.Ui.Common.UnitAndUpgradeTypeIcon;
using S1914.Ui.Common.Util;
using Stillfront.Validators;
using Stratkit.Localization.Components;
using Stratkit.Ui.Core;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// UI for displaying a single stats entry in the end game summary.
    /// </summary>
    public class EndGameSummaryStatsEntryUI : MonoBehaviour {
        [SerializeField, NotNull]
        private EndGameSummaryStatConfig _config = null!;

        [SerializeField, NotNull]
        private LocalizeTextDynamic _statNameText = null!;
        [SerializeField, NotNull]
        private TMP_Text _statValueText = null!;
        [SerializeField, NotNull]
        private Image _statIcon = null!;
        [SerializeField, NotNull]
        private UnitAndUpgradeTypeIconUI _unitAndUpgradeTypeIcon = null!;

        /// <summary>
        /// Initializes the entry with the given model.
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="assetContext">Asset context</param>
        public void Initialize(EndGameStatsEntryModel model, AssetContext assetContext) {
            EndGameSummaryStatConfig.EndGameSummaryStatConfigElement statConfig = _config.GetForStatType(model.StatType);
            _statIcon.sprite = statConfig.Icon;
            _statNameText.SetKey(statConfig.Name);
            _statValueText.text = NumberFormatter.FormatNumber(model.Amount);
            _unitAndUpgradeTypeIcon.gameObject.SetActive(model.UnitType != null);
            if (model.UnitType != null) {
                _unitAndUpgradeTypeIcon.Initialize(model.UnitType, assetContext, false);
            }
        }
    }
}
