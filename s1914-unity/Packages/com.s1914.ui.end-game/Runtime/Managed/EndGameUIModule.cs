using UnityEngine;
using S1914.Ui.MUS;
using System;

namespace S1914.Ui.EndGame {
    /// <summary>
    /// Module for <see cref="EndGameUI"/> and <see cref="EndGameUISystem"/>.
    /// </summary>
    [CreateAssetMenu(fileName = nameof(EndGameUIModule),
        menuName = "S1914/Modules/UI/" + nameof(EndGameUIModule))]
    public sealed class EndGameUIModule
        : UIModule<EndGameUISystem, EndGameUI, EndGameUIModuleConfig> {

        public override Type[] GetSystemsToAddToRootLevel() => new[] {
            typeof(EndGameUITriggerSystem),
            typeof(EndGameUISystem),
            typeof(EndGameSummaryUISystem),
            typeof(EndGameRankingUISystem),
        };
    }
}
