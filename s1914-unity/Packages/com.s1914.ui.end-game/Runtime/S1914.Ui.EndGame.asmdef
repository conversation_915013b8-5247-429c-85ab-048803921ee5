{"name": "S1914.Ui.EndGame", "rootNamespace": "S1914.Ui.EndGame", "references": ["com.stillfront.logging", "com.stillfront.preparevalidate", "S1914.Ui.Common", "S1914.Ui.Common.Util", "S1914.Ui.Models", "S1914.Ui.Requests", "S1914.Ui.UberGamesList", "Stillfront.AddressablesExtensions", "Stillfront.Validators", "Stratkit.AddressablesUtils", "Stratkit.AppNavigation", "Stratkit.ContentItemsModel", "Stratkit.ContentItemsModel.Common", "Stratkit.ContentItemsModel.Generated", "Stratkit.Core", "Stratkit.Entities", "Stratkit.Entities.Bridge", "Stratkit.Entities.HybridBaker", "Stratkit.Entities.Managed", "Stratkit.GameEnd", "Stratkit.GameInfoStateLoader", "Stratkit.Localization", "Stratkit.Localization.Components", "Stratkit.NewspaperStateLoader", "Stratkit.PlayerAndTeamRanking", "Stratkit.PlayerStateLoader", "Stratkit.Properties.Loader", "Stratkit.StatisticsStateLoader", "Stratkit.Ui.Core", "Stratkit.UiCommon", "Stratkit.WebApi.GetUser", "Stratkit.WorldReference", "Unity.Addressables", "Unity.Burst", "Unity.Collections", "Unity.Entities", "Unity.Mathematics", "Unity.TextMeshPro"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}