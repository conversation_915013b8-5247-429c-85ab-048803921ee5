using Stratkit.Core;
using System;
using System.Threading.Tasks;
using Unity.Entities;

namespace Stratkit.Tutorial.StateLoader {
    /// <summary>
    /// Module for the tutorial package.
    /// </summary>
    public sealed class TutorialStateLoaderModule : AModuleScriptable {
        /// <inheritdoc/>
        public override Type[] GetSystemsToAddToRootLevel() => new[] {
            typeof(TutorialStateLoaderSetupSystem),
            typeof(TutorialTaskCleanUpSystem)
        };

        /// <inheritdoc/>
        public override Task SetupAsync(World world) => Task.CompletedTask;
    }
}
