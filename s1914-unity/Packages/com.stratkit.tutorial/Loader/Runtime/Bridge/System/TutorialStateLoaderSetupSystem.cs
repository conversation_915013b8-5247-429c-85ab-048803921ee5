using Stratkit.GameStateLoader;
using Stratkit.ServerCommunication;
using Unity.Entities;
using Stratkit.Tutorial.Data;

namespace Stratkit.Tutorial.StateLoader {
    /// <summary>
    /// Sets up the tutorial state loader module.
    /// </summary>
    [UpdateInGroup(typeof(InitializationSystemGroup))]
    internal sealed partial class TutorialStateLoaderSetupSystem : SystemBase {
        protected override void OnCreate() => RequireForUpdate<DataTransformerContainer>();

        /// <summary>
        /// Registers data handlers for the tutorial state
        /// </summary>
        protected override void OnUpdate() {
            DataTransformer dataTransformer = SystemAPI.ManagedAPI.GetSingleton<DataTransformerContainer>()!.Value;
            dataTransformer.AddHandler(new StateVersionHandler(TutorialState.TypeIdentifier));
            dataTransformer.AddHandler<TutorialStateDataHandler>();
            Enabled = false;
        }
    }
}
