using Stratkit.ServerCommunication;
using Stratkit.Tutorial.Data;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;

namespace Stratkit.Tutorial.StateLoader {
    /// <summary>
    /// System responsible for cleaning up tutorial task entities, keeping only the current task entity alive.
    /// </summary>
    [BurstCompile]
    public partial struct TutorialTaskCleanUpSystem : ISystem {
        /// <summary>
        /// Query for entities representing the current tutorial state with a current task reference.
        /// </summary>
        private EntityQuery _tutorialStateChangedQuery;

        /// <summary>
        /// Query for all entities with a <see cref="TutorialTask"/> component.
        /// </summary>
        private EntityQuery _allTutorialTasksQuery;

        /// <summary>
        /// Initializes the entity queries used by this system.
        /// </summary>
        /// <param name="state">The system state.</param>
        [BurstCompile]
        public void OnCreate(ref SystemState state) {
            _allTutorialTasksQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<TutorialTask>()
                .Build(ref state);
            _tutorialStateChangedQuery = new EntityQueryBuilder(Allocator.Temp)
                .WithAll<TutorialStateTag>()
                .WithAll<TutorialStateCurrentTaskRef>()
                .WithAll<StateVersion>()
                .Build(ref state);
        }

        /// <summary>
        /// Called every frame; triggers cleanup if the tutorial state has changed.
        /// </summary>
        /// <param name="state">The system state.</param>
        [BurstCompile]
        public void OnUpdate(ref SystemState state) {
            if (_tutorialStateChangedQuery.IsEmpty) {
                return;
            }

            CleanUp(ref state);
        }

        /// <summary>
        /// Destroys all tutorial task entities except for the current task entity.
        /// </summary>
        /// <param name="state">The system state.</param>
        [BurstCompile]
        private void CleanUp(ref SystemState state) {
            EntityCommandBuffer ecb = new(Allocator.Temp);
            Entity currentTask = _tutorialStateChangedQuery.GetSingleton<TutorialStateCurrentTaskRef>().Value;
            using NativeArray<Entity> candidates = _allTutorialTasksQuery.ToEntityArray(Allocator.Temp);
            foreach (Entity candidate in candidates) {
                if (candidate == currentTask) {
                    continue; // skip the current task
                }

                ecb.DestroyEntity(candidate);
            }

            ecb.Playback(state.EntityManager);
        }
    }
}
