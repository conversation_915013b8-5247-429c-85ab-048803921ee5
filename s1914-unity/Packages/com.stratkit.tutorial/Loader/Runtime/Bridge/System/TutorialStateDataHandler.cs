using Stillfront.Logging;
using Stratkit.ServerCommunication;
using Stratkit.Tutorial.Data;
using Unity.Entities;

namespace Stratkit.Tutorial.StateLoader {
    /// <summary>
    /// A small data handler which adds the TutorialStateTag component to an entity.
    /// In addition, resolves the current tutorial task and creates and entity for it.
    /// </summary>
    internal sealed class TutorialStateDataHandler : IDataHandler {
        public string TypeIdentifier => TutorialState.TypeIdentifier;
        public string? IdPropertyName => null;

        /// <inheritdoc/>
        public void Invoke(IDeserializationContext context, EntityCommandBuffer ecb) {
            Entity entity = context.GetOrCreateEntity(ecb, TypeIdentifier);
            ecb.AddComponent<TutorialStateTag>(entity);

            // check if we have a current task:
            if (!context.Node.TryGetObjectValue("currentTask", out DataObject currentTask)) {
                // no tutorial task.
                // todo: perform clean up.
                return;
            }

            if (!TryResolveTutorialTask(context, ecb, currentTask, out Entity taskEntity)) {
                // failed to resolve the current task.
                context.LogError("Failed to resolve current tutorial task.");
                return;
            }

            ecb.AddComponent(entity, new TutorialStateCurrentTaskRef {
                Value = taskEntity
            });
        }

        /// <summary>
        /// Resolves the current tutorial task from the provided data object.
        /// </summary>
        /// <param name="context">context for the deserialization process</param>
        /// <param name="ecb">Entity command buffer to record into</param>
        /// <param name="currentTaskObj">Data object representing the current task</param>
        /// <param name="result">The resolved entity representing the current task, if successful.</param>
        /// <returns>true if the task was successfully resolved, false otherwise.</returns>
        private bool TryResolveTutorialTask(
            IDeserializationContext context,
            EntityCommandBuffer ecb,
            DataObject currentTaskObj,
            out Entity result) {
            result = Entity.Null;
            if (!currentTaskObj.TryGetStringValue("@c", out string typeHint)) {
                context.LogError("currentTask object missing type hint '@c'");
                return false;
            }

            if (!TryResolveCommonProperties(context, currentTaskObj, out TutorialTask common)) {
                context.LogError("Failed to resolve common properties for StartTutorialTask.");
                return false;
            }

            switch (typeHint) {
                case "ultshared.tutorial.tasks.UltStartTutorialTask":
                    TryResolveStartTutorialTask(context, ecb, currentTaskObj, out result);
                    break;
                // Add more cases for other task types
                default:
                    Log.Error($"Unsupported task type: {typeHint}");
                    break;
            }

            bool isResolved = result != Entity.Null;
            if (isResolved) {
                ecb.AddComponent<TutorialTask>(result);
                ecb.SetComponent(result, common);
            }

            return isResolved;
        }

        /// <summary>
        /// Attempts to resolve a StartTutorialTask from the provided data object.
        /// </summary>
        /// <param name="context">context for the deserialization process</param>
        /// <param name="ecb">Entity command buffer to record into</param>
        /// <param name="startTaskObj">Data object representing the current task</param>
        /// <param name="result">The resolved entity representing the current task, if successful.</param>
        /// <returns>true if successful</returns>
        private bool TryResolveStartTutorialTask(
            IDeserializationContext context,
            EntityCommandBuffer ecb,
            DataObject startTaskObj,
            out Entity result) {
            result = Entity.Null;

            result = ecb.CreateEntity();
            ecb.AddComponent(result, new TutorialTaskStartTutorial());
            return true;
        }

        /// <summary>
        /// Resolves the common tutorial task properties from the provided data object.
        /// </summary>
        /// <param name="context">context for the deserialization process</param>
        /// <param name="taskObj">Data object representing the task</param>
        /// <param name="common">Output parameter for the resolved common properties</param>
        /// <returns>true if the common properties were successfully resolved, false otherwise.</returns>
        private bool TryResolveCommonProperties(
            IDeserializationContext context,
            DataObject taskObj,
            out TutorialTask common) {
            common = default;
            if (!taskObj.TryGetStringValue("taskIdentifier", out string identifier)) {
                context.LogError("Task object missing 'taskIdentifier'");
                return false;
            }

            common = new TutorialTask {
                TaskIdentifier = identifier
            };
            return true;
        }
    }
}
