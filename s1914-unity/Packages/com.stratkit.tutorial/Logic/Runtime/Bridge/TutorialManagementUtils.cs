using Stratkit.GameStateLoader;
using Unity.Entities;

namespace Stratkit.Tutorial.Logic {
    /// <summary>
    /// Provides utility methods for managing the tutorial state.
    /// </summary>
    public static class TutorialManagementUtils {
        /// <summary>
        /// Tells game server to try to progress to the next step.
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Progress(EntityManager em) =>
            SendAction(em, new TutorialAction(TutorialActionType.Progress));

        /// <summary>
        /// Tells game server to cancel the tutorial entirely.
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Cancel(EntityManager em) =>
            SendAction(em, new TutorialAction(TutorialActionType.Cancel));

        /// <summary>
        /// Triggers sending the action to the game server
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="action">action to send</param>
        private static void SendAction(EntityManager em, TutorialAction action) =>
            em.SetComponentData(
                em.CreateEntity(
                    stackalloc ComponentType[] {
                        ComponentType.ReadWrite<ServerCommandRequest>(),
                    }
                ),
                new ServerCommandRequest { Value = action }
            );
    }
}
