using Stratkit.ServerCommunication;

namespace Stratkit.Tutorial.Logic {
    /// <summary>
    /// Used for sending tutorial specific actions to the server.
    /// </summary>
    public sealed class TutorialAction : ServerCommand {
        [SerializableName("action")]
        public int ActionType;

        /// <summary>
        /// Initializes a new instance of the <see cref="TutorialAction"/> class.
        /// </summary>
        /// <param name="actionType">Type of the action to perform.</param>
        public TutorialAction(TutorialActionType actionType) {
            Type = "ultshared.tutorial.UltTutorialAction";
            ActionType = (int)actionType;
        }
    }
}
