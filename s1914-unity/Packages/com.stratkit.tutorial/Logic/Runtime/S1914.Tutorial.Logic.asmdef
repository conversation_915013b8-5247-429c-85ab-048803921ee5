{"name": "Stratkit.Tutorial.Logic", "rootNamespace": "Stratkit.Tutorial.Logic", "references": ["com.stillfront.logging", "Stratkit.Tutorial.Data", "Stratkit.ContentItemsModel", "Stratkit.Core", "Stratkit.Entities", "Stratkit.GameStateLoader", "Stratkit.Properties.Common", "Stratkit.Properties.Loader", "Stratkit.ServerCommunication", "Stratkit.ServerCommunication.Core", "Stratkit.WorldReference", "Unity.Burst", "Unity.Collections", "Unity.Entities"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}