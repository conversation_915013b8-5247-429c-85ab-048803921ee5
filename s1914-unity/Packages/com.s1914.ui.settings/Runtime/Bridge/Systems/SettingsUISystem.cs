using S1914.Ui.MUS;
using S1914.Ui.Requests;
using S1914.Ui.Settings.InitializationRequests;
using Stratkit.Entities.Bridge;
using Stratkit.Entities.Managed;

namespace S1914.Ui.Settings {
    /// <summary>
    /// System responsible for managing <see cref="SettingsUI"/>.
    /// </summary>
    public sealed partial class SettingsUISystem : UIMainSystem<
        SettingsUI,
        SettingsOpenRequest,
        SettingsModel,
        SettingsUIModuleConfig> {
        /// <inheritdoc/>
        protected override SettingsModel CreateModel(SettingsOpenRequest openRequest) {
            // query some data here and pass it to the constructor
            // in order to keep the system clean.
            return new SettingsModel(EntityManager);
        }

        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(SettingsUI ui) {
            ui.OnUICreated += BridgeDelegate.Create<BaseHybridComponent>(this, OnTabUICreated);
        }

        /// <summary>
        /// Called when a tab UI instance gets created for the first time.
        /// It creates a new initialization request so that the UI can be initialized.
        /// </summary>
        /// <param name="tabUI">tab UI</param>
        private void OnTabUICreated(BaseHybridComponent tabUI) {
            if (tabUI.TryGetComponent(out AccountSettingsUI accountSettingsUI)) {
                AccountSettingsInitializationRequest.Trigger(EntityManager, accountSettingsUI);
            } else if (tabUI.TryGetComponent(out ApplicationSettingsUI applicationSettingsUI)) {
                ApplicationSettingsInitializationRequest.Trigger(EntityManager, applicationSettingsUI);
            } else if (tabUI.TryGetComponent(out NotificationSettingsUI notificationSettingsUI)) {
                NotificationSettingsInitializationRequest.Trigger(EntityManager, notificationSettingsUI);
            }
        }
    }
}
