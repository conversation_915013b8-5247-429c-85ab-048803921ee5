using S1914.Ui.MUS;
using S1914.Ui.Requests;
using Stratkit.Entities.Bridge;

namespace S1914.Ui.Settings {
    /// <summary>
    /// System responsible for managing <see cref="SettingsUI"/>.
    /// </summary>
    public sealed partial class SettingsUISystem : UIMainSystem<
        SettingsUI,
        SettingsOpenRequest,
        SettingsModel,
        SettingsUIModuleConfig> {
        /// <inheritdoc/>
        protected override void OnCreate() {
            base.OnCreate();
            // add additional data & change detection queries here if necessary.
        }

        /// <inheritdoc/>
        protected override SettingsModel CreateModel(SettingsOpenRequest openRequest) {
            // query some data here and pass it to the constructor
            // in order to keep the system clean.
            return new SettingsModel(EntityManager);
        }

        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(SettingsUI ui) {
            // todo: subscribe to UI specific events using BridgeDelegate.Create
            ui.OnSomethingHappenedEvent += BridgeDelegate.Create(this, () => OnSomethingHappened(ui));
        }

        /// <inheritdoc/>
        protected override void UpdateUI(SettingsUI ui) {
            base.UpdateUI(ui);
            // 1- update the UI for timers.
            // 2- update the UI for changed data.
        }

        /// <summary>
        /// called whenever something interesting happens.
        /// todo: remove this.
        /// </summary>
        /// <param name="ui">ui where this event has taken place</param>
        private void OnSomethingHappened(SettingsUI ui) {
            throw new System.NotImplementedException();
        }
    }
}
