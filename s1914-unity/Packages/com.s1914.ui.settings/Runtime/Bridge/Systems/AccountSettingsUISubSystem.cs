using S1914.Ui.MUS;
using S1914.Ui.Settings.InitializationRequests;

namespace S1914.Ui.Settings {
    /// <summary>
    /// System for managing the account settings user interface.
    /// </summary>
    public partial class AccountSettingsUISubSystem : UISubSystem<
        AccountSettingsUI,
        AccountSettingsInitializationRequest,
        AccountSettingsModel> {
        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(AccountSettingsUI ui) {
        }

        /// <inheritdoc/>
        protected override AccountSettingsModel CreateModel(AccountSettingsInitializationRequest openRequest) => new();
    }
}
