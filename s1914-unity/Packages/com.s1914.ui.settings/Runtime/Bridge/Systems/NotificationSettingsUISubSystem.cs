using S1914.Ui.MUS;
using S1914.Ui.Settings.InitializationRequests;

namespace S1914.Ui.Settings {
    /// <summary>
    /// System for managing the account settings user interface.
    /// </summary>
    public partial class NotificationSettingsUISubSystem : UISubSystem<
        NotificationSettingsUI,
        NotificationSettingsInitializationRequest,
        NotificationSettingsModel> {
        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(NotificationSettingsUI ui) {
        }

        /// <inheritdoc/>
        protected override NotificationSettingsModel
            CreateModel(NotificationSettingsInitializationRequest openRequest) => new();
    }
}
