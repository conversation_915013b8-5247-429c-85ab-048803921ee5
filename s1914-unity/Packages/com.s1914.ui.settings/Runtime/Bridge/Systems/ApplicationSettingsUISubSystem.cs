using S1914.Ui.MUS;
using S1914.Ui.Settings.InitializationRequests;

namespace S1914.Ui.Settings {
    /// <summary>
    /// System for managing the application settings user interface.
    /// </summary>
    public partial class ApplicationSettingsUISubSystem : UISubSystem<
        ApplicationSettingsUI,
        ApplicationSettingsInitializationRequest,
        ApplicationSettingsModel> {
        /// <inheritdoc/>
        protected override void SubscribeToSpecialEvents(ApplicationSettingsUI ui) {
        }

        /// <inheritdoc/>
        protected override ApplicationSettingsModel CreateModel(ApplicationSettingsInitializationRequest openRequest) =>
            new();
    }
}
