using S1914.Ui.Requests;
using Unity.Entities;

namespace S1914.Ui.Settings.InitializationRequests {
    /// <summary>
    /// Initialization request for <see cref="NotificationSettingsUI"/>.
    /// </summary>
    public class NotificationSettingsInitializationRequest : InitializationRequestBase {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public NotificationSettingsInitializationRequest() : base(null!) {
        }

        /// <summary>
        /// Constructor that initializes the request with a specific UI.
        /// </summary>
        /// <param name="ui">UI to initialize</param>
        private NotificationSettingsInitializationRequest(NotificationSettingsUI ui) : base(ui.gameObject) {
        }

        /// <summary>
        /// Triggers the initialization request.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="ui">UI to initialize</param>
        public static void Trigger(EntityManager em, NotificationSettingsUI ui) => em.AddComponentData(
            em.CreateEntity(stackalloc ComponentType[] {
                ComponentType.ReadWrite<NotificationSettingsInitializationRequest>()
            }),
            new NotificationSettingsInitializationRequest(ui));
    }
}
