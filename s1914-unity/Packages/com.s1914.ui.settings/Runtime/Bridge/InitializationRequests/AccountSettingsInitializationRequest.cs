using S1914.Ui.Requests;
using Unity.Entities;

namespace S1914.Ui.Settings.InitializationRequests {
    /// <summary>
    /// Initialization request for <see cref="AccountSettingsUI"/>.
    /// </summary>
    public class AccountSettingsInitializationRequest : InitializationRequestBase {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public AccountSettingsInitializationRequest() : base(null!) {
        }

        /// <summary>
        /// Constructor that initializes the request with a specific UI.
        /// </summary>
        /// <param name="ui">UI to initialize</param>
        private AccountSettingsInitializationRequest(AccountSettingsUI ui) : base(ui.gameObject) {
        }

        /// <summary>
        /// Triggers the initialization request.
        /// </summary>
        /// <param name="em">entity manager</param>
        /// <param name="ui">UI to initialize</param>
        public static void Trigger(EntityManager em, AccountSettingsUI ui) => em.AddComponentData(
            em.CreateEntity(stackalloc ComponentType[] {
                ComponentType.ReadWrite<AccountSettingsInitializationRequest>()
            }),
            new AccountSettingsInitializationRequest(ui));
    }
}
