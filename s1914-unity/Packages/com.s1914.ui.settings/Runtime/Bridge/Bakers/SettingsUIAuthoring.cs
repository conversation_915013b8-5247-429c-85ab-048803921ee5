using Stratkit.Entities.HybridBaker;
using Unity.Entities;
using UnityEngine;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Exposes the ui component to ECS.
    /// Manually attach this on <see cref="SettingsUI"/> prefab.
    /// </summary>
    [DisallowMultipleComponent, RequireComponent(typeof(ConvertGameObjectToEntity))]
    public class SettingsUIAuthoring : AGameObjectToEntityAuthoring {
        public override void ConvertToEcsComponent(Entity entity, EntityManager entityManager) =>
            entityManager.AddComponentData(entity, GetComponent<SettingsUI>());
    }
}
