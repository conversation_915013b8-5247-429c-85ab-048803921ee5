using Stratkit.Entities.HybridBaker;
using Unity.Entities;
using UnityEngine;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Exposes the ui component to ECS
    /// </summary>
    [DisallowMultipleComponent, RequireComponent(typeof(ConvertGameObjectToEntity))]
    internal class ApplicationSettingsUIAuthoring : AGameObjectToEntityAuthoring {
        public override void ConvertToEcsComponent(Entity entity, EntityManager entityManager) =>
            entityManager.AddComponentData(entity, GetComponent<AccountSettingsUI>());
    }
}
