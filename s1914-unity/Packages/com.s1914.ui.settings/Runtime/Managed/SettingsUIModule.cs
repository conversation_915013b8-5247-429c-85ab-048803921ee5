using UnityEngine;
using S1914.Ui.MUS;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Module for <see cref="SettingsUI"/> and <see cref="SettingsUISystem"/>.
    /// </summary>
    [CreateAssetMenu(fileName = nameof(SettingsUIModule),
        menuName = "S1914/Modules/UI/" + nameof(SettingsUIModule))]
    public sealed class SettingsUIModule : UIModule<SettingsUISystem, SettingsUI, SettingsUIModuleConfig> {
    }
}
