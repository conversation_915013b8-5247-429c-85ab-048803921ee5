using UnityEngine;
using S1914.Ui.MUS;
using System;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Module for <see cref="SettingsUI"/> and <see cref="SettingsUISystem"/>.
    /// </summary>
    [CreateAssetMenu(fileName = nameof(SettingsUIModule),
        menuName = "S1914/Modules/UI/" + nameof(SettingsUIModule))]
    public sealed class SettingsUIModule : UIModule<SettingsUISystem, SettingsUI, SettingsUIModuleConfig> {
        /// <inheritdoc/>
        public override Type[] GetSystemsToAddToRootLevel() =>
            new[] {
                typeof(SettingsUISystem),
                typeof(AccountSettingsUISubSystem),
                typeof(ApplicationSettingsUISubSystem),
                typeof(NotificationSettingsUISubSystem),
            };
    }
}
