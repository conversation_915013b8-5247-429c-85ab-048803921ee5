using S1914.Ui.MUS;
using Stratkit.Entities.Managed;
using Stratkit.Ui.Core;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Visualizes the notification settings of the user.
    /// </summary>
    public class NotificationSettingsUI : BaseHybridComponent, ICommonSubUI<NotificationSettingsModel> {
        public NotificationSettingsModel? Model { get; set; }
        private AssetContext _context;

        /// <summary>
        /// Initializes the notification settings UI with the provided model and context.§
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">context</param>
        public void Initialize(NotificationSettingsModel model, AssetContext context) {
            Model = model;
            _context = context;
        }
    }
}
