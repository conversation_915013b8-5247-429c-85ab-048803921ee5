using S1914.Ui.MUS;
using Stratkit.Ui.Core;
using Stratkit.Ui.Core.Extensions;
using System;

namespace S1914.Ui.Settings {
    /// <summary>
    /// A UI is responsible for visualizing <see cref="SettingsModel"/>
    /// Controlled by <see cref="SettingsUISystem"/>
    /// </summary>
    public class SettingsUI : ACloseableDialog, ICommonMainUI<SettingsModel> {
        public event Action? OnCloseEvent;
        public event Action? OnSomethingHappenedEvent;
        public SettingsModel? Model { get; set; }
        private AssetContext? _assetContext;

        /// <summary>
        /// Initializes the UI with the given model
        /// </summary>
        /// <param name="model">the model</param>
        /// <param name="context">asset context</param>
        public void Initialize(SettingsModel model, AssetContext context) {
            Model = model;
            _assetContext = context;
        }

        /// <summary>
        /// Called when the user wants to close the UI.
        /// Triggers the close event.
        /// </summary>
        public void OnClose() => OnCloseEvent?.Invoke();
    }
}
