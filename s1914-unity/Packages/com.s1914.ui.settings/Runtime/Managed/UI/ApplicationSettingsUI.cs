using S1914.Ui.MUS;
using Stratkit.Entities.Managed;
using Stratkit.Ui.Core;

namespace S1914.Ui.Settings {
    /// <summary>
    /// Visualizes the application settings of the user.
    /// </summary>
    public class ApplicationSettingsUI : BaseHybridComponent, ICommonSubUI<ApplicationSettingsModel> {
        public ApplicationSettingsModel? Model { get; set; }
        private AssetContext _context;

        /// <summary>
        /// Initializes the application settings UI with the provided model and context.
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">asset context</param>
        public void Initialize(ApplicationSettingsModel model, AssetContext context) {
            Model = model;
            _context = context;
        }
    }
}
