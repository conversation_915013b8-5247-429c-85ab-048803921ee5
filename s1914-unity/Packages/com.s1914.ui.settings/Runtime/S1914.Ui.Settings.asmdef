{"name": "S1914.<PERSON><PERSON>.<PERSON>s", "rootNamespace": "S1914.<PERSON><PERSON>.<PERSON>s", "references": ["com.stillfront.logging", "com.stillfront.preparevalidate", "S1914.Ui.Common", "S1914.Ui.Models", "S1914.Ui.Requests", "Stillfront.AddressablesExtensions", "Stillfront.Validators", "Stratkit.AddressablesUtils", "Stratkit.Core", "Stratkit.Entities", "Stratkit.Entities.Bridge", "Stratkit.Entities.HybridBaker", "Stratkit.Entities.Managed", "Stratkit.Ui.Core", "Unity.Addressables", "Unity.Collections", "Unity.Entities", "Unity.TextMeshPro"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}