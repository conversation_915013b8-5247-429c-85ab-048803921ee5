using S1914.Ui.Common.CountryFlag;
using S1914.Ui.Models;
using Stillfront.Validators;
using Stratkit.Ui.Core;
using UnityEngine;

namespace S1914.Ui.Common {
    /// <summary>
    /// Controls the UI for a team member ranking entry
    /// </summary>
    public class GameRankingEntryTeamMemberUi : GameRankingEntryUi {
        [SerializeField, NotNull]
        private GameObject? _playerIcon;

        [SerializeField, NotNull]
        private CountryFlagUI _flag = null!;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">asset context</param>
        public void Initialize(RankingEntryPlayerModel model, AssetContext context) {
            base.Initialize(model.Player.Name, model.Player.NationName, model.Points);
            _playerIcon!.SetActive(!model.IsAI);
            _flag.Initialize(model.Player, context);
        }
    }
}
