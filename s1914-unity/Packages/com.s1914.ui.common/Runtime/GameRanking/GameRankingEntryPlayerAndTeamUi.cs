using S1914.Ui.Common.Util;
using Stillfront.Validators;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace S1914.Ui.Common {
    /// <summary>
    /// Controls the UI for a player ranking entry
    /// </summary>
    public class GameRankingEntryPlayerAndTeamUi : GameRankingEntryUi {
        [SerializeField, NotNull]
        private TMP_Text? _positionText;

        [SerializeField, NotNull]
        private Image? _positionIcon;

        [SerializeField, NotNull]
        private Sprite[]? _positionIcons;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="ownerName">name of the owner</param>
        /// <param name="nationName">name of the nation</param>
        /// <param name="position">ranking position</param>
        /// <param name="points">points</param>
        protected void Initialize(string ownerName, string nationName, int position, int points) {
            base.Initialize(ownerName, nationName, points);

            // Check if we have enough position icons in the array
            // and set the sprite accordingly
            if (position <= _positionIcons!.Length) {
                _positionIcon!.sprite = _positionIcons[position - 1];
                _positionText!.gameObject.SetActive(false);
            } else {
                _positionIcon!.gameObject.SetActive(false);
                _positionText!.text = NumberFormatter.FormatOrdinal(position);
            }
        }
    }
}
