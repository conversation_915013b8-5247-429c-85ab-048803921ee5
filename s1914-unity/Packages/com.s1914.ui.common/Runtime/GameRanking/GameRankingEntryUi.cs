using S1914.Ui.Common.Util;
using TMPro;
using UnityEngine;

namespace S1914.Ui.Common {
    /// <summary>
    /// Base class of all ranking entries
    /// </summary>
    public abstract class GameRankingEntryUi : MonoBehaviour {
        [SerializeField]
        protected TMP_Text? _nameText;

        [SerializeField]
        protected TMP_Text? _leaderNameText;

        [SerializeField]
        protected TMP_Text? _pointsText;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="ownerName">name owner</param>
        /// <param name="nationName">name of the nation</param>
        /// <param name="points">points</param>
        protected void Initialize(string ownerName, string nationName, int points) {
            _nameText!.text = nationName;
            _leaderNameText!.text = ownerName;
            _pointsText!.text = $"{NumberFormatter.FormatNumber(points)}";
        }
    }
}
