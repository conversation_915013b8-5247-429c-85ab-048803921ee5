using S1914.Ui.Common.CountryFlag;
using S1914.Ui.Models;
using Stillfront.Validators;
using Stratkit.Ui.Core;
using UnityEngine;
using UnityEngine.UI;

namespace S1914.Ui.Common {
    /// <summary>
    /// Controls the UI for a player ranking entry
    /// </summary>
    public class GameRankingEntryPlayerUi : GameRankingEntryPlayerAndTeamUi {
        [SerializeField, NotNull]
        private Image? _playerImage;

        [SerializeField, NotNull]
        private Sprite? _playerIcon;

        [SerializeField, NotNull]
        private Vector2 _playerIconSize;

        [SerializeField, NotNull]
        private CountryFlagUI _flag = null!;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">context</param>
        public void Initialize(RankingEntryEffectiveModel model, AssetContext context) {
            RankingEntryPlayerModel innerModel = (RankingEntryPlayerModel)model.InnerModel;

            _playerImage!.sprite = _playerIcon;
            _playerImage!.GetComponent<RectTransform>().sizeDelta = _playerIconSize;
            _flag.Initialize(innerModel.Player, context);
            base.Initialize(innerModel.Player.Name, innerModel.Player.NationName, model.Position, innerModel.Points);
        }
    }
}
