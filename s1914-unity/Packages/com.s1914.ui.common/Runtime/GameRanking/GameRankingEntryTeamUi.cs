using S1914.Ui.Common.TeamFlag;
using S1914.Ui.Models;
using Stillfront.Validators;
using Stratkit.Ui.Core;
using UnityEngine;
using UnityEngine.UI;

namespace S1914.Ui.Common {
    /// <summary>
    /// Controls the UI for a team ranking entry
    /// </summary>
    public class GameRankingEntryTeamUi : GameRankingEntryPlayerAndTeamUi {
        [Header("GO")]
        [SerializeField, NotNull]
        private GameObject _teamMemberList = null!;

        [SerializeField, NotNull]
        private GameObject _teamMemberPrefab = null!;

        [Header("UI")]
        [SerializeField, NotNull]
        private Sprite _teamSprite = null!;

        [SerializeField, NotNull]
        private Sprite _coalitionIcon = null!;

        [SerializeField, NotNull]
        private Image _coalitionImage = null!;

        [SerializeField, NotNull]
        private TeamFlagUI _flag = null!;

        [Space]
        [SerializeField, NotNull]
        private Vector2 _teamIconSize;
        [Ser<PERSON><PERSON><PERSON><PERSON>, NotNull]
        private Vector2 _coalitionIconSize;

        [Header("Components")]
        [Serial<PERSON><PERSON><PERSON>, NotNull]
        private RectTransform? _rectTransform;
        [SerializeField, NotNull]
        private RectTransform? _teamMemberListRectTransform;
        [SerializeField, NotNull]
        private RectTransform? _coalitionImageRectTransform;
        [SerializeField, NotNull]
        private VerticalLayoutGroup? _teamMemberListVerticalLayoutGroup;

        /// <summary>
        /// Initializes the UI with the given data
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="context">asset context</param>
        public void Initialize(RankingEntryEffectiveModel model, AssetContext context) {
            RankingEntryTeamModel innerModel = (RankingEntryTeamModel)model.InnerModel;
            base.Initialize(string.Empty, innerModel.Team.Name, model.Position, innerModel.Points);

            if (_teamMemberList == null) {
                return;
            }

            foreach (RankingEntryPlayerModel memberModel in innerModel.Players) {
                GameObject memberObject = Instantiate(_teamMemberPrefab, _teamMemberList.transform);
                GameRankingEntryTeamMemberUi memberComponent = memberObject.GetComponent<GameRankingEntryTeamMemberUi>();
                memberComponent.Initialize(memberModel, context);

                _rectTransform!.sizeDelta = new Vector2(_rectTransform!.sizeDelta.x, _rectTransform!.sizeDelta.y + _teamMemberListVerticalLayoutGroup!.spacing);
                _teamMemberListRectTransform!.sizeDelta = new Vector2(_teamMemberListRectTransform.sizeDelta.x, _teamMemberListRectTransform.sizeDelta.y + _teamMemberListVerticalLayoutGroup.spacing);
            }

            _coalitionImage.sprite = _coalitionIcon;
            _coalitionImageRectTransform!.sizeDelta = _coalitionIconSize;
            _flag.Initialize(innerModel.Team, context);
        }
    }
}
