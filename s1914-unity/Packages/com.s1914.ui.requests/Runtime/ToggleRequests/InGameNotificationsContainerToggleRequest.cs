using Unity.Entities;

namespace S1914.Ui.Requests {
    /// <summary>
    /// Request to show or hide in-game notifications container.
    /// </summary>
    public struct InGameNotificationsContainerToggleRequest : IComponentData {
        public bool Show { get; private set; }

        /// <summary>
        /// Triggers a show/hide request for the in-game notifications container.
        /// </summary>
        /// <param name="em">manager of all entities</param>
        /// <param name="show">true if in-game notifications container is to be shown</param>
        public static void Trigger(EntityManager em, bool show) =>
            em.SetComponentData(em.CreateEntity(stackalloc ComponentType[] {
                    ComponentType.ReadWrite<InGameNotificationsContainerToggleRequest>()
                }),
                new InGameNotificationsContainerToggleRequest { Show = show });
    }
}
