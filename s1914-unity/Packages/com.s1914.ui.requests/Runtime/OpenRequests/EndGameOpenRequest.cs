using Unity.Entities;

namespace S1914.Ui.Requests {
    /// <summary>
    /// Open request for <see cref="EndGameUI"/>
    /// </summary>
    public sealed class EndGameOpenRequest : OpenRequestBase {
        /// <summary>
        /// Trigger the open request
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Trigger(EntityManager em) =>
            em.SetComponentData(em.CreateEntity(stackalloc ComponentType[] {
                ComponentType.ReadWrite<EndGameOpenRequest>()
            }), new EndGameOpenRequest());
    }
}
