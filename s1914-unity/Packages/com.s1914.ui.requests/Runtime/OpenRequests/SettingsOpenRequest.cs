using Unity.Entities;

namespace S1914.Ui.Requests {
    /// <summary>
    /// Open request for <see cref="SettingsUI"/>
    /// </summary>
    public class SettingsOpenRequest : OpenRequestBase {
        /// <summary>
        /// Trigger the open request
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Trigger(EntityManager em) =>
            em.AddComponentData(em.CreateEntity(typeof(SettingsOpenRequest)), new SettingsOpenRequest());
    }
}
