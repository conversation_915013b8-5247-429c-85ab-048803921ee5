using Unity.Entities;

namespace S1914.Ui.Requests {
    /// <summary>
    /// Open request for <see cref="ExampleUI"/>
    /// </summary>
    public class UberCreateRequest : IComponentData {
        /// <summary>
        /// Trigger the open request
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Trigger(EntityManager em) {
            UberCreateRequest request = new();
            em.AddComponentData(em.CreateEntity(), request);
        }
    }
}
