using Unity.Entities;

namespace S1914.Ui.Requests {
    /// <summary>
    /// Create request for <see cref="UberUI"/>
    /// </summary>
    public class UberCreateRequest : IComponentData {
        /// <summary>
        /// Trigger the open request
        /// </summary>
        /// <param name="em">entity manager</param>
        public static void Trigger(EntityManager em) {
            UberCreateRequest request = new();
            em.AddComponentData(em.CreateEntity(), request);
        }
    }
}
