{"dependencies": {"appsflyer-unity-plugin": {"version": "https://github.com/AppsFlyerSDK/appsflyer-unity-plugin.git#v6.14.0", "depth": 0, "source": "git", "dependencies": {}, "hash": "f61da50285178530d2cf352d014174a0b4a0cc2c"}, "com.autodesk.fbx": {"version": "5.1.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.coffee.ui-particle": {"version": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "depth": 0, "source": "git", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "hash": "2e813c531325647f9d228100a76ab91cff993a00"}, "com.marijnzwemmer.unity-toolbar-extender": {"version": "file:unity-toolbar-extender", "depth": 0, "source": "embedded", "dependencies": {}}, "com.rukhanka.animation": {"version": "file:../../stratkit/stratkit/Packages/com.rukhanka.animation", "depth": 0, "source": "local", "dependencies": {"com.unity.entities.graphics": "1.0.16"}}, "com.s1914.army-error-input-feedback": {"version": "file:com.s1914.army-error-input-feedback", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.cityareas-building-visualization": {"version": "file:com.s1914.cityareas-building-visualization", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.1.0", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.buildings": "3.0.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.core": "1.0.4", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.factions": "1.0.0", "com.stratkit.map": "4.1.1", "com.stratkit.moddable-upgrade-state-loader": "1.10.0", "com.stratkit.player-state-loader": "2.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.provinces": "1.5.0", "com.stratkit.service-injector": "1.0.1", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6"}}, "com.s1914.deploy-unit": {"version": "file:com.s1914.deploy-unit", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.3.8"}}, "com.s1914.deploy-unit-renderer": {"version": "file:com.s1914.deploy-unit-renderer", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.3.8"}}, "com.s1914.game-events": {"version": "file:com.s1914.gameplay-events", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.game-player-flag": {"version": "file:com.s1914.game-player-flag", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.game-server-error-handler": {"version": "file:com.s1914.game-server-error-handler", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.heroes": {"version": "file:com.s1914.heroes", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0"}}, "com.s1914.map-view-modes": {"version": "file:com.stratkit.diplomacy-terrain", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "2.1.1", "com.stratkit.core": "1.0.2", "com.stratkit.entities-core": "2.5.0", "com.stratkit.entities-reactive": "1.0.0", "com.stratkit.foreign-affairs-balancing-data": "1.1.0", "com.stratkit.foreign-affairs-state-loader": "1.2.0", "com.stratkit.map": "4.0.0", "com.stratkit.player-state-loader": "0.3.0", "com.stratkit.properties-loader": "1.1.2", "com.stratkit.provinces": "1.0.0", "com.stratkit.service-injector": "1.0.0", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.map.frame": {"version": "file:com.s1914.map.frame", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.3.5"}}, "com.s1914.pathfinding": {"version": "file:com.s1914.pathfinding", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.province-filtering-utils": {"version": "file:com.s1914.province-filtering-utils", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.resource-bar-ui": {"version": "file:com.s1914.resource-bar-ui", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.1.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.4", "com.stratkit.data": "1.0.0", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "1.1.1", "com.stratkit.localization": "0.3.0", "com.stratkit.localization-components": "1.0.2", "com.stratkit.player-state-loader": "2.0.0", "com.stratkit.premiums": "0.1.4", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.resource-state-loader": "1.2.0", "com.stratkit.ui": "1.5.0", "com.stratkit.ui-common": "2.2.1", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.unity.textmeshpro": "3.0.9"}}, "com.s1914.s1914.unitsdeploymentview": {"version": "file:com.s1914.unitsdeploymentview", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.3.5"}}, "com.s1914.sound.ambience-audio": {"version": "file:com.s1914.sound.music-and-ambience", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.tutorial-material-replace": {"version": "file:com.s1914.tutorial-material-replace", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "2.1.1", "com.stratkit.core": "1.0.2", "com.stratkit.entities-core": "2.5.0", "com.stratkit.entities-reactive": "1.0.0", "com.stratkit.foreign-affairs-balancing-data": "1.1.0", "com.stratkit.foreign-affairs-state-loader": "1.2.0", "com.stratkit.map": "4.0.0", "com.stratkit.player-state-loader": "0.3.0", "com.stratkit.properties-loader": "1.1.2", "com.stratkit.provinces": "1.0.0", "com.stratkit.service-injector": "1.0.0", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.uber-games-list": {"version": "file:com.s1914.ui.uber-games-list", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.uber-overview": {"version": "file:com.s1914.ui.uber-overview", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui-army-label": {"version": "file:com.s1914.ui-army-label", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.3.8"}}, "com.s1914.ui.accept-order": {"version": "file:com.s1914.ui.accept-order", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.add-army": {"version": "file:com.s1914.ui.add-army", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.army-bar": {"version": "file:com.s1914.ui.army-bar", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.army-command-confirmation": {"version": "file:com.s1914.ui.army-command-confirmation", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.army-command-cost-confirmation": {"version": "file:com.s1914.ui.army-command-cost-confirmation", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.army-delay": {"version": "file:com.s1914.ui.army-delay", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.army-split": {"version": "file:com.s1914.ui.army-split", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.battle-pass": {"version": "file:com.s1914.ui.battle-pass", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.battle-pass-claim-all": {"version": "file:com.s1914.ui.battle-pass-claim-all", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.battle-pass-tier-details": {"version": "file:com.s1914.ui.battle-pass-tier-details", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.building-info": {"version": "file:com.s1914.ui.building-info", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.0.16"}}, "com.s1914.ui.coalition-profile": {"version": "file:com.s1914.ui.coalition-profile", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.common": {"version": "file:com.s1914.ui.common", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0"}}, "com.s1914.ui.common-util": {"version": "file:com.s1914.ui.common-util", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0"}}, "com.s1914.ui.create-coalition": {"version": "file:com.s1914.ui.create-coalition", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.create-order": {"version": "file:com.s1914.ui.create-order", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.day-of-game": {"version": "file:com.s1914.ui.day-of-game", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.diplomacy": {"version": "file:com.s1914.ui.diplomacy", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.fatfinger": {"version": "file:com.s1914.ui.fatfinger", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.fire-control": {"version": "file:com.s1914.ui.fire-control", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.generic-confirm": {"version": "file:com.s1914.ui.generic-confirm", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.generic-input": {"version": "file:com.s1914.ui.generic-input", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.generic-message": {"version": "file:com.s1914.ui.generic-message", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.hero-list": {"version": "file:com.s1914.ui.hero-list", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.hero-promoted": {"version": "file:com.s1914.ui.hero-promoted", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.hero-stats": {"version": "file:com.s1914.ui.hero-stats", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.hud": {"version": "file:com.s1914.ui.hud", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.in-game-notifications": {"version": "file:com.s1914.ui.in-game-notifications", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.inventory-list": {"version": "file:com.s1914.ui.inventory-list", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.inventory-system-item-details": {"version": "file:com.s1914.ui.inventory-system-item-details", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.item-requirements": {"version": "file:com.s1914.ui.item-requirements", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.map-filters": {"version": "file:com.s1914.ui.map-filters", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.missing-premium-currency": {"version": "file:com.s1914.ui.missing-premium-currency", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.missing-resources": {"version": "file:com.s1914.ui.missing-resources", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.models": {"version": "file:com.s1914.ui.models", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.news": {"version": "file:com.s1914.ui.news", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.order-book": {"version": "file:com.s1914.ui.order-book", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.player-profile": {"version": "file:com.s1914.ui.player-profile", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.player-selector": {"version": "file:com.s1914.ui.player-selector", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.premium-account-teaser": {"version": "file:com.s1914.ui.premium-account-teaser", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.premium-action": {"version": "file:com.s1914.ui.premium-action", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.private-message-details": {"version": "file:com.s1914.ui.private-message-details", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.private-message-list": {"version": "file:com.s1914.ui.private-message-list", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.production-helper-utils": {"version": "file:com.s1914.ui.production-helper-utils", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0"}}, "com.s1914.ui.province-bar": {"version": "file:com.s1914.ui.province-bar", "depth": 0, "source": "embedded", "dependencies": {"com.unity.entities": "1.0.16"}}, "com.s1914.ui.province-list": {"version": "file:com.s1914.ui.province-list", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.province-productions": {"version": "file:com.s1914.ui.province-productions", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.requests": {"version": "file:com.s1914.ui.requests", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.resource-details": {"version": "file:com.s1914.ui.resource-details", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.resources": {"version": "file:com.s1914.ui.resources", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.send-diplomatic-message": {"version": "file:com.s1914.ui.send-diplomatic-message", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.send-trade-offer": {"version": "file:com.s1914.ui.send-trade-offer", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.settings": {"version": "file:com.s1914.ui.settings", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.shop": {"version": "file:com.s1914.ui.shop", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.shop-offer-details": {"version": "file:com.s1914.ui.shop-offer-details", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.uber": {"version": "file:com.s1914.ui.uber", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.unit-deployment-location-selection": {"version": "file:com.s1914.ui.unit-deployment-location-selection", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.unit-info": {"version": "file:com.s1914.ui.unit-info", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.user-login": {"version": "file:com.s1914.ui.user-login", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.user-profile": {"version": "file:com.s1914.ui.user-profile", "depth": 0, "source": "embedded", "dependencies": {}}, "com.s1914.ui.victory-progress": {"version": "file:com.s1914.ui.victory-progress", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.ui.war-declaration-warning": {"version": "file:com.s1914.ui.war-declaration-warning", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.3", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.stratkit.localization": "0.2.2", "com.stratkit.localization-components": "1.0.1", "com.stratkit.player-state-loader": "1.0.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.provinces": "1.1.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-common": "2.1.0", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.s1914.unit-deployment-map-pins": {"version": "file:com.s1914.unit-deployment-map-pins", "depth": 0, "source": "embedded", "dependencies": {}}, "com.sirenix.odininspector": {"version": "file:com.sirenix.odininspector", "depth": 0, "source": "embedded", "dependencies": {}}, "com.starasgames.tmpro-dynamic-data-cleaner": {"version": "https://github.com/STARasGAMES/tmpro-dynamic-data-cleaner.git#upm", "depth": 0, "source": "git", "dependencies": {}, "hash": "3a3607621431b0173bea325a53fb34e8d574f995"}, "com.stillfront.addressables-hierarchydrawer": {"version": "2.0.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.addressables": "1.19.19"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.addressables-setup": {"version": "2.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.addressables": "1.19.19"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.addressablesextensions": {"version": "2.1.1", "depth": 0, "source": "registry", "dependencies": {"com.stillfront.logging": "3.1.1", "com.unity.addressables": "1.21.9"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.anchors": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.assetreferencevalidator": {"version": "2.1.0", "depth": 0, "source": "registry", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.unity.addressables": "1.21.17"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.audioscriptable": {"version": "4.1.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.buildpipeline": {"version": "3.1.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.editorprogressbar": {"version": "4.0.4", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.logging": {"version": "3.1.4", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.notchhelper": {"version": "1.0.2", "depth": 0, "source": "registry", "dependencies": {"com.stillfront.logging": "3.1.1"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.outline": {"version": "1.0.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.package-dependencies-updater": {"version": "1.0.3", "depth": 1, "source": "registry", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.preparevalidate": {"version": "5.1.0", "depth": 1, "source": "registry", "dependencies": {"com.stillfront.editorprogressbar": "4.0.2"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.referencesfinder": {"version": "4.1.0", "depth": 0, "source": "registry", "dependencies": {"com.stillfront.editorprogressbar": "4.0.4"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.scriptableobjectcreator": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.thirdpartypatcher": {"version": "0.0.1", "depth": 0, "source": "registry", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.validators": "2.0.3"}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stillfront.ui-raycast-target": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stillfront.validators": {"version": "3.3.1", "depth": 1, "source": "registry", "dependencies": {"com.stillfront.preparevalidate": "5.1.0"}, "url": "https://npm.pkg.github.com/@Stillfront"}, "com.stratkit.addressables-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.addressables-utils", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.core": "2.0.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.app-navigation": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.app-navigation", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.0", "com.stratkit.localization": "0.7.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.3.0", "com.stratkit.ui-common": "6.2.0", "com.stratkit.worldreference": "2.1.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.inputsystem": "1.7.0"}}, "com.stratkit.app-version-split": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.app-version-split", "depth": 0, "source": "local", "dependencies": {"com.stillfront.buildpipeline": "3.0.0", "com.stillfront.validators": "3.1.0"}}, "com.stratkit.appsflyer-plugin": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.appsflyer-plugin", "depth": 0, "source": "local", "dependencies": {"appsflyer-unity-plugin": "6.15.2", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.6", "com.stratkit.entities-core": "2.14.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.webapi": "0.13.0", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.army-aggressiveness": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-aggressiveness", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.army-air": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-air", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-command": "3.19.0", "com.stratkit.army-state-loader": "5.36.0", "com.stratkit.buildings": "4.4.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.moddable-upgrade-state-loader": "2.9.0", "com.stratkit.mods": "3.20.0", "com.stratkit.pathfinding": "3.7.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.server-communication": "5.11.0", "com.stratkit.units": "5.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-air-convoy": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-air-convoy", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.5.0", "com.stratkit.core": "1.1.0", "com.stratkit.entities-core": "2.14.0", "com.stratkit.entities-reactive": "1.2.0", "com.stratkit.foreign-affairs-state-loader": "2.1.2", "com.stratkit.map": "5.0.0", "com.stratkit.mods": "3.4.0", "com.stratkit.pathfinding": "2.11.0", "com.stratkit.player-state-loader": "2.1.0", "com.stratkit.properties-commons": "1.2.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.scenario": "2.1.0", "com.stratkit.server-communication": "5.5.0", "com.stratkit.units": "3.8.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-air-rendering": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-air-rendering", "depth": 0, "source": "local", "dependencies": {"com.rukhanka.animation": "1.8.0-StratkitFix1", "com.stillfront.validators": "3.3.1", "com.stratkit.army-air": "2.0.0", "com.stratkit.army-command": "3.19.0", "com.stratkit.army-entity-graphics": "4.2.0", "com.stratkit.army-formation": "1.1.0", "com.stratkit.army-state-loader": "5.36.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-info-state-loader": "1.5.1", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.properties-commons": "2.0.0", "com.stratkit.properties-loader": "3.0.0", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.units": "5.2.0", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.2.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2", "com.unity.physics": "1.3.2"}}, "com.stratkit.army-command": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-command", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-state-loader": "5.33.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-info-state-loader": "1.5.1", "com.stratkit.game-state-loader": "4.9.5", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.sectionstreaming": "5.8.0", "com.stratkit.server-communication": "5.11.0", "com.stratkit.terrain-pathfinding": "2.11.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.units": "5.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-command-candidates": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-command-candidates", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-aggressiveness": "1.0.0", "com.stratkit.army-air": "1.15.0", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-input-forced-march": "1.0.0", "com.stratkit.army-state-loader": "5.4.1", "com.stratkit.entities-reactive": "1.2.0", "com.stratkit.map": "5.0.0", "com.stratkit.player-state-loader": "2.1.0", "com.stratkit.units": "3.8.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.2", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.army-entity-graphics": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-entity-graphics", "depth": 0, "source": "local", "dependencies": {"com.rukhanka.animation": "1.8.0-StratkitFix1", "com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.validators": "3.3.1", "com.stratkit.army-formation": "1.1.0", "com.stratkit.army-state-loader": "6.0.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.6.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.18.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.6.2", "com.stratkit.foreign-affairs-state-loader": "4.2.6", "com.stratkit.map": "6.2.0", "com.stratkit.math": "1.16.0", "com.stratkit.orientfightingarmies": "1.0.1", "com.stratkit.pathfinding": "3.7.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "2.0.0", "com.stratkit.properties-loader": "3.0.2", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.units": "5.3.1", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.2.1", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2", "com.unity.physics": "1.3.2"}}, "com.stratkit.army-formation": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-formation", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.units": "1.11.2", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.army-input-add": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-add", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.16.0", "com.stratkit.army-input-core": "4.0.0", "com.stratkit.army-input-core-data": "3.0.0", "com.stratkit.army-state-loader": "5.32.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.server-communication": "5.10.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-air": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-air", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-air": "2.0.0", "com.stratkit.army-command": "3.19.0", "com.stratkit.army-input-attack": "1.20.0", "com.stratkit.army-input-core": "4.3.0", "com.stratkit.army-input-core-data": "3.2.0", "com.stratkit.army-state-loader": "5.36.0", "com.stratkit.buildings": "4.4.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.moddable-upgrade-state-loader": "2.9.0", "com.stratkit.pathfinding": "3.7.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.selection": "1.4.2", "com.stratkit.server-communication": "5.11.0", "com.stratkit.units": "5.2.0", "com.stratkit.user-input-actions": "1.9.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-attack": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-attack", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.16.0", "com.stratkit.army-input-core": "4.0.0", "com.stratkit.army-input-core-data": "3.0.0", "com.stratkit.army-state-loader": "5.32.0", "com.stratkit.contentitemsmodel": "6.0.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.selection": "1.4.2", "com.stratkit.server-communication": "5.10.0", "com.stratkit.user-input-actions": "1.9.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-core", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.logging": "3.1.4", "com.stratkit.army-air": "2.0.0", "com.stratkit.army-command": "3.19.0", "com.stratkit.army-state-loader": "5.36.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.pathfinding": "3.7.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.selection": "1.4.2", "com.stratkit.terrain-pathfinding": "2.11.1", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.2.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-core-data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-core-data", "depth": 0, "source": "local", "dependencies": {"com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.15.0", "com.stratkit.pathfinding": "3.7.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-delay": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-delay", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.4.1", "com.stratkit.collections": "1.6.0", "com.stratkit.core": "1.0.6", "com.stratkit.entities-core": "2.14.0", "com.stratkit.entities-reactive": "1.2.0", "com.stratkit.game-state-loader": "4.3.2", "com.stratkit.selection": "1.4.0", "com.stratkit.server-communication": "5.1.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.army-input-fire-control": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-fire-control", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.4.0", "com.stratkit.collections": "1.6.0", "com.stratkit.core": "1.0.6", "com.stratkit.entities-core": "2.14.0", "com.stratkit.selection": "1.4.0", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.army-input-forced-march": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-forced-march", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.4.0", "com.stratkit.collections": "1.6.0", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.selection": "1.3.0", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.army-input-merge": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-merge", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-command": "3.0.0", "com.stratkit.core": "1.0.5", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.game-state-loader": "4.0.0", "com.stratkit.server-communication": "4.0.0", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.army-input-overwrite": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-overwrite", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.16.0", "com.stratkit.army-input-core": "4.0.0", "com.stratkit.army-input-core-data": "3.0.0", "com.stratkit.army-state-loader": "5.32.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.server-communication": "5.10.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-snapping-points": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-snapping-points", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-input-core-data": "3.0.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.map": "6.0.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.snapping-points": "1.0.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-split": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-split", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.3.0", "com.stratkit.army-selection": "1.4.0", "com.stratkit.army-state-loader": "5.13.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-state-loader": "4.6.1", "com.stratkit.selection": "1.4.1", "com.stratkit.server-communication": "5.7.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-input-stop": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-input-stop", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.16.0", "com.stratkit.army-input-core": "3.1.0", "com.stratkit.army-input-core-data": "2.0.0", "com.stratkit.army-input-overwrite": "1.10.0", "com.stratkit.army-state-loader": "5.31.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.sectionstreaming": "5.7.0", "com.stratkit.selection": "1.4.2", "com.stratkit.snapping-points": "1.0.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-label": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-label", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.1", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.army-command": "3.18.0", "com.stratkit.army-entity-graphics": "4.0.2", "com.stratkit.army-offset": "2.6.0", "com.stratkit.army-state-loader": "5.35.2", "com.stratkit.contentitemsmodel": "10.2.1", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.1", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.6.2", "com.stratkit.foreign-affairs-state-loader": "4.2.6", "com.stratkit.game-info-state-loader": "1.5.1", "com.stratkit.gameobject-pool": "1.1.1", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "1.21.0", "com.stratkit.properties-loader": "2.2.1", "com.stratkit.render-core": "1.0.0", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.rts-camera": "2.4.1", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.selection": "1.4.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.13.1", "com.stratkit.ui-common": "7.0.0", "com.stratkit.units": "5.2.0", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.2.0", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.army-label-status": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-label-status", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.army-air": "1.22.0", "com.stratkit.army-command": "3.4.0", "com.stratkit.army-label": "5.10.1", "com.stratkit.army-state-loader": "5.14.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-info-state-loader": "1.4.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.selection": "1.4.1", "com.stratkit.ui": "2.4.0", "com.stratkit.ui-common": "6.6.1", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "io.jahro.console": "3.0.4"}}, "com.stratkit.army-offset": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-offset", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-entity-graphics": "2.1.0", "com.stratkit.army-state-loader": "5.4.1", "com.stratkit.core": "1.1.0", "com.stratkit.entities-core": "2.14.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.2.0", "com.stratkit.map": "5.0.0", "com.stratkit.math": "1.13.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.rts-camera": "1.7.0", "com.stratkit.rts-camera-controller": "0.1.4", "com.stratkit.sectionstreaming": "3.7.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.0.1", "com.stratkit.user-input-actions": "1.5.1", "com.stratkit.zoom": "3.1.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-offset-indicator": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-offset-indicator", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.army-command": "3.18.0", "com.stratkit.army-entity-graphics": "3.13.0", "com.stratkit.army-offset": "2.6.0", "com.stratkit.army-state-loader": "5.35.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.15.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.render-core": "1.0.0", "com.stratkit.rts-camera": "2.4.1", "com.stratkit.rts-camera-controller": "0.2.0", "com.stratkit.sectionstreaming": "6.0.0", "com.stratkit.selection": "1.4.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.13.1", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.0.0", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-path-renderer": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-path-renderer", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.army-command": "3.18.0", "com.stratkit.army-entity-graphics": "4.0.1", "com.stratkit.army-state-loader": "5.35.1", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.gameobject-pool": "1.1.1", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.16.0", "com.stratkit.pathfinding": "3.7.0", "com.stratkit.render-core": "1.0.0", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.selection": "1.4.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.2.0", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-path-renderer-diplomacy": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-path-renderer-diplomacy", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-command": "3.16.0", "com.stratkit.army-path-renderer": "3.0.0", "com.stratkit.army-state-loader": "5.31.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.6.1", "com.stratkit.foreign-affairs-state-loader": "4.2.2", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-loader": "1.18.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.selection": "1.4.2", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-rendering-diplomacy": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-rendering-diplomacy", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.army-entity-graphics": "1.21.0", "com.stratkit.army-state-loader": "4.0.0", "com.stratkit.core": "1.0.4", "com.stratkit.entities-core": "2.12.0", "com.stratkit.foreign-affairs-balancing-data": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "1.6.0", "com.stratkit.player-state-loader": "2.0.1", "com.stratkit.service-injector": "1.0.1", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.entities.graphics": "1.2.3", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.army-selection": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-selection", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-state-loader": "5.4.0", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-fsm": "1.3.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.player-state-loader": "2.0.3", "com.stratkit.roslyn-analyzers": "0.2.2", "com.stratkit.selection": "1.4.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.user-input-actions": "1.5.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.army-selection-renderer": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-selection-renderer", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-command": "3.18.0", "com.stratkit.army-state-loader": "5.35.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "10.0.2", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.entities-transform-sync": "0.2.2", "com.stratkit.map": "6.1.0", "com.stratkit.map-feedback-entity-graphics": "0.12.1", "com.stratkit.properties-commons": "1.20.1", "com.stratkit.selection": "1.4.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.units": "5.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-selection-renderer-air": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-selection-renderer-air", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-air": "2.0.0", "com.stratkit.army-command": "3.19.0", "com.stratkit.army-entity-graphics": "4.2.0", "com.stratkit.army-input-air": "2.23.0", "com.stratkit.army-input-core": "4.3.0", "com.stratkit.army-selection-renderer": "2.6.2", "com.stratkit.army-state-loader": "5.36.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.entities-transform-sync": "0.2.2", "com.stratkit.map": "6.1.0", "com.stratkit.map-feedback-entity-graphics": "0.12.2", "com.stratkit.math": "1.16.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.selection": "1.4.2", "com.stratkit.units": "5.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "11.2.0", "com.stratkit.core": "2.1.0", "com.stratkit.data": "3.25.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "4.2.6", "com.stratkit.game-info-state-loader": "1.5.1", "com.stratkit.game-state-loader": "4.10.0", "com.stratkit.map": "6.1.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "2.0.0", "com.stratkit.properties-loader": "3.0.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.server-communication": "5.11.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.units": "5.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.army-strength-info-util": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-strength-info-util", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-air": "1.23.0", "com.stratkit.army-command": "3.5.0", "com.stratkit.army-state-loader": "5.17.1", "com.stratkit.buildings": "4.0.0", "com.stratkit.contentitemsmodel": "1.1.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "2.5.0", "com.stratkit.moddable-upgrade-state-loader": "2.0.0", "com.stratkit.mods": "3.8.0", "com.stratkit.properties-commons": "1.7.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.17.1", "com.stratkit.units": "3.24.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.army-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.army-utils", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-state-loader": "5.10.0", "com.stratkit.buildings": "3.5.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.foreign-affairs-state-loader": "2.4.0", "com.stratkit.moddable-upgrade-state-loader": "1.18.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.railroad": "1.3.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.asset-importer": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.asset-importer", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.entities-hybridbaker": "0.1.2", "com.stratkit.zoom": "2.1.1", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.audio": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.audio", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "3.1.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.worldreference": "2.0.2", "com.unity.addressables": "1.21.17", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.sirenix.odininspector": "3.1.14", "com.unity.ugui": "1.0.0", "com.unity.burst": "1.8.8"}}, "com.stratkit.audio-leaders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.audio-leaders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "3.1.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.audio": "3.1.0", "com.stratkit.audio-load-play": "0.3.0", "com.stratkit.core": "1.0.1", "com.stratkit.entities-core": "2.3.0", "com.stratkit.entities-reactive": "1.0.0", "com.stratkit.leaders": "0.2.0", "com.stratkit.properties-loader": "1.1.2", "com.stratkit.worldreference": "2.0.0", "com.unity.addressables": "1.21.17", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.audio-load-play": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.audio-load-play", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "3.1.0", "com.stillfront.logging": "3.1.2", "com.stratkit.audio": "3.1.5", "com.stratkit.core": "1.0.3", "com.unity.addressables": "1.21.17", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.stratkit.addressables-utils": "0.2.2"}}, "com.stratkit.audio-on-selected": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.audio-on-selected", "depth": 0, "source": "local", "dependencies": {"com.stillfront.assetreferencevalidator": "2.1.0", "com.stillfront.audioscriptable": "4.0.0", "com.stillfront.logging": "3.1.4", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.army-state-loader": "5.9.0", "com.stratkit.audio": "3.2.1", "com.stratkit.audio-load-play": "0.3.1", "com.stratkit.cityareas": "0.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.moddable-upgrade-state-loader": "1.18.1", "com.stratkit.player-state-loader": "2.1.1", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.selection": "1.4.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.units": "3.10.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.audio-on-server-command": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.audio-on-server-command", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "4.0.0", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.army-air": "1.16.0", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.9.0", "com.stratkit.audio": "3.2.1", "com.stratkit.audio-load-play": "0.3.1", "com.stratkit.audio-on-selected": "1.0.0", "com.stratkit.core": "1.2.0", "com.stratkit.csv": "1.7.0", "com.stratkit.data": "3.6.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.game-state-loader": "4.6.1", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.server-communication": "5.7.1", "com.stratkit.service-injector": "1.0.1", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.balancing-data-workflow": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.balancing-data-workflow", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.balancing-data-xlsx-export": "0.6.0", "com.stratkit.csv": "1.4.3", "com.stratkit.maps-catalog": "1.5.0", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.scriptable-path": "0.1.1", "com.stratkit.tools.pr-creator": "1.1.4", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.balancing-data-xlsx-export": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.balancing-data-xlsx-export", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.editor-directory-utils": "0.1.3", "com.stratkit.localization": "0.6.0", "com.stratkit.localization-components": "1.0.2", "com.stratkit.mapmaker": "3.1.0", "com.stratkit.mapmaker-shape": "1.14.0", "com.stratkit.mapmaker-visual-terrain-override": "0.2.0", "com.stratkit.map-remote-validation": "0.3.0", "com.stratkit.player-data": "1.2.0", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.4.3", "com.stratkit.provinces": "1.12.0", "com.stratkit.resources": "0.2.0", "com.stratkit.scriptable-path": "0.1.1", "com.stratkit.units": "3.1.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3"}}, "com.stratkit.battle-pass": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.battle-pass", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.buildings": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.buildings", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.cityareas": "0.2.0", "com.stratkit.collections": "2.0.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.csv": "1.8.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.localization": "0.7.0", "com.stratkit.properties-commons": "1.6.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.15.0", "com.stratkit.researches": "2.3.2", "com.stratkit.resources": "1.0.0", "com.stratkit.server-communication": "5.7.1", "com.stratkit.units": "3.14.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.buildings.core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.buildings.core", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16"}}, "com.stratkit.cheats": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cheats", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-selection": "1.4.0", "com.stratkit.army-state-loader": "5.9.0", "com.stratkit.buildings": "3.5.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.0", "com.stratkit.entities-fsm": "1.7.0", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.game-info-state-loader": "1.3.0", "com.stratkit.gamejoining": "4.4.0", "com.stratkit.game-state-loader": "4.6.0", "com.stratkit.map": "5.2.0", "com.stratkit.maps-catalog": "1.7.0", "com.stratkit.moddable-upgrade-state-loader": "1.18.1", "com.stratkit.player-state-loader": "2.1.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.resource-state-loader": "1.7.1", "com.stratkit.selection": "1.4.0", "com.stratkit.server-communication": "5.6.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.3.0", "com.stratkit.user-input-actions": "1.5.1", "com.stratkit.webapi": "3.0.0", "com.stratkit.worldreference": "2.1.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.cheats-jahro": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cheats-jahro", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.cheats": "1.8.0", "com.stratkit.core": "2.1.0", "com.stratkit.core.deeplinks": "2.2.0", "com.stratkit.data": "3.25.1", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.factions": "1.10.0", "com.stratkit.foreign-affairs-state-loader": "4.2.6", "com.stratkit.gamejoining": "7.2.0", "com.stratkit.moddable-upgrade-state-loader": "2.9.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.premium-account": "0.1.0", "com.stratkit.properties-loader": "2.2.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.resources": "2.2.0", "com.stratkit.resource-state-loader": "1.21.0", "com.stratkit.selection": "1.4.2", "com.stratkit.server-communication": "5.11.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.unity-iap-shop": "2.0.0", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.webapi": "5.3.1", "com.stratkit.worldreference": "2.1.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.yasirkula.nativeshare": "1.5.3", "io.jahro.console": "3.0.5"}}, "com.stratkit.city-label": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.city-label", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.3.1", "com.stratkit.buildings": "4.0.0", "com.stratkit.cityareas": "0.2.1", "com.stratkit.collections": "2.1.0", "com.stratkit.contentitemsmodel": "2.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.5.0", "com.stratkit.foreign-affairs-state-loader": "2.7.0", "com.stratkit.gameobject-pool": "1.1.1", "com.stratkit.localization": "1.1.0", "com.stratkit.localization-components": "1.3.0", "com.stratkit.map": "5.9.1", "com.stratkit.math": "1.13.1", "com.stratkit.moddable-upgrade-state-loader": "2.3.0", "com.stratkit.player-data": "1.3.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.properties-commons": "1.10.0", "com.stratkit.properties-loader": "1.10.3", "com.stratkit.provinces": "1.18.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.resources": "1.2.1", "com.stratkit.rts-camera": "2.3.1", "com.stratkit.sectionstreaming": "5.2.0", "com.stratkit.selection": "1.4.2", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.7.0", "com.stratkit.ui-common": "6.13.0", "com.stratkit.user-input-actions": "1.9.0", "com.stratkit.zoom": "3.2.2", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.cityareas": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cityareas", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.validators": "3.1.0", "com.unity.addressables": "1.21.17", "com.unity.entities": "1.0.16", "com.unity.collections": "2.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.logging": "3.1.2", "com.unity.burst": "1.8.8"}}, "com.stratkit.cityareas-empty-slots": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cityareas-empty-slots", "depth": 0, "source": "local", "dependencies": {"com.stratkit.cityareas-render": "1.7.0", "com.unity.entities": "1.0.16", "com.stillfront.logging": "3.1.2", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.addressables-utils": "0.2.2", "com.stratkit.cityareas": "0.1.3", "com.stratkit.core": "1.0.3", "com.stratkit.buildings": "3.0.0", "com.stratkit.provinces": "1.2.1", "com.stratkit.upgrade-grounds": "0.1.1", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.map": "4.0.0", "com.stratkit.math": "1.7.0", "com.stratkit.moddable-upgrade-state-loader": "1.9.0", "com.stratkit.player-state-loader": "1.2.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.render-core": "0.1.3", "com.stratkit.selection": "1.1.1", "com.stratkit.server-communication": "1.7.1", "com.stratkit.user-input-actions": "1.2.1", "com.stratkit.zoom": "2.2.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.1.4", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.cityareas-entity-graphics": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cityareas-entity-graphics", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.0", "com.stillfront.validators": "3.1.0", "com.stratkit.buildings": "3.0.1", "com.stratkit.cityareas": "0.1.3", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.map": "4.5.0", "com.stratkit.math": "1.9.0", "com.stratkit.moddable-upgrade-state-loader": "1.15.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.5.0", "com.stratkit.provinces": "1.13.0", "com.stratkit.selection": "1.3.0", "com.stratkit.user-input-actions": "1.4.0", "com.stratkit.zoom": "2.3.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.cityareas-render": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cityareas-render", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.buildings": "3.0.1", "com.stratkit.cityareas": "0.1.3", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.map": "4.5.0", "com.stratkit.math": "1.9.0", "com.stratkit.moddable-upgrade-state-loader": "1.15.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.provinces": "1.13.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.selection": "1.3.0", "com.stratkit.server-communication": "1.13.0", "com.stratkit.user-input-actions": "1.4.0", "com.stratkit.zoom": "2.3.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.cityareas-selectable": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.cityareas-selectable", "depth": 0, "source": "local", "dependencies": {"com.stratkit.cityareas-render": "1.2.0", "com.unity.entities": "1.0.16"}}, "com.stratkit.coalition-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.coalition-state-loader", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.coalitions": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.coalitions", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.code-tools": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.code-tools", "depth": 0, "source": "local", "dependencies": {"com.stratkit.tools.module-scaffolding": "1.0.0", "com.sirenix.odininspector": "3.1.14", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.stillfront.logging": "3.1.2"}}, "com.stratkit.collections": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.collections", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.math": "1.15.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.combat-indicator": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.combat-indicator", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.army-command": "3.17.1", "com.stratkit.army-entity-graphics": "3.12.0", "com.stratkit.army-offset": "2.5.3", "com.stratkit.army-state-loader": "5.34.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.math": "1.15.0", "com.stratkit.render-core": "1.0.0", "com.stratkit.rts-camera": "2.4.1", "com.stratkit.rts-camera-controller": "0.2.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.13.1", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.zoom": "4.0.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.commons.ironsource": {"version": "file:com.stratkit.commons.ironsource", "depth": 0, "source": "embedded", "dependencies": {}}, "com.stratkit.commons.ui-audio": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.commons.ui-audio", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "4.0.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.audio": "3.1.3", "com.stratkit.core": "1.0.2", "com.stratkit.ui": "1.2.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.commons.ui.fullscreen-culling": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.commons.ui.fullscreen-culling", "depth": 0, "source": "local", "dependencies": {"com.stratkit.core": "1.0.4", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.ui": "1.5.0", "com.stratkit.worldreference": "2.0.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.content-items.level": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.content-items.level", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stratkit.core": "1.0.3", "com.unity.burst": "1.8.8", "com.unity.collections": "2.1.4"}}, "com.stratkit.contentarchives": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.contentarchives", "depth": 0, "source": "local", "dependencies": {"com.stillfront.buildpipeline": "3.0.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.4", "com.stratkit.mapmaker": "2.9.0", "com.stratkit.sectionstreaming": "1.8.0", "com.stratkit.tracking": "1.2.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.contentitems-upload": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.contentitems-upload", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.mapmaker": "2.7.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.properties-loader": "1.4.2", "com.thirdparty.ziplib": "1.4.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.ext.nunit": "1.0.6"}}, "com.stratkit.contentitemsmodel": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.contentitemsmodel", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressables-setup": "2.1.0", "com.stillfront.logging": "3.1.4", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.collections": "2.2.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.server-communication": "5.11.0", "com.stratkit.time": "1.1.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.serialization": "3.1.2", "com.unity.test-framework.performance": "3.0.3"}}, "com.stratkit.core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.core", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.entities-core": "2.16.0", "com.stratkit.service-injector": "1.0.1", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.test-framework": "1.4.5"}}, "com.stratkit.core.deeplinks": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.core.deeplinks", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.core": "1.0.4", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3"}}, "com.stratkit.csv": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.csv", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.1.0", "com.stillfront.validators": "3.3.1", "com.stratkit.contentitemsmodel": "10.2.1", "com.stratkit.editor-directory-utils": "0.2.1", "com.stratkit.properties-commons": "1.21.0", "com.stratkit.properties-loader": "2.2.1", "com.stratkit.scriptable-path": "0.1.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.time": "1.1.0", "com.stratkit.tools.pr-creator": "1.1.4", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.stratkit.csv-audio": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.csv-audio", "depth": 0, "source": "local", "dependencies": {"com.stratkit.csv": "1.3.2", "com.stillfront.audioscriptable": "3.1.0", "com.unity.addressables": "1.21.17", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.csv.city-areas": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.csv.city-areas", "depth": 0, "source": "local", "dependencies": {"com.stratkit.cityareas": "0.1.3", "com.stratkit.csv": "1.3.2", "com.unity.addressables": "1.21.17", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.csv.localization": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.csv.localization", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.5.2", "com.stratkit.localization": "0.6.0"}}, "com.stratkit.csv.units": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.csv.units", "depth": 0, "source": "local", "dependencies": {"com.stratkit.csv": "1.3.2", "com.stratkit.units": "1.11.2", "com.stratkit.properties-loader": "1.4.1", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.data", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.army-state-loader": "5.25.0", "com.stratkit.buildings": "4.1.0", "com.stratkit.contentitemsmodel": "3.7.0", "com.stratkit.core": "2.0.0", "com.stratkit.csv": "1.9.0", "com.stratkit.entities-core": "2.16.1", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-state-loader": "4.9.0", "com.stratkit.localization-components": "1.6.0", "com.stratkit.moddable-upgrade-state-loader": "2.5.0", "com.stratkit.mods": "3.14.0", "com.stratkit.offers": "1.4.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.premiums": "1.11.0", "com.stratkit.production-units": "1.6.0", "com.stratkit.properties-commons": "1.15.0", "com.stratkit.properties-loader": "1.13.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.researches": "2.9.1", "com.stratkit.resources": "1.5.0", "com.stratkit.resource-state-loader": "1.18.0", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.server-communication": "5.10.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.11.0", "com.stratkit.units": "4.4.0", "com.stratkit.worldreference": "2.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.dependency-chain-finder": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.dependency-chain-finder", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.didomi-plugin": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.didomi-plugin", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.diplomacy-borders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.diplomacy-borders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.foreign-affairs-balancing-data": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "1.6.0", "com.stratkit.map": "4.0.0", "com.stratkit.player-state-loader": "1.2.0", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.provinces": "1.2.1", "com.stratkit.service-injector": "1.0.1", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.sirenix.odininspector": "3.1.14", "com.unity.burst": "1.8.15"}}, "com.stratkit.drop-unused-state": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.drop-unused-state", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.game-state-loader": "1.1.4", "com.stratkit.server-communication": "1.6.1", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.burst": "1.8.8"}}, "com.stratkit.editor-directory-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.editor-directory-utils", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.entities-core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.entities-core", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.roslyn-analyzers": "0.3.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.ext.nunit": "2.0.3", "com.unity.inputsystem": "1.7.0", "com.unity.mathematics": "1.3.2", "com.unity.test-framework.performance": "3.0.3"}}, "com.stratkit.entities-fsm": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.entities-fsm", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2"}}, "com.stratkit.entities-hybridbaker": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.entities-hybridbaker", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.entities-core": "2.11.0", "com.stratkit.worldreference": "2.0.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.entities-reactive": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.entities-reactive", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.service-injector": "1.0.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.ext.nunit": "2.0.3", "com.unity.inputsystem": "1.7.0", "com.unity.mathematics": "1.3.2", "com.unity.test-framework.performance": "3.0.3"}}, "com.stratkit.entities-transform-sync": {"version": "0.2.2", "depth": 1, "source": "registry", "dependencies": {"com.stratkit.core": "1.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.physics": "1.3.2"}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stratkit.factions": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.factions", "depth": 0, "source": "local", "dependencies": {"com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.server-communication": "1.12.1", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3"}}, "com.stratkit.firebaseintegration": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.firebaseintegration", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.entities-core": "2.14.0", "com.stratkit.entities-fsm": "1.4.0", "com.stratkit.gamejoining": "2.1.0", "com.stratkit.tracking": "1.3.0", "com.stratkit.webapi": "1.0.2", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.2"}}, "com.stratkit.fog-of-war": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.fog-of-war", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.army-entity-graphics": "3.9.3", "com.stratkit.army-state-loader": "5.24.0", "com.stratkit.contentitemsmodel": "3.6.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.5.0", "com.stratkit.foreign-affairs-state-loader": "4.0.0", "com.stratkit.map": "5.13.0", "com.stratkit.math": "1.15.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.provinces": "1.20.0", "com.stratkit.user-input-actions": "1.9.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2", "com.unity.render-pipelines.core": "17.0.3", "com.unity.render-pipelines.universal": "17.0.3"}}, "com.stratkit.force-update": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.force-update", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stillfront.logging": "3.1.2", "com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.validators": "3.1.0", "com.stratkit.app-version-split": "0.1.1", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.localization": "0.2.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "1.2.4", "com.stratkit.ui-common": "1.9.1", "com.stratkit.webapi": "0.5.0", "com.unity.collections": "2.1.4", "com.unity.burst": "1.8.8"}}, "com.stratkit.forced-march-state": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.forced-march-state", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-input-forced-march": "1.1.0", "com.stratkit.army-state-loader": "5.25.0", "com.stratkit.core": "2.0.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.foreign-affairs-balancing-data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.foreign-affairs-balancing-data", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.1.0", "com.stillfront.validators": "3.3.0", "com.stratkit.collections": "2.1.0", "com.stratkit.contentitemsmodel": "3.9.0", "com.stratkit.entities-core": "2.16.1", "com.stratkit.localization": "1.1.0", "com.stratkit.localization-components": "1.6.0", "com.stratkit.properties-loader": "1.14.0", "com.stratkit.ui-common": "6.21.0", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.foreign-affairs-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.foreign-affairs-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "6.1.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.6.2", "com.stratkit.game-state-loader": "4.9.3", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "1.18.0", "com.stratkit.properties-loader": "1.18.1", "com.stratkit.resources": "2.0.0", "com.stratkit.resource-state-loader": "1.20.1", "com.stratkit.server-communication": "5.10.0", "com.stratkit.webapi": "4.31.1", "com.stratkit.worldreference": "2.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.game-end": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.game-end", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.game-info-state-loader": "1.2.0", "com.stratkit.newspaper-state-loader": "2.0.0", "com.stratkit.player-and-team-ranking": "1.2.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.properties-loader": "1.4.3", "com.stratkit.provinces": "1.13.0", "com.stratkit.server-communication": "1.12.1", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3"}}, "com.stratkit.game-events": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.game-events", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-state-loader": "5.33.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.game-state-loader": "4.9.5", "com.stratkit.map": "6.0.0", "com.stratkit.moddable-upgrade-state-loader": "2.7.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "1.20.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.researches": "2.12.0", "com.stratkit.resources": "2.1.0", "com.stratkit.server-communication": "5.11.0", "com.stratkit.tracking": "1.4.2", "com.stratkit.units": "5.1.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.game-info-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.game-info-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.game-state-loader": "4.9.0", "com.stratkit.properties-commons": "1.17.1", "com.stratkit.server-communication": "5.10.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.game-server-pretender": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.game-server-pretender", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.map": "4.0.0", "com.stratkit.math": "1.7.0", "com.stratkit.player-data": "1.1.5", "com.stratkit.player-state-loader": "1.2.0", "com.stratkit.properties-commons": "0.3.3", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.provinces": "1.2.1", "com.stratkit.resources": "0.1.2", "com.stratkit.service-injector": "1.0.1", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.stratkit.game-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.game-state-loader", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.logging": "3.1.4", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.properties-loader": "1.18.2", "com.stratkit.server-communication": "5.11.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.webapi.data": "1.4.0", "com.stratkit.worldreference": "2.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.gamejoining": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.gamejoining", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.app-navigation": "0.4.0", "com.stratkit.app-version-split": "0.1.1", "com.stratkit.contentarchives": "2.6.2", "com.stratkit.contentitemsmodel": "3.4.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-fsm": "1.9.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.5.0", "com.stratkit.game-end": "1.1.0", "com.stratkit.game-info-state-loader": "1.5.0", "com.stratkit.game-state-loader": "4.8.0", "com.stratkit.localization": "1.1.0", "com.stratkit.map": "5.11.0", "com.stratkit.maps-catalog": "1.9.0", "com.stratkit.mods": "3.13.0", "com.stratkit.music": "0.3.0", "com.stratkit.performance-core": "0.1.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.properties-commons": "1.13.0", "com.stratkit.properties-loader": "1.11.0", "com.stratkit.provinces": "1.19.0", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.rts-camera": "2.3.1", "com.stratkit.sectionstreaming": "5.2.0", "com.stratkit.server-communication": "5.9.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.tracking-performance": "2.1.0", "com.stratkit.ui": "2.11.0", "com.stratkit.ui.minimap": "0.2.1", "com.stratkit.ui-common": "6.18.0", "com.stratkit.webapi": "4.18.1", "com.stratkit.webapi.data": "1.4.0", "com.stratkit.worldreference": "2.1.0", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.gameobject-pool": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.gameobject-pool", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.worldreference": "2.1.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.heroes": {"version": "file:com.stratkit.heroes", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0"}}, "com.stratkit.leaders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.leaders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.entities-core": "2.10.1", "com.stratkit.entities-reactive": "1.0.1", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.localization": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.localization", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.1.0", "com.stillfront.validators": "3.3.0", "com.unity.addressables": "2.3.16", "com.unity.localization": "1.5.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.localization-components": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.localization-components", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.localization": "0.7.0", "com.stratkit.properties-loader": "1.6.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.2", "com.unity.localization": "1.5.2", "com.unity.textmeshpro": "3.0.9"}}, "com.stratkit.map": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.map", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "6.0.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-state-loader": "4.9.1", "com.stratkit.math": "1.15.0", "com.stratkit.pathfinding": "3.6.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "1.18.0", "com.stratkit.properties-loader": "1.18.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.sectionstreaming": "5.7.0", "com.stratkit.server-communication": "5.10.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.units": "4.8.1", "com.stratkit.user-input-actions": "1.9.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.map-feedback-entity-graphics": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.map-feedback-entity-graphics", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.3.0", "com.stratkit.army-entity-graphics": "3.13.0", "com.stratkit.army-state-loader": "5.35.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-transform-sync": "0.2.2", "com.stratkit.math": "1.16.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.map-icons": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.map-icons", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.game-info-state-loader": "1.3.0", "com.stratkit.gameobject-pool": "1.0.1", "com.stratkit.map": "5.2.0", "com.stratkit.math": "1.13.1", "com.stratkit.properties-commons": "1.4.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.resources": "1.0.0", "com.stratkit.rts-camera": "2.0.1", "com.stratkit.scenario": "2.2.0", "com.stratkit.sectionstreaming": "4.2.0", "com.stratkit.server-communication": "5.6.2", "com.stratkit.ui": "2.3.0", "com.stratkit.ui-common": "6.2.0", "com.stratkit.webapi.data": "0.2.0", "com.stratkit.worldreference": "2.1.0", "com.stratkit.zoom": "3.2.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.map-remote-validation": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.map-remote-validation", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.editor-directory-utils": "0.1.3", "com.stratkit.mapmaker": "4.5.0", "com.stratkit.scriptable-path": "0.1.1"}}, "com.stratkit.mapmaker": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.editorprogressbar": "4.0.4", "com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.cityareas": "0.2.0", "com.stratkit.editor-directory-utils": "0.2.1", "com.stratkit.localization": "1.0.0", "com.stratkit.localization-components": "1.1.0", "com.stratkit.map": "5.7.0", "com.stratkit.maps-catalog": "1.7.0", "com.stratkit.math": "1.13.1", "com.stratkit.player-data": "1.2.1", "com.stratkit.properties-commons": "1.6.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.16.0", "com.stratkit.resources": "1.1.0", "com.stratkit.scriptable-path": "0.1.1", "com.stratkit.sectionstreaming": "5.2.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.snapping-points": "0.6.0", "com.stratkit.snapping-points-generator": "2.2.1", "com.stratkit.units": "3.18.0", "com.stratkit.zoom": "3.2.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.formats.fbx": "5.1.1", "com.unity.mathematics": "1.3.2", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.vectorgraphics": "2.0.0-preview.24"}}, "com.stratkit.mapmaker-borders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-borders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.entities-core": "2.11.0", "com.stratkit.map": "4.2.0", "com.stratkit.mapmaker": "2.15.0", "com.stratkit.math": "1.9.0", "com.stratkit.sectionstreaming": "1.8.1", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.mapmaker-cities": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-cities", "depth": 0, "source": "local", "dependencies": {"com.stratkit.cityareas": "0.1.3", "com.stratkit.mapmaker": "2.10.0", "com.stratkit.math": "1.7.0", "com.stratkit.properties-loader": "1.4.2", "com.stratkit.sectionstreaming": "1.8.0", "com.stratkit.zoom": "2.2.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.stratkit.upgrade-grounds": "0.1.1"}}, "com.stratkit.mapmaker-coastlines": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-coastlines", "depth": 0, "source": "local", "dependencies": {"com.stratkit.mapmaker": "2.10.0", "com.stratkit.sectionstreaming": "1.8.0", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.mapmaker-extruded-borders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-extruded-borders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.2.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.map": "5.9.0", "com.stratkit.mapmaker": "5.2.2", "com.stratkit.math": "1.13.1", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.mapmaker-minimap": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-minimap", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressables-setup": "2.1.0", "com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.1.0", "com.stillfront.validators": "3.3.1", "com.stratkit.mapmaker": "5.8.1", "com.stratkit.properties-loader": "2.2.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.ui.minimap": "0.3.0", "com.stratkit.ui-common": "7.0.0", "com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.3.2", "com.unity.vectorgraphics": "2.0.0-preview.24"}}, "com.stratkit.mapmaker-props": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-props", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.cityareas": "0.1.3", "com.stratkit.entities-hybridbaker": "0.2.0", "com.stratkit.map": "4.1.0", "com.stratkit.mapmaker": "2.10.0", "com.stratkit.math": "1.7.0", "com.stratkit.sectionstreaming": "1.8.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.zoom": "2.2.0", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.stratkit.upgrade-grounds": "0.1.1", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.mapmaker-roads": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-roads", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.map": "5.12.0", "com.stratkit.mapmaker": "5.3.1", "com.stratkit.math": "1.15.0", "com.stratkit.sectionstreaming": "5.2.0", "com.stratkit.service-injector": "1.0.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2", "com.unity.splines": "2.7.2"}}, "com.stratkit.mapmaker-shape": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-shape", "depth": 0, "source": "local", "dependencies": {"com.stillfront.editorprogressbar": "4.0.4", "com.stillfront.logging": "3.1.4", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.cityareas": "0.2.1", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "6.0.0", "com.stratkit.csv": "1.9.1", "com.stratkit.localization": "1.2.0", "com.stratkit.localization-components": "1.6.0", "com.stratkit.map": "6.0.0", "com.stratkit.mapmaker": "5.6.1", "com.stratkit.mapmaker-visual-terrain-override": "0.2.0", "com.stratkit.math": "1.15.0", "com.stratkit.player-data": "1.4.0", "com.stratkit.properties-commons": "1.18.0", "com.stratkit.properties-loader": "1.18.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.resources": "2.0.0", "com.stratkit.sectionstreaming": "5.7.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.editorcoroutines": "1.0.0", "com.unity.entities": "1.3.8", "com.unity.localization": "1.5.2", "com.unity.mathematics": "1.3.2", "com.unity.vectorgraphics": "2.0.0-preview.24"}}, "com.stratkit.mapmaker-terrain": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-terrain", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.map": "4.2.0", "com.stratkit.mapmaker": "2.13.0", "com.stratkit.math": "1.9.0", "com.stratkit.sectionstreaming": "1.8.1", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.mapmaker-visual-terrain-override": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mapmaker-visual-terrain-override", "depth": 0, "source": "local", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.5.0", "com.stratkit.properties-loader": "1.4.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.maps-catalog": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.maps-catalog", "depth": 0, "source": "local", "dependencies": {"com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.6.1", "com.stratkit.foreign-affairs-balancing-data": "2.1.0", "com.stratkit.mapmaker": "4.1.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.rts-camera": "2.0.1", "com.stratkit.scriptable-path": "0.1.1", "com.stratkit.sectionstreaming": "4.1.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui.minimap": "0.2.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.math": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.math", "depth": 0, "source": "local", "dependencies": {"com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.mechanics.missions": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mechanics.missions", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.1", "com.stratkit.army-command": "3.19.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.contentitemsmodel": "11.3.0", "com.stratkit.core": "2.1.0", "com.stratkit.data": "3.25.1", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-end": "1.1.0", "com.stratkit.game-state-loader": "4.10.0", "com.stratkit.localization": "1.3.0", "com.stratkit.localization-components": "2.0.1", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-commons": "2.0.0", "com.stratkit.properties-loader": "3.0.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.researches": "2.14.0", "com.stratkit.research-state-loader": "0.6.0", "com.stratkit.server-communication": "5.11.0", "com.stratkit.statistics-state-loader": "0.5.0", "com.stratkit.units": "5.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.moddable-upgrade-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.moddable-upgrade-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-state-loader": "5.20.1", "com.stratkit.buildings": "4.0.0", "com.stratkit.cityareas": "0.2.1", "com.stratkit.collections": "2.1.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.contentitemsmodel": "2.2.0", "com.stratkit.core": "1.3.1", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-info-state-loader": "1.4.0", "com.stratkit.game-state-loader": "4.6.2", "com.stratkit.map": "5.9.1", "com.stratkit.mods": "3.11.0", "com.stratkit.player-data": "1.3.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.production-core": "1.2.0", "com.stratkit.properties-commons": "1.10.0", "com.stratkit.properties-loader": "1.10.0", "com.stratkit.provinces": "1.18.0", "com.stratkit.server-communication": "5.8.0", "com.stratkit.time": "1.1.0", "com.stratkit.units": "4.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.mods": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.mods", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.buildings": "4.0.0", "com.stratkit.contentitemsmodel": "3.0.2", "com.stratkit.csv": "1.9.0", "com.stratkit.data": "3.10.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.foreign-affairs-balancing-data": "2.5.0", "com.stratkit.offers": "1.1.0", "com.stratkit.offersutils": "0.1.1", "com.stratkit.premiums": "1.6.0", "com.stratkit.properties-commons": "1.11.0", "com.stratkit.properties-loader": "1.10.4", "com.stratkit.provinces": "1.18.0", "com.stratkit.ranks-data": "0.1.1", "com.stratkit.researches": "2.6.0", "com.stratkit.resources": "1.2.1", "com.stratkit.scenario": "2.4.0", "com.stratkit.server-communication": "5.8.0", "com.stratkit.time": "1.1.0", "com.stratkit.units": "4.1.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.music": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.music", "depth": 0, "source": "local", "dependencies": {"com.stillfront.audioscriptable": "4.0.0", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.audio": "3.2.4", "com.stratkit.core": "2.0.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.newspaper-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.newspaper-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.army-state-loader": "5.23.0", "com.stratkit.core": "2.0.0", "com.stratkit.game-info-state-loader": "1.5.0", "com.stratkit.game-state-loader": "4.8.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.properties-commons": "1.13.0", "com.stratkit.properties-loader": "1.11.0", "com.stratkit.provinces": "1.19.0", "com.stratkit.server-communication": "5.9.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.units": "4.3.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.offers": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.offers", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.premiums": "0.2.0", "com.stratkit.properties-commons": "1.0.2", "com.stratkit.properties-loader": "1.6.0", "com.stratkit.server-communication": "5.0.0", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4"}}, "com.stratkit.offersutils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.offersutils", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.properties-commons": "0.3.2"}}, "com.stratkit.orientfightingarmies": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.orient-fighting-armies", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.pathfinding": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.pathfinding", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-state-loader": "5.9.0", "com.stratkit.collections": "2.0.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.map": "5.5.0", "com.stratkit.math": "1.13.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.sectionstreaming": "4.2.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.ext.nunit": "2.0.3", "com.unity.inputsystem": "1.7.0", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.performance-core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.performance-core", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.3.5"}}, "com.stratkit.pinging-system": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.pinging-system", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.contentitemsmodel": "3.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-fsm": "1.8.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "2.8.0", "com.stratkit.game-info-state-loader": "1.4.0", "com.stratkit.game-state-loader": "4.8.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.properties-commons": "1.13.0", "com.stratkit.properties-loader": "1.11.0", "com.stratkit.selection": "1.4.2", "com.stratkit.server-communication": "5.9.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.10.0", "com.stratkit.ui-common": "6.17.0", "com.stratkit.user-input-actions": "1.9.1", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.player-and-team-ranking": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.player-and-team-ranking", "depth": 0, "source": "local", "dependencies": {"com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.game-info-state-loader": "1.2.0", "com.stratkit.game-state-loader": "2.0.0", "com.stratkit.newspaper-state-loader": "2.0.1", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.6.0", "com.stratkit.provinces": "1.13.0", "com.stratkit.scenario": "1.1.0", "com.stratkit.server-communication": "2.0.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.player-borders": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.player-borders", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.1.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.6.2", "com.stratkit.foreign-affairs-state-loader": "4.2.6", "com.stratkit.map": "6.1.0", "com.stratkit.math": "1.15.0", "com.stratkit.player-state-loader": "2.7.0", "com.stratkit.properties-loader": "2.0.0", "com.stratkit.provinces": "1.20.1", "com.stratkit.sectionstreaming": "6.0.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.zoom": "4.0.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.player-data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.player-data", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.1.0", "com.stratkit.localization": "0.2.1", "com.stratkit.localization-components": "1.0.1", "com.stratkit.properties-commons": "0.3.2", "com.stratkit.properties-loader": "1.4.1", "com.stratkit.provinces": "1.1.1", "com.stratkit.server-communication": "1.7.0", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.player-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.player-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.factions": "1.2.0", "com.stratkit.game-state-loader": "4.6.1", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.server-communication": "5.7.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.player-territory-label": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.player-territory-label", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.2.3", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-balancing-data": "2.4.0", "com.stratkit.foreign-affairs-state-loader": "2.5.0", "com.stratkit.gameobject-pool": "1.1.1", "com.stratkit.localization": "1.0.0", "com.stratkit.localization-components": "1.1.0", "com.stratkit.map": "5.8.0", "com.stratkit.math": "1.13.1", "com.stratkit.player-data": "1.2.1", "com.stratkit.player-state-loader": "2.5.0", "com.stratkit.properties-commons": "1.7.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.17.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.sectionstreaming": "5.2.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.zoom": "3.2.1", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.premium-account": {"version": "0.1.0", "depth": 1, "source": "registry", "dependencies": {"com.stratkit.contentitemsmodel": "10.2.1", "com.stratkit.core": "2.1.0", "com.stratkit.data": "3.25.1", "com.stratkit.player-state-loader": "2.7.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stratkit.premiums": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.premiums", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.3.2", "com.stratkit.resources": "0.1.1", "com.stratkit.units": "1.11.2", "com.stratkit.offersutils": "0.1.1", "com.stratkit.properties-commons": "0.3.2", "com.stratkit.properties-loader": "1.4.1", "com.stratkit.time": "0.1.1", "com.unity.collections": "2.1.4", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.sirenix.odininspector": "3.1.14", "com.unity.burst": "1.8.8"}}, "com.stratkit.production-core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.production-core", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stratkit.provinces": "1.1.1", "com.stratkit.server-communication": "1.6.1", "com.stratkit.game-info-state-loader": "0.3.1", "com.unity.collections": "2.1.4", "com.unity.mathematics": "1.2.6"}}, "com.stratkit.production-units": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.production-units", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.army-state-loader": "2.0.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.game-info-state-loader": "1.1.0", "com.stratkit.game-state-loader": "1.1.4", "com.stratkit.moddable-upgrade-state-loader": "1.9.0", "com.stratkit.player-state-loader": "1.2.0", "com.stratkit.production-core": "1.0.2", "com.stratkit.provinces": "1.2.1", "com.stratkit.server-communication": "1.7.1", "com.stratkit.units": "1.12.1", "com.unity.burst": "1.8.15", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.stratkit.map": "4.0.0"}}, "com.stratkit.properties-commons": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.properties-commons", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.8.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.server-communication": "5.7.1", "com.unity.addressables": "1.21.21", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.ext.nunit": "2.0.3", "com.unity.mathematics": "1.3.2", "com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.stratkit.properties-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.properties-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.2.0", "com.stratkit.contentitemsmodel": "1.4.0", "com.stratkit.core": "1.2.0", "com.stratkit.csv": "1.9.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.server-communication": "5.7.1", "com.stratkit.service-injector": "1.0.1", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2"}}, "com.stratkit.province-production-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.province-production-utils", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-air": "1.13.0", "com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.2.0", "com.stratkit.buildings": "3.2.0", "com.stratkit.collections": "1.6.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.data": "1.5.0", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.foreign-affairs-state-loader": "1.7.0", "com.stratkit.game-state-loader": "1.5.1", "com.stratkit.moddable-upgrade-state-loader": "1.16.0", "com.stratkit.mods": "1.7.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.production-core": "1.1.0", "com.stratkit.production-units": "1.5.0", "com.stratkit.properties-commons": "1.0.1", "com.stratkit.properties-loader": "1.6.0", "com.stratkit.provinces": "1.13.0", "com.stratkit.railroad": "1.2.0", "com.stratkit.resources": "0.2.0", "com.stratkit.server-communication": "1.13.1", "com.stratkit.units": "3.3.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.mathematics": "1.3.1"}}, "com.stratkit.provinces": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.provinces", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.2.0", "com.stratkit.contentitemsmodel": "1.6.0", "com.stratkit.core": "1.2.0", "com.stratkit.csv": "1.8.1", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.player-state-loader": "2.5.0", "com.stratkit.properties-commons": "1.7.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.resources": "1.1.0", "com.stratkit.server-communication": "5.7.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.railroad": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.railroad", "depth": 0, "source": "local", "dependencies": {"com.stratkit.buildings": "4.0.0", "com.stratkit.core": "1.3.1", "com.stratkit.entities-core": "2.16.0", "com.stratkit.map": "5.9.0", "com.stratkit.moddable-upgrade-state-loader": "2.1.0", "com.stratkit.sectionstreaming": "5.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.ranks-data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ranks-data", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stratkit.properties-loader": "1.4.1", "com.stillfront.validators": "3.1.0", "com.stratkit.properties-commons": "0.3.2", "com.unity.collections": "2.1.4"}}, "com.stratkit.reflection-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.reflection-utils", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2"}}, "com.stratkit.render-core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.render-core", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stillfront.logging": "3.1.2", "com.stratkit.gameobject-pool": "0.1.1", "com.unity.collections": "2.1.4", "com.unity.burst": "1.8.8"}}, "com.stratkit.research-state-loader": {"version": "0.6.0", "depth": 1, "source": "registry", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.0.5", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.factions": "1.2.0", "com.stratkit.game-info-state-loader": "1.2.0", "com.stratkit.game-state-loader": "4.3.2", "com.stratkit.premiums": "0.2.0", "com.stratkit.properties-commons": "1.0.2", "com.stratkit.properties-loader": "1.6.0", "com.stratkit.researches": "2.3.1", "com.stratkit.server-communication": "5.1.0", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.3", "com.unity.entities": "1.2.4", "com.unity.mathematics": "1.3.1"}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stratkit.researches": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.researches", "depth": 0, "source": "local", "dependencies": {"com.stillfront.validators": "3.2.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.contentitemsmodel": "3.6.1", "com.stratkit.csv": "1.9.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.localization-components": "1.6.0", "com.stratkit.premiums": "1.10.0", "com.stratkit.properties-commons": "1.15.0", "com.stratkit.properties-loader": "1.13.0", "com.stratkit.server-communication": "5.10.0", "com.stratkit.time": "1.1.0", "com.stratkit.ui": "2.11.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.resource-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.resource-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.contentitemsmodel": "2.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.game-info-state-loader": "1.4.0", "com.stratkit.game-state-loader": "4.6.2", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.properties-commons": "1.10.0", "com.stratkit.properties-loader": "1.10.3", "com.stratkit.server-communication": "5.8.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.resources": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.resources", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.csv": "1.4.2", "com.stratkit.properties-commons": "0.3.2", "com.stratkit.properties-loader": "1.4.1", "com.stratkit.server-communication": "1.7.0", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.ext.nunit": "1.0.6"}}, "com.stratkit.roslyn-analyzers": {"version": "1.6.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stratkit.rts-camera": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.rts-camera", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.sectionstreaming": "5.8.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.user-input-actions": "1.9.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.rts-camera-controller": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.rts-camera-controller", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.rts-camera": "1.3.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.user-input-actions": "1.2.1", "com.unity.collections": "2.1.4", "com.unity.mathematics": "1.2.6", "com.stillfront.logging": "3.1.2", "com.unity.burst": "1.8.8"}}, "com.stratkit.rts-camera-start-on-country": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.rts-camera-start-on-country", "depth": 0, "source": "local", "dependencies": {"com.stratkit.collections": "2.1.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "5.11.0", "com.stratkit.math": "1.15.0", "com.stratkit.moddable-upgrade-state-loader": "2.4.0", "com.stratkit.player-state-loader": "2.6.0", "com.stratkit.provinces": "1.19.0", "com.stratkit.rts-camera-controller": "0.1.4", "com.stratkit.service-injector": "1.0.1", "com.stratkit.zoom": "3.3.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.scenario": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.scenario", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.army-state-loader": "5.9.0", "com.stratkit.csv": "1.7.0", "com.stratkit.properties-commons": "1.4.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.ranks-data": "0.1.1", "com.stratkit.researches": "2.3.2", "com.stratkit.server-communication": "5.6.2", "com.stratkit.time": "0.1.1", "com.stratkit.units": "3.10.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.scriptable-path": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.scriptable-path", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.sectionstreaming": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.sectionstreaming", "depth": 0, "source": "local", "dependencies": {"com.stillfront.editorprogressbar": "4.0.4", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.2.0", "com.stratkit.editor-directory-utils": "0.1.3", "com.stratkit.entities-core": "2.15.1", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.mapmaker": "4.2.0", "com.stratkit.math": "1.13.1", "com.stratkit.render-core": "0.1.3", "com.stratkit.user-input-actions": "1.6.0", "com.stratkit.zoom": "3.2.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.entities.graphics": "1.2.3", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.selection": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.selection", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-fsm": "1.8.0", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.ui": "2.3.0", "com.stratkit.user-input-actions": "1.6.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.ext.nunit": "2.0.3", "com.unity.inputsystem": "1.7.0", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.selection-province": {"version": "file:com.stratkit.selection-province", "depth": 0, "source": "embedded", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-fsm": "1.9.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "5.10.0", "com.stratkit.provinces": "1.19.0", "com.stratkit.selection": "1.4.2", "com.stratkit.user-input-actions": "1.9.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.server-communication": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.server-communication", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.collections": "2.2.0", "com.stratkit.contentitemsmodel": "6.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.map": "6.0.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2", "com.unity.nuget.newtonsoft-json": "3.2.1"}}, "com.stratkit.service-injector": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.service-injector", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.unity.collections": "2.1.4", "com.sirenix.odininspector": "3.1.14", "com.unity.test-framework": "1.1.33", "com.unity.ext.nunit": "1.0.6", "com.unity.burst": "1.8.8", "com.stillfront.logging": "3.1.2"}}, "com.stratkit.shophandler-bytro": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.shophandler-bytro", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.offers": "1.6.0", "com.stratkit.properties-loader": "1.18.2", "com.stratkit.unity-iap-shop": "0.4.7", "com.stratkit.webapi": "4.32.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.purchasing": "4.13.0"}}, "com.stratkit.snapping-points": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.snapping-points", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.map": "6.0.0", "com.stratkit.math": "1.15.0", "com.stratkit.sectionstreaming": "5.7.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.zoom": "3.4.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.snapping-points-generator": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.snapping-points-generator", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.mapmaker": "4.2.0", "com.stratkit.math": "1.13.1", "com.stratkit.sectionstreaming": "4.2.0", "com.stratkit.snapping-points": "0.5.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.entities.graphics": "1.2.3", "com.unity.mathematics": "1.3.2", "com.unity.physics": "1.0.16"}}, "com.stratkit.snapping-points-renderer": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.snapping-points-renderer", "depth": 0, "source": "local", "dependencies": {"com.stratkit.core": "1.0.3", "com.stratkit.snapping-points": "0.3.1", "com.stratkit.user-input-actions": "1.2.1", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.burst": "1.8.8"}}, "com.stratkit.solution-generator": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.solution-generator", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.unity.ide.visualstudio": "2.0.22"}}, "com.stratkit.statistics-state-loader": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.statistics-state-loader", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.army-state-loader": "5.10.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.game-state-loader": "4.6.1", "com.stratkit.player-state-loader": "2.1.2", "com.stratkit.properties-commons": "1.5.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.server-communication": "5.7.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.units": "3.12.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.terrain-pathfinding": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.terrain-pathfinding", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-state-loader": "5.9.0", "com.stratkit.buildings": "3.5.0", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.1", "com.stratkit.entities-reactive": "1.3.0", "com.stratkit.foreign-affairs-balancing-data": "2.2.0", "com.stratkit.foreign-affairs-state-loader": "2.4.0", "com.stratkit.map": "5.5.0", "com.stratkit.moddable-upgrade-state-loader": "1.18.1", "com.stratkit.pathfinding": "3.0.0", "com.stratkit.player-state-loader": "2.1.2", "com.stratkit.provinces": "1.14.0", "com.stratkit.railroad": "1.3.0", "com.stratkit.units": "3.11.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.time": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.time", "depth": 0, "source": "local", "dependencies": {"com.unity.ext.nunit": "1.0.6", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16"}}, "com.stratkit.tools.engine-workflow": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tools.engine-workflow", "depth": 0, "source": "local", "dependencies": {"com.stratkit.editor-directory-utils": "0.1.2", "com.stillfront.logging": "3.1.2", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.tools.module-scaffolding": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tools.module-scaffolding", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.package-dependencies-updater": "1.0.3", "com.stratkit.tools.engine-workflow": "0.1.1"}}, "com.stratkit.tools.moq": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tools.moq", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.tools.pr-creator": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tools.pr-creator", "depth": 0, "source": "local", "dependencies": {"com.sirenix.odininspector": "3.1.14", "com.stillfront.logging": "3.1.2"}}, "com.stratkit.tracking": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tracking", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.worldreference": "2.0.2", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.ext.nunit": "1.0.6"}}, "com.stratkit.tracking-performance": {"version": "2.1.0", "depth": 1, "source": "registry", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.1.0", "com.stratkit.core": "1.1.0", "com.stratkit.entities-core": "2.14.0", "com.stratkit.rts-camera": "1.7.0", "com.stratkit.tracking": "1.3.0", "com.stratkit.user-input-actions": "1.5.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}, "url": "https://npm.pkg.github.com/@bytro"}, "com.stratkit.tracking.bytro": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.tracking.bytro", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.1.0", "com.stratkit.app-version-split": "0.1.1", "com.stratkit.core": "1.0.6", "com.stratkit.entities-core": "2.14.0", "com.stratkit.gamejoining": "1.26.0", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.roslyn-analyzers": "0.2.2", "com.stratkit.server-communication": "5.2.0", "com.stratkit.tracking": "1.2.0", "com.stratkit.tracking-performance": "1.0.0", "com.stratkit.webapi": "0.13.0", "com.stratkit.worldreference": "2.0.2", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.2"}}, "com.stratkit.transport-ship": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.transport-ship", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-command": "3.0.0", "com.stratkit.army-state-loader": "5.5.0", "com.stratkit.core": "1.1.0", "com.stratkit.entities-core": "2.14.0", "com.stratkit.entities-reactive": "1.2.0", "com.stratkit.foreign-affairs-state-loader": "2.1.2", "com.stratkit.map": "5.0.0", "com.stratkit.mods": "3.4.0", "com.stratkit.pathfinding": "2.11.0", "com.stratkit.player-state-loader": "2.1.0", "com.stratkit.properties-commons": "1.2.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.14.0", "com.stratkit.scenario": "2.1.0", "com.stratkit.server-communication": "5.5.0", "com.stratkit.units": "3.8.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.ui": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.contentitemsmodel": "3.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.properties-loader": "1.11.0", "com.stratkit.tracking": "1.4.2", "com.stratkit.worldreference": "2.1.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.test-framework": "1.4.5", "com.unity.ugui": "2.0.0"}}, "com.stratkit.ui-buildings.indicators.city-labels": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui-buildings.indicators.city-labels", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.buildings": "4.0.0", "com.stratkit.cityareas": "0.2.1", "com.stratkit.cityareas-render": "1.9.0", "com.stratkit.city-label": "5.1.2", "com.stratkit.core": "1.2.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.map": "5.8.0", "com.stratkit.moddable-upgrade-state-loader": "2.0.0", "com.stratkit.player-state-loader": "2.4.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.16.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.ui": "2.4.0", "com.stratkit.ui-buildings.indicators.core": "1.2.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.ui-buildings.indicators.core": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui-buildings.indicators.core", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.buildings": "3.0.0", "com.stratkit.cityareas": "0.1.3", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.core": "1.0.4", "com.stratkit.entities-core": "2.13.0", "com.stratkit.entities-reactive": "1.1.0", "com.stratkit.map": "4.3.0", "com.stratkit.moddable-upgrade-state-loader": "1.10.0", "com.stratkit.player-state-loader": "2.0.2", "com.stratkit.provinces": "1.7.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.ui": "1.8.0", "com.stratkit.ui-common": "2.2.4", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.4.2", "com.unity.entities": "1.2.3", "com.unity.mathematics": "1.3.1", "com.unity.textmeshpro": "3.0.9"}}, "com.stratkit.ui-buildings.indicators.map": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui-buildings.indicators.map", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.0.0", "com.stillfront.logging": "3.1.2", "com.stillfront.validators": "3.1.0", "com.stratkit.buildings": "3.0.0", "com.stratkit.cityareas": "0.1.3", "com.stratkit.cityareas-render": "1.7.0", "com.stratkit.city-label": "3.1.0", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.11.0", "com.stratkit.entities-reactive": "1.0.1", "com.stratkit.map": "4.0.0", "com.stratkit.moddable-upgrade-state-loader": "1.9.0", "com.stratkit.player-state-loader": "1.2.0", "com.stratkit.render-core": "0.1.3", "com.stratkit.rts-camera": "1.7.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "1.4.0", "com.stratkit.ui-buildings.indicators.core": "1.1.0", "com.unity.addressables": "1.21.21", "com.unity.burst": "1.8.15", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.unity.mathematics": "1.2.6", "com.stratkit.addressables-utils": "0.2.2"}}, "com.stratkit.ui-common": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui-common", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.1", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.contentitemsmodel": "11.3.0", "com.stratkit.core": "2.1.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.1", "com.stratkit.game-info-state-loader": "1.5.1", "com.stratkit.localization": "1.3.0", "com.stratkit.localization-components": "2.0.1", "com.stratkit.math": "1.16.0", "com.stratkit.properties-commons": "2.0.0", "com.stratkit.properties-loader": "3.0.0", "com.stratkit.resource-state-loader": "1.21.0", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.sectionstreaming": "6.1.0", "com.stratkit.server-communication": "5.11.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.13.1", "com.stratkit.units": "5.2.0", "com.stratkit.worldreference": "2.1.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0"}}, "com.stratkit.ui-usersettings": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui-usersettings", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stratkit.core": "1.0.3", "com.stratkit.entities-core": "2.10.1", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui-common": "1.9.1", "com.stratkit.webapi": "0.13.0", "com.unity.collections": "2.1.4", "com.unity.entities": "1.0.16", "com.sirenix.odininspector": "3.1.14", "com.unity.burst": "1.8.8"}}, "com.stratkit.ui.floating-text": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui.floating-text", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.localization": "1.1.0", "com.stratkit.localization-components": "1.5.0", "com.stratkit.selection": "1.4.2", "com.stratkit.ui": "2.11.0", "com.stratkit.user-input-actions": "1.9.1", "com.stratkit.worldreference": "2.1.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.ui.minimap": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui.minimap", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.1", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.ui.pagination": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui.pagination", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.2", "com.stillfront.preparevalidate": "5.0.7", "com.thefallengames.osa": "0.1.0", "com.stillfront.validators": "3.1.0", "com.stratkit.ui.scrollview": "0.1.3", "com.unity.textmeshpro": "3.0.6", "com.unity.ugui": "1.0.0", "com.sirenix.odininspector": "3.1.14"}}, "com.stratkit.ui.scrollview": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.ui.scrollview", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.ui": "2.11.1", "com.thefallengames.osa": "0.1.0", "com.unity.test-framework": "1.4.5"}}, "com.stratkit.units": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.units", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.preparevalidate": "5.0.7", "com.stillfront.validators": "3.2.0", "com.stratkit.collections": "2.1.0", "com.stratkit.content-items.level": "0.1.1", "com.stratkit.csv": "1.8.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.factions": "1.2.0", "com.stratkit.localization": "1.0.0", "com.stratkit.localization-components": "1.1.0", "com.stratkit.properties-commons": "1.7.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.16.0", "com.stratkit.resources": "1.1.0", "com.stratkit.server-communication": "5.7.1", "com.stratkit.time": "1.0.0", "com.unity.addressables": "2.2.2", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.stratkit.unity-iap-shop": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.unity-iap-shop", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.core": "2.0.0", "com.stratkit.csv": "1.11.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.properties-loader": "1.18.2", "com.stratkit.webapi": "4.32.1", "com.unity.addressables": "2.3.16", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.purchasing": "4.13.0", "com.unity.services.core": "1.14.0"}}, "com.stratkit.upgrade-grounds": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.upgrade-grounds", "depth": 0, "source": "local", "dependencies": {"com.unity.entities": "1.0.16", "com.stratkit.cityareas": "0.1.3", "com.stratkit.properties-loader": "1.4.1", "com.unity.addressables": "1.21.17", "com.stillfront.validators": "3.1.0", "com.stillfront.preparevalidate": "5.0.7", "com.stratkit.addressables-utils": "0.2.2", "com.sirenix.odininspector": "3.1.14", "com.unity.collections": "2.1.4", "com.unity.burst": "1.8.8"}}, "com.stratkit.user-input-actions": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.user-input-actions", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.core": "1.3.1", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.service-injector": "1.0.1", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.inputsystem": "1.11.2", "com.unity.mathematics": "1.3.2", "com.unity.physics": "1.3.2"}}, "com.stratkit.user-inventory-utils": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.user-inventory-utils", "depth": 0, "source": "local", "dependencies": {"com.stratkit.army-air": "1.23.0", "com.stratkit.army-command": "3.5.0", "com.stratkit.army-state-loader": "5.17.1", "com.stratkit.buildings": "4.0.0", "com.stratkit.contentitemsmodel": "1.1.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.foreign-affairs-state-loader": "2.5.0", "com.stratkit.moddable-upgrade-state-loader": "2.0.0", "com.stratkit.mods": "3.8.0", "com.stratkit.properties-commons": "1.7.0", "com.stratkit.properties-loader": "1.6.1", "com.stratkit.provinces": "1.17.1", "com.stratkit.units": "3.24.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.vfx": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.vfx", "depth": 0, "source": "local", "dependencies": {"com.rukhanka.animation": "1.8.0-StratkitFix1", "com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.3.0", "com.stratkit.addressables-utils": "0.4.0", "com.stratkit.army-command": "3.17.2", "com.stratkit.army-entity-graphics": "3.13.0", "com.stratkit.collections": "2.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.17.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.map": "6.1.0", "com.stratkit.properties-commons": "1.20.0", "com.stratkit.properties-loader": "1.18.2", "com.stratkit.render-core": "1.0.0", "com.stratkit.sectionstreaming": "5.8.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.tracking": "1.4.2", "com.stratkit.zoom": "4.0.0", "com.unity.addressables": "2.3.16", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.entities.graphics": "1.3.2"}}, "com.stratkit.victory-rewards": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.victory-rewards", "depth": 0, "source": "local", "dependencies": {}}, "com.stratkit.webapi": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.webapi", "depth": 0, "source": "local", "dependencies": {"com.stillfront.addressablesextensions": "2.1.1", "com.stillfront.buildpipeline": "3.1.0", "com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.buildings": "4.0.1", "com.stratkit.contentitemsmodel": "3.4.0", "com.stratkit.core": "2.0.0", "com.stratkit.core.deeplinks": "2.2.0", "com.stratkit.csv": "1.9.0", "com.stratkit.data": "3.12.1", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.factions": "1.5.0", "com.stratkit.localization": "1.1.0", "com.stratkit.mods": "3.13.0", "com.stratkit.offers": "1.1.0", "com.stratkit.premiums": "1.7.0", "com.stratkit.properties-commons": "1.13.0", "com.stratkit.properties-loader": "1.11.0", "com.stratkit.reflection-utils": "0.1.1", "com.stratkit.roslyn-analyzers": "1.6.0", "com.stratkit.server-communication": "5.9.0", "com.stratkit.service-injector": "1.0.1", "com.stratkit.ui": "2.11.0", "com.stratkit.ui-common": "6.18.0", "com.stratkit.units": "4.3.0", "com.stratkit.worldreference": "2.1.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.5", "com.unity.ugui": "2.0.0", "io.jahro.console": "3.0.5"}}, "com.stratkit.webapi.data": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.webapi.data", "depth": 0, "source": "local", "dependencies": {"com.stratkit.entities-reactive": "1.3.0", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8"}}, "com.stratkit.worldreference": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.worldreference", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stratkit.core": "1.2.0", "com.stratkit.entities-core": "2.15.0", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.5"}}, "com.stratkit.zoom": {"version": "file:../../stratkit/stratkit/Packages/com.stratkit.zoom", "depth": 0, "source": "local", "dependencies": {"com.stillfront.logging": "3.1.4", "com.stillfront.validators": "3.2.0", "com.stratkit.core": "2.0.0", "com.stratkit.entities-core": "2.16.0", "com.stratkit.entities-hybridbaker": "0.4.0", "com.stratkit.entities-reactive": "1.4.0", "com.stratkit.rts-camera": "2.3.1", "com.stratkit.service-injector": "1.0.1", "com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.entities": "1.3.8", "com.unity.mathematics": "1.3.2"}}, "com.thefallengames.osa": {"version": "0.1.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@bytro"}, "com.thirdparty.ziplib": {"version": "1.4.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://npm.pkg.github.com/@bytro"}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.addressables": {"version": "2.3.16", "depth": 1, "source": "registry", "dependencies": {"com.unity.profiling.core": "1.0.2", "com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "2.2.11", "com.unity.modules.unitywebrequestassetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ads.ios-support": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.18", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.1", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.8.17", "com.unity.test-framework": "1.4.5", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.device-simulator.devices": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.entities": {"version": "1.3.8", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.8.18", "com.unity.collections": "2.5.1", "com.unity.mathematics": "1.3.2", "com.unity.modules.audio": "1.0.0", "com.unity.serialization": "3.1.1", "com.unity.profiling.core": "1.0.2", "com.unity.modules.physics": "1.0.0", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "1.21.21", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.entities.graphics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.entities": "1.3.2", "com.unity.render-pipelines.core": "14.0.9", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.formats.fbx": {"version": "5.1.1", "depth": 1, "source": "registry", "dependencies": {"com.autodesk.fbx": "5.1.1", "com.unity.timeline": "1.7.1"}, "url": "https://packages.unity.com"}, "com.unity.ide.rider": {"version": "3.0.34", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.22", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.inputsystem": {"version": "1.11.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.localization": {"version": "1.5.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.addressables": "1.21.9", "com.unity.nuget.newtonsoft-json": "3.0.2"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mobile.android-logcat": {"version": "1.4.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.mono-cecil": {"version": "1.11.4", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.physics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.8.17", "com.unity.entities": "1.3.2", "com.unity.collections": "2.5.1", "com.unity.mathematics": "1.3.2", "com.unity.modules.imgui": "1.0.0", "com.unity.test-framework": "1.4.5", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.project-auditor": {"version": "1.0.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.nuget.mono-cecil": "1.10.1", "com.unity.nuget.newtonsoft-json": "3.2.1"}, "url": "https://packages.unity.com"}, "com.unity.purchasing": {"version": "4.13.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.12.5", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "14.0.11", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3", "com.unity.shadergraph": "17.0.3", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "2.2.11", "depth": 2, "source": "registry", "dependencies": {"com.unity.test-framework": "1.4.5", "com.unity.modules.assetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.2", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.serialization": {"version": "3.1.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.7.2", "com.unity.collections": "2.4.2"}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.14.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.0.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3", "com.unity.searcher": "4.9.2"}}, "com.unity.sharp-zip-lib": {"version": "1.3.8", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.splines": {"version": "2.7.2", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.4.5", "depth": 1, "source": "registry", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.test-framework.performance": {"version": "3.0.3", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.31", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.testtools.codecoverage": {"version": "1.2.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.0.16", "com.unity.settings-manager": "1.0.1"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "3.0.9", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ugui": "2.0.0"}}, "com.unity.timeline": {"version": "1.7.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.vectorgraphics": {"version": "2.0.0-preview.24", "depth": 1, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.2d.sprite": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0"}, "url": "https://packages.unity.com"}, "com.yasirkula.nativeshare": {"version": "https://github.com/yasirkula/UnityNativeShare.git#v1.5.3", "depth": 0, "source": "git", "dependencies": {}, "hash": "99559c82d9ef7fa2e2c588b91b1995f117f754b2"}, "io.jahro.console": {"version": "file:../../stratkit/stratkit/Packages/io.jahro.console", "depth": 0, "source": "local", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 2, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 2, "source": "builtin", "dependencies": {}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}}}